'use client';

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { User, Package, MessageSquare, CreditCard, Heart, LogOut } from 'lucide-react';

interface AccountLayoutProps {
  children: ReactNode;
}

export default function AccountLayout({ children }: AccountLayoutProps) {
  const pathname = usePathname();
  const { customer, logout } = useAuth();

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">My Account</h1>
      
      {customer && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-8">
          <p className="text-blue-800">
            Welcome back, <span className="font-semibold">{customer.firstName} {customer.lastName}</span>!
          </p>
        </div>
      )}
      
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar Navigation */}
        <div className="w-full md:w-1/4">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <nav>
              <Link
                href="/account"
                className={`flex items-center px-4 py-3 border-l-4 ${
                  isActive('/account')
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent hover:bg-gray-50'
                }`}
              >
                <User size={18} className="mr-3" />
                <span>Profile</span>
              </Link>
              
              <Link
                href="/account/orders"
                className={`flex items-center px-4 py-3 border-l-4 ${
                  isActive('/account/orders')
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent hover:bg-gray-50'
                }`}
              >
                <Package size={18} className="mr-3" />
                <span>Orders</span>
              </Link>
              
              <Link
                href="/account/tickets"
                className={`flex items-center px-4 py-3 border-l-4 ${
                  isActive('/account/tickets') || pathname?.startsWith('/account/tickets/')
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent hover:bg-gray-50'
                }`}
              >
                <MessageSquare size={18} className="mr-3" />
                <span>Support Tickets</span>
              </Link>
              
              <Link
                href="/account/payment-methods"
                className={`flex items-center px-4 py-3 border-l-4 ${
                  isActive('/account/payment-methods')
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent hover:bg-gray-50'
                }`}
              >
                <CreditCard size={18} className="mr-3" />
                <span>Payment Methods</span>
              </Link>
              
              <Link
                href="/account/wishlist"
                className={`flex items-center px-4 py-3 border-l-4 ${
                  isActive('/account/wishlist')
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent hover:bg-gray-50'
                }`}
              >
                <Heart size={18} className="mr-3" />
                <span>Wishlist</span>
              </Link>
              
              <button
                onClick={() => logout()}
                className="w-full flex items-center px-4 py-3 border-l-4 border-transparent text-left hover:bg-gray-50 text-red-600"
              >
                <LogOut size={18} className="mr-3" />
                <span>Logout</span>
              </button>
            </nav>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="w-full md:w-3/4">
          {children}
        </div>
      </div>
    </div>
  );
} 