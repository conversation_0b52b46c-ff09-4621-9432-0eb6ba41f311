'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface Permission {
  resource: string;
  actions: string[];
}

interface Role {
  _id: string;
  name: string;
  permissions: Permission[];
}

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: Role;
  isActive: boolean;
  lastLogin?: Date;
}

interface AdminAuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  hasPermission: (resource: string, action: string) => boolean;
  forgotPassword: (email: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
}

const AdminAuthContext = createContext<AdminAuthContextType | null>(null);

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

export const AdminAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if admin is already logged in by retrieving user info from localStorage
    const checkAuthStatus = async () => {
      setIsLoading(true);
      
      try {
        const storedUser = localStorage.getItem('admin_user');
        
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          
          // Verify the stored user with the backend
          const response = await fetch('/api/admin/auth/verify', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: parsedUser._id }),
          });
          
          if (response.ok) {
            setUser(parsedUser);
          } else {
            // If verification fails, clear localStorage
            localStorage.removeItem('admin_user');
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error checking admin auth status:', error);
        localStorage.removeItem('admin_user');
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        return false;
      }
      
      const data = await response.json();
      setUser(data.user);
      
      // Store user info in localStorage
      localStorage.setItem('admin_user', JSON.stringify(data.user));
      
      return true;
    } catch (error) {
      console.error('Admin login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const logout = async (): Promise<void> => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      
      // Clear localStorage and state
      localStorage.removeItem('admin_user');
      setUser(null);
    } catch (error) {
      console.error('Admin logout error:', error);
    }
  };

  // Function to check if user has a specific permission
  const hasPermission = (resource: string, action: string): boolean => {
    if (!user || !user.role || !user.role.permissions) return false;
    
    const permission = user.role.permissions.find(p => p.resource === resource);
    return permission ? permission.actions.includes(action) : false;
  };

  // Function to handle forgot password request
  const forgotPassword = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      
      return response.ok;
    } catch (error) {
      console.error('Forgot password error:', error);
      return false;
    }
  };
  
  // Function to reset password with token
  const resetPassword = async (token: string, newPassword: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/auth/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token, newPassword }),
      });
      
      return response.ok;
    } catch (error) {
      console.error('Reset password error:', error);
      return false;
    }
  };

  return (
    <AdminAuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        logout,
        hasPermission,
        forgotPassword,
        resetPassword,
      }}
    >
      {children}
    </AdminAuthContext.Provider>
  );
};

export default AdminAuthContext; 