import { NextRequest, NextResponse } from 'next/server';

const PAYPAL_API_URL = 'https://api-m.sandbox.paypal.com';

// Get PayPal access token (same as in create-paypal-order)
async function getAccessToken() {
  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET || 'sandbox_secret'; // Replace with your secret
  
  const response = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
    },
    body: 'grant_type=client_credentials'
  });
  
  const data = await response.json();
  return data.access_token;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { orderID } = body;
    
    console.log('Capturing PayPal payment for order:', orderID);
    
    if (!orderID) {
      return NextResponse.json(
        { error: 'Order ID is required.' },
        { status: 400 }
      );
    }
    
    // Get access token
    const accessToken = await getAccessToken();
    
    // Capture the payment
    const response = await fetch(`${PAYPAL_API_URL}/v2/checkout/orders/${orderID}/capture`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    const captureData = await response.json();
    
    if (response.ok) {
      console.log('Payment captured successfully:', captureData.id);
      return NextResponse.json({ 
        id: captureData.id,
        status: captureData.status,
        payer: captureData.payer
      });
    } else {
      console.error('Error capturing payment:', captureData);
      throw new Error(captureData.message || 'Failed to capture payment');
    }
  } catch (error: any) {
    console.error('PayPal capture error:', error);
    
    return NextResponse.json(
      { 
        error: 'An error occurred capturing the payment.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
} 