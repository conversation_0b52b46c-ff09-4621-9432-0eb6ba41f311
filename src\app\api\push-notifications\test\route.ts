import { NextRequest, NextResponse } from 'next/server';
import admin from 'firebase-admin';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  try {
    // Make sure to trim any whitespace from credentials
    const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY
      ? process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n').trim()
      : undefined;

    const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID?.trim();
    const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL?.trim();

    console.log('Firebase Admin SDK initializing with project ID:', projectId);
    
    if (!projectId || !clientEmail || !privateKey) {
      throw new Error(
        `Missing Firebase Admin credentials: 
        ProjectID: ${projectId ? 'OK' : 'MISSING'}, 
        ClientEmail: ${clientEmail ? 'OK' : 'MISSING'}, 
        PrivateKey: ${privateKey ? 'OK' : 'MISSING'}`
      );
    }

    admin.initializeApp({
      credential: admin.credential.cert({
        projectId,
        clientEmail,
        privateKey,
      }),
    });
    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { token, title, body } = await request.json();
    
    if (!token) {
      return NextResponse.json({ success: false, error: 'FCM token is required' }, { status: 400 });
    }
    
    console.log('Sending test message to FCM token:', token.substring(0, 10) + '...' + token.substring(token.length - 10));
    
    // Verify Firebase Admin is initialized
    if (!admin.apps.length) {
      return NextResponse.json(
        { success: false, error: 'Firebase Admin SDK is not initialized' },
        { status: 500 }
      );
    }
    
    // Send a test message
    const message = {
      notification: {
        title: title || 'Admin Panel Test Notification',
        body: body || 'This is a test notification sent from the admin panel.',
      },
      token: token,
    };
    
    try {
      const response = await admin.messaging().send(message);
      console.log('Successfully sent test message:', response);
      
      return NextResponse.json({ 
        success: true, 
        message: 'Test notification sent successfully',
        messageId: response 
      });
    } catch (firebaseError: any) {
      console.error('Firebase messaging error:', firebaseError);
      
      // More detailed error info
      const errorInfo = {
        code: firebaseError.code,
        message: firebaseError.message,
        stack: firebaseError.stack,
      };
      
      // Check for specific Firebase error cases
      if (firebaseError.code === 'messaging/registration-token-not-registered') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'The FCM token is not registered. The client may need to re-register.',
            details: errorInfo
          },
          { status: 400 }
        );
      } else if (firebaseError.code === 'messaging/mismatched-credential') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'The FCM token was issued for a different Firebase project. Check your configuration.',
            details: errorInfo
          },
          { status: 400 }
        );
      }
      
      // Generic error response
      return NextResponse.json(
        { 
          success: false, 
          error: `Firebase error: ${firebaseError.message}`,
          details: errorInfo
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test notification:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { success: false, error: `Failed to send test notification: ${errorMessage}` },
      { status: 500 }
    );
  }
} 