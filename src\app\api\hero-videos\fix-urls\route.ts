import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';

/**
 * Fix existing chunked video URLs to use proper HLS proxy format
 * 
 * POST /api/hero-videos/fix-urls
 */
export async function POST() {
  try {
    await connectToDatabase();
    
    // Find all videos that have chunked URLs but are using old format
    const videosToFix = await HeroVideo.find({
      isChunked: true,
      chunkingStatus: 'completed',
      chunkedVideoUrl: { 
        $exists: true, 
        $not: /^\/api\/hls-proxy\// // Not already using proxy format
      }
    });

    console.log(`Found ${videosToFix.length} videos with URLs to fix`);

    let fixedCount = 0;
    const fixResults = [];

    for (const video of videosToFix) {
      const oldUrl = video.chunkedVideoUrl;
      
      // Extract S3 key from old URL format
      let s3Key = '';
      
      if (oldUrl.startsWith('videosbucket2025/')) {
        // Format: videosbucket2025/chunked-videos/...
        s3Key = oldUrl.replace('videosbucket2025/', '');
      } else if (oldUrl.includes('chunked-videos/')) {
        // Format: chunked-videos/...
        const parts = oldUrl.split('chunked-videos/');
        s3Key = 'chunked-videos/' + parts[parts.length - 1];
      } else {
        // Unknown format, skip
        console.warn(`Unknown URL format for video ${video._id}: ${oldUrl}`);
        continue;
      }

      // Update to use HLS proxy format
      const newUrl = `/api/hls-proxy/${s3Key}`;
      
      video.chunkedVideoUrl = newUrl;
      await video.save();
      
      fixedCount++;
      fixResults.push({
        videoId: video._id,
        type: video.type,
        oldUrl,
        newUrl
      });
      
      console.log(`✅ Fixed URL for ${video.type} video ${video._id}`);
      console.log(`   Old: ${oldUrl}`);
      console.log(`   New: ${newUrl}`);
    }

    return NextResponse.json({
      success: true,
      message: `Fixed ${fixedCount} video URLs`,
      totalFound: videosToFix.length,
      fixedCount,
      results: fixResults
    });

  } catch (error: any) {
    console.error('Error fixing video URLs:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fix video URLs'
    }, { status: 500 });
  }
}

/**
 * Get status of URLs that need fixing
 * 
 * GET /api/hero-videos/fix-urls
 */
export async function GET() {
  try {
    await connectToDatabase();
    
    // Count videos with old URL format
    const videosNeedingFix = await HeroVideo.countDocuments({
      isChunked: true,
      chunkingStatus: 'completed',
      chunkedVideoUrl: { 
        $exists: true, 
        $not: /^\/api\/hls-proxy\// 
      }
    });

    // Count videos with new URL format
    const videosAlreadyFixed = await HeroVideo.countDocuments({
      isChunked: true,
      chunkingStatus: 'completed',
      chunkedVideoUrl: /^\/api\/hls-proxy\//
    });

    return NextResponse.json({
      success: true,
      videosNeedingFix,
      videosAlreadyFixed,
      totalChunkedVideos: videosNeedingFix + videosAlreadyFixed
    });

  } catch (error: any) {
    console.error('Error checking video URLs:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to check video URLs'
    }, { status: 500 });
  }
} 