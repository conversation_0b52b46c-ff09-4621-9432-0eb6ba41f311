import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { TaxRule } from '@/models/TaxRule';

// GET /api/admin/settings/tax-rules - Get all tax rules
export async function GET(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get all tax rules
    const taxRules = await TaxRule.find().sort({ isDefault: -1, createdAt: -1 });
    
    return NextResponse.json({ taxRules });
  } catch (error) {
    console.error('Error fetching tax rules:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tax rules' },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings/tax-rules - Create a new tax rule
export async function POST(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Parse request body
    const taxRuleData = await req.json();
    
    // Validate required fields
    if (!taxRuleData.name || taxRuleData.rate === undefined || !taxRuleData.country) {
      return NextResponse.json(
        { error: 'Name, rate, and country are required fields' },
        { status: 400 }
      );
    }

    // If this is the first tax rule, make it the default
    const count = await TaxRule.countDocuments();
    if (count === 0) {
      taxRuleData.isDefault = true;
    } else if (taxRuleData.isDefault) {
      // If this rule is set as default, unset any existing default
      await TaxRule.updateMany(
        { isDefault: true },
        { isDefault: false }
      );
    }
    
    // Create new tax rule
    const newTaxRule = await TaxRule.create(taxRuleData);
    
    return NextResponse.json(newTaxRule, { status: 201 });
  } catch (error) {
    console.error('Error creating tax rule:', error);
    return NextResponse.json(
      { error: 'Failed to create tax rule' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings/tax-rules/:id - Update a tax rule
export async function PUT(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get tax rule ID from URL
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Tax rule ID is required' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const updateData = await req.json();
    
    // Check if rule exists
    const taxRule = await TaxRule.findById(id);
    if (!taxRule) {
      return NextResponse.json(
        { error: 'Tax rule not found' },
        { status: 404 }
      );
    }
    
    // If setting as default, unset any existing default
    if (updateData.isDefault && !taxRule.isDefault) {
      await TaxRule.updateMany(
        { _id: { $ne: id }, isDefault: true },
        { isDefault: false }
      );
    }
    
    // Update and return the tax rule
    const updatedTaxRule = await TaxRule.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );
    
    return NextResponse.json(updatedTaxRule);
  } catch (error) {
    console.error('Error updating tax rule:', error);
    return NextResponse.json(
      { error: 'Failed to update tax rule' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/settings/tax-rules/:id - Delete a tax rule
export async function DELETE(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get tax rule ID from URL
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Tax rule ID is required' },
        { status: 400 }
      );
    }
    
    // Check if rule exists
    const taxRule = await TaxRule.findById(id);
    if (!taxRule) {
      return NextResponse.json(
        { error: 'Tax rule not found' },
        { status: 404 }
      );
    }
    
    // If this is the default rule, don't allow deletion
    if (taxRule.isDefault) {
      return NextResponse.json(
        { error: 'Cannot delete the default tax rule. Make another rule default first.' },
        { status: 400 }
      );
    }
    
    // Delete the tax rule
    await TaxRule.findByIdAndDelete(id);
    
    return NextResponse.json(
      { message: 'Tax rule deleted successfully' }
    );
  } catch (error) {
    console.error('Error deleting tax rule:', error);
    return NextResponse.json(
      { error: 'Failed to delete tax rule' },
      { status: 500 }
    );
  }
} 