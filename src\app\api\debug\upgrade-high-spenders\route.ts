import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function GET() {
  try {
    await dbConnect();
    
    // Find customers with total spent
    const customers = await Customer.find({ totalSpent: { $exists: true, $gt: 0 } });
    const results = [];
    
    for (const customer of customers) {
      // Skip customers with spending less than $2000
      if (!customer.totalSpent || customer.totalSpent < 2000) {
        continue;
      }
      
      // Initialize rewards array if needed
      if (!customer.currentRewards) {
        customer.currentRewards = [];
      }
      
      // Calculate what multiple of 2000 the customer has reached
      const multiplier = Math.floor(customer.totalSpent / 2000);
      const totalRewardAmount = multiplier * 100;
      
      // Calculate existing reward amount
      let currentRewardAmount = 0;
      if (customer.currentRewards && customer.currentRewards.length > 0) {
        const existingRewardMatch = customer.currentRewards[0].name.match(/\$(\d+)/);
        if (existingRewardMatch && existingRewardMatch[1]) {
          currentRewardAmount = parseInt(existingRewardMatch[1], 10);
        }
      }
      
      // If they already have the correct reward amount, skip
      if (currentRewardAmount === totalRewardAmount) {
        continue;
      }
      
      // Update the customer with the initial reward (for customers with no rewards)
      if (currentRewardAmount === 0) {
        customer.currentRewards = [{
          id: Date.now(),
          name: `$${totalRewardAmount} Reward`,
          issuedDate: new Date().toISOString().split('T')[0]
        }];
        
        await customer.save();
        
        results.push({
          email: customer.email,
          totalSpent: customer.totalSpent,
          initialReward: `$${totalRewardAmount} Reward`,
          success: true
        });
      }
      // Leave customers with rewards for admins to add more rewards via UI
    }
    
    return NextResponse.json({
      success: true,
      message: `Initialized rewards for ${results.length} customers with no existing rewards`,
      results,
      note: "Customers with existing rewards need manual upgrades through the UI"
    });
  } catch (error: any) {
    console.error('Error initializing rewards:', error);
    return NextResponse.json(
      { 
        error: 'Failed to initialize rewards',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
} 