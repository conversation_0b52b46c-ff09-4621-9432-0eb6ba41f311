import mongoose, { Schema, Document } from 'mongoose';

export interface IHeroVideo extends Document {
  videoUrl: string;
  type: 'mobile' | 'desktop';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string; // Admin user ID who created/uploaded this
  fileName: string;
  fileSize: number;
  fileType: string;
  displayOrder?: number;
  // Chunking-related fields
  isChunked?: boolean;
  chunkingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  chunkingProgress?: number; // 0-100
  chunkedVideoUrl?: string; // HLS manifest URL
  mediaConvertJobId?: string;
  chunkingError?: string;
}

const HeroVideoSchema: Schema = new Schema(
  {
    videoUrl: { 
      type: String, 
      required: true 
    },
    type: { 
      type: String, 
      enum: ['mobile', 'desktop'], 
      required: true 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    createdBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User',
      required: false
    },
    fileName: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    fileType: {
      type: String,
      required: true
    },
    displayOrder: {
      type: Number,
      default: 0
    },
    // Chunking-related fields
    isChunked: {
      type: Boolean,
      default: false
    },
    chunkingStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    chunkingProgress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    chunkedVideoUrl: {
      type: String,
      required: false
    },
    mediaConvertJobId: {
      type: String,
      required: false
    },
    chunkingError: {
      type: String,
      required: false
    }
  },
  {
    timestamps: true
  }
);

// Add a pre-save hook to ensure there's only one active video per type
HeroVideoSchema.pre('save', async function(next) {
  if (this.isActive) {
    // If this video is being set to active, deactivate all other videos of the same type
    await mongoose.models.HeroVideo.updateMany(
      { 
        type: this.type,
        _id: { $ne: this._id } // Exclude the current document
      },
      { isActive: false }
    );
  }
  next();
});

export default mongoose.models.HeroVideo || mongoose.model<IHeroVideo>('HeroVideo', HeroVideoSchema); 