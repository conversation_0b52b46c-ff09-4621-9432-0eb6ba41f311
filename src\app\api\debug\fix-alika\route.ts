import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function GET() {
  try {
    await dbConnect();
    
    // Find Alika Oneill specifically by email
    const customer = await Customer.findOne({ email: "<EMAIL>" });
    
    if (!customer) {
      return NextResponse.json({
        success: false,
        message: "Alika Oneill not found"
      });
    }
    
    // Calculate what multiple of 2000 the customer has reached
    const totalSpent = customer.totalSpent;
    const multiplier = Math.floor(totalSpent / 2000);
    const totalRewardAmount = multiplier * 100;
    
    // Get current reward amount
    let currentRewardAmount = 0;
    if (customer.currentRewards && customer.currentRewards.length > 0) {
      const match = customer.currentRewards[0].name.match(/\$(\d+)/);
      if (match && match[1]) {
        currentRewardAmount = parseInt(match[1], 10);
      }
    }
    
    // Reset customer's reward to starting point for debugging
    customer.currentRewards = [{
      id: Date.now(),
      name: "$200 Reward",
      issuedDate: new Date().toISOString().split('T')[0]
    }];
    
    await customer.save();
    
    return NextResponse.json({
      success: true,
      customer: {
        name: `${customer.firstName} ${customer.lastName}`,
        email: customer.email,
        totalSpent: totalSpent,
        multiplier: multiplier,
        shouldHaveReward: `$${totalRewardAmount} Reward`,
        currentReward: "$200 Reward",
        eligibleForAdditional: `$${totalRewardAmount - 200} Reward`
      }
    });
  } catch (error: any) {
    console.error('Error fixing Alika Oneill:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fix Alika Oneill',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
} 