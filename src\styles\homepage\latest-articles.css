.latest-articles-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .latest-articles-section {
        padding-top: 3rem;
        padding-bottom: 3rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .latest-articles-section {
        padding-top: 4rem;
        padding-bottom: 4rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.latest-articles-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
}

.latest-articles-heading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
}

@media (min-width: 640px) {
    .latest-articles-heading-container {
        margin-bottom: 3rem;
    }
}

.latest-articles-heading {
    font-family: var(--font-dosis), sans-serif;
    font-size: 25px;
    font-weight: 500;
    line-height: 36px;
    letter-spacing: 1.25px;
    color: black;
    text-align: center;
    margin-bottom: 0.75rem;
    text-transform: capitalize;
}

@media (min-width: 640px) {
    .latest-articles-heading {
        /* Keep the same font properties, no size changes */
    }
}



.latest-articles-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2.5rem;
}

@media (min-width: 640px) {
    .latest-articles-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .latest-articles-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

.latest-articles-card {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
    height: 100%;
}

.latest-articles-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.latest-articles-image-container {
    position: relative;
}

.latest-articles-image-wrapper {
    width: 100%;
    aspect-ratio: 16/9;
}

@media (min-width: 640px) {
    .latest-articles-image-wrapper {
        aspect-ratio: 3/2;
    }
}

.latest-articles-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.latest-articles-card:hover .latest-articles-image {
    transform: scale(1.05);
}

.latest-articles-content {
    padding: 0.75rem;
}

@media (min-width: 640px) {
    .latest-articles-content {
        padding: 1.5rem;
    }
}

.latest-articles-title {
    font-family: var(--font-dosis), sans-serif;
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    letter-spacing: 1.7px;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: 640px) {
    .latest-articles-title {
        margin-bottom: 1rem;
        -webkit-line-clamp: unset;
    }
}

@media (min-width: 768px) {
    .latest-articles-title {
        font-size: 15px;
        line-height: 22px;
    }
}

@media (min-width: 1024px) {
    .latest-articles-title {
        font-size: 16px;
        line-height: 23px;
    }
}

.latest-articles-description {
    font-family: var(--font-dosis), sans-serif;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0.07em;
    color: #374151;
    text-align: center;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: 640px) {
    .latest-articles-description {
        margin-bottom: 1rem;
        -webkit-line-clamp: unset;
    }
}

@media (min-width: 768px) {
    .latest-articles-description {
        font-size: 13px;
        line-height: 19px;
    }
}

@media (min-width: 1024px) {
    .latest-articles-description {
        font-size: 14px;
        line-height: 20px;
    }
}

.latest-articles-read-more {
    font-family: var(--font-dosis), sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0.02em;
    color: #D3821F;
    display: block;
    text-align: center;
}

.latest-articles-read-more:hover {
    text-decoration: underline;
}

@media (min-width: 640px) {
    .latest-articles-read-more {
        /* Keep the same font properties, no size changes */
    }
}

.latest-articles-button-container {
    display: flex;
    justify-content: center;
}

.latest-articles-button {
    padding-left: 32px;
    padding-right: 2rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border-radius: 9999px;
    background: linear-gradient(to top left, #51575F, #1F2937);
    color: white;
    font-family: var(--font-segoe-ui), sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 16.5px;
    letter-spacing: 1.83px;
    /* text-transform: uppercase; */
    transition: all 0.3s;
}

.latest-articles-button:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to top left, #6B7280, #374151);
}

.latest-articles-button:active {
    transform: scale(0.95);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    background: linear-gradient(to top left, #4B5563, #111827);
} 

/* Mobile and Tablet: Show only first 2 articles */
@media (max-width: 1023px) {
  .latest-articles-card:nth-child(3) {
    display: none;
  }
}

/* Laptop and Desktop: Show all 3 articles */
@media (min-width: 1024px) {
  .latest-articles-card:nth-child(3) {
    display: block;
  }
} 