import { NextRequest, NextResponse } from 'next/server';

const PAYPAL_API_URL = 'https://api-m.sandbox.paypal.com';

// Get PayPal access token
async function getAccessToken() {
  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET || 'sandbox_secret'; // Replace with your secret
  
  const response = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
    },
    body: 'grant_type=client_credentials'
  });
  
  const data = await response.json();
  return data.access_token;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { items } = body;
    
    console.log('Received PayPal order request with items:', JSON.stringify(items));
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Items are required.' },
        { status: 400 }
      );
    }
    
    // Calculate total
    const value = items.reduce(
      (total, item) => total + (item.product.price * item.quantity),
      0
    ).toFixed(2);
    
    // Get access token
    const accessToken = await getAccessToken();
    
    // Create PayPal order
    const response = await fetch(`${PAYPAL_API_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: value,
              breakdown: {
                item_total: {
                  currency_code: 'USD',
                  value: value
                }
              }
            },
            items: items.map(item => ({
              name: item.product.name,
              description: item.product.description?.substring(0, 127) || '',
              quantity: item.quantity.toString(),
              unit_amount: {
                currency_code: 'USD',
                value: item.product.price.toFixed(2)
              }
            }))
          }
        ],
        application_context: {
          shipping_preference: 'GET_FROM_FILE',
          return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/success`,
          cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout?canceled=true`
        }
      })
    });
    
    const order = await response.json();
    
    if (response.ok) {
      console.log('PayPal order created successfully:', order.id);
      return NextResponse.json({ id: order.id });
    } else {
      console.error('Error creating PayPal order:', order);
      throw new Error(order.message || 'Failed to create PayPal order');
    }
  } catch (error: any) {
    console.error('PayPal order error:', error);
    
    return NextResponse.json(
      { 
        error: 'An error occurred creating the PayPal order.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
} 