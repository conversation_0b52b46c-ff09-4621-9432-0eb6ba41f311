'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CheckCircle } from 'lucide-react';
import { useCartStore } from '@/store/useCartStore';
import { useLanguage } from '@/contexts/LanguageContext';
import { markCartAsRecovered } from '@/services/cartSyncService';

// Component that uses useSearchParams
function SuccessContent() {
  const [orderProcessed, setOrderProcessed] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const { items, clearCart } = useCartStore();
  const { translations } = useLanguage();
  
  // Process the order and clear cart on successful payment
  useEffect(() => {
    if (sessionId && items.length > 0 && !orderProcessed) {
      const storeOrder = async () => {
        try {
          // Prepare order data
          const orderData = {
            customer: {
              name: 'Customer', // Would normally come from form or user profile
              email: '<EMAIL>', // Would normally come from form or user profile
            },
            items: items.map(item => ({
              productId: item.product._id,
              name: item.product.name,
              price: item.product.price,
              quantity: item.quantity,
              imageUrl: item.product.imageUrl,
            })),
            subtotal: items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0),
            shipping: 15.00, // Fixed shipping cost
            tax: items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0) * 0.05, // 5% tax
            total: items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0) + 15.00 + 
                  (items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0) * 0.05),
            paymentStatus: 'paid',
            paymentMethod: 'credit_card',
            shippingAddress: {
              street: '123 Main St', // Would normally come from checkout form
              city: 'Anytown', // Would normally come from checkout form
              state: 'State', // Would normally come from checkout form
              postalCode: '12345', // Would normally come from checkout form
              country: 'US', // Would normally come from checkout form
            },
          };
          
          // Post to orders API
          const response = await fetch('/api/orders', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(orderData),
          });
          
          const result = await response.json();
          
          if (!response.ok) {
            throw new Error(result.message || 'Failed to store order');
          }
          
          // Mark the abandoned cart as recovered
          await markCartAsRecovered(sessionId);
          
          // Clear cart using Zustand
          clearCart();
          setOrderProcessed(true);
        } catch (err) {
          console.error('Error storing order:', err);
          setError(err instanceof Error ? err.message : 'Failed to store order');
        }
      };
      
      storeOrder();
    } else if (sessionId && items.length === 0 && !orderProcessed) {
      // If we have a session ID but no items, mark as processed
      // This can happen if the page refreshes after checkout
      
      // Still try to mark the cart as recovered
      (async () => {
        try {
          await markCartAsRecovered(sessionId);
        } catch (err) {
          console.error('Error marking cart as recovered:', err);
        }
      })();
      
      setOrderProcessed(true);
    }
  }, [sessionId, items, clearCart, orderProcessed]);
  
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 py-16">
        <div className="text-center">
          <div className="inline-flex items-center justify-center h-24 w-24 rounded-full bg-green-100 mb-6">
            <CheckCircle size={48} className="text-green-600" />
          </div>
          
          <h1 className="text-3xl font-bold mb-4 text-gray-900">{translations.payment_successful}</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            {translations.thank_you_message}
          </p>
          
          {sessionId && (
            <div className="mb-8 p-4 bg-gray-100 rounded-md inline-block">
              <p className="text-sm font-medium text-gray-800">{translations.order_reference_label}</p>
              <p className="text-sm text-gray-600 font-mono">{sessionId}</p>
            </div>
          )}
          
          {error && (
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">
                {error}
              </p>
              <p className="text-sm text-red-600 mt-1">
                {translations.contact_support}
              </p>
            </div>
          )}
          
          <p className="text-gray-600 mb-8">
            {translations.confirmation_email}
          </p>
          
          <Link 
            href="/"
            className="inline-block px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            {translations.continue_shopping}
          </Link>
        </div>
      </div>
    </div>
  );
}

// Wrap with Suspense to handle useSearchParams() which requires client components
export default function SuccessPage() {
  const { translations } = useLanguage();
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">{translations.loading}</div>}>
      <SuccessContent />
    </Suspense>
  );
} 