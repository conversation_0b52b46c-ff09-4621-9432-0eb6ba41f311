const mongoose = require('mongoose');
require('dotenv').config();

// Connect to database
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Failed to connect to MongoDB', err);
    process.exit(1);
  });

// Define schema
const PermissionSchema = new mongoose.Schema({
  resource: { type: String, required: true },
  actions: [{ type: String }]
});

const RoleSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  description: { type: String },
  permissions: [PermissionSchema]
}, { timestamps: true });

const UserSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: mongoose.Schema.Types.ObjectId, ref: 'Role' },
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date }
}, { timestamps: true });

// Add password hashing
const bcrypt = require('bcryptjs');
UserSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 10);
  }
  next();
});

// Create models
const Role = mongoose.models.Role || mongoose.model('Role', RoleSchema);
const User = mongoose.models.User || mongoose.model('User', UserSchema);

// List of all resources and actions
const resources = [
  'dashboard', 'products', 'categories', 'orders', 'customers', 
  'inventory', 'reviews', 'analytics', 'returns', 'users'
];
const actions = ['view', 'create', 'update', 'delete'];

// Create Super Admin role with all permissions
async function createSuperAdminRole() {
  try {
    // Check if role already exists
    let superAdminRole = await Role.findOne({ name: 'Super Admin' });
    
    if (superAdminRole) {
      console.log('Super Admin role already exists');
      return superAdminRole;
    }
    
    // Create permissions for all resources and actions
    const permissions = resources.map(resource => ({
      resource,
      actions: [...actions]
    }));
    
    // Create the role
    superAdminRole = await Role.create({
      name: 'Super Admin',
      description: 'Full access to all system features',
      permissions
    });
    
    console.log('Super Admin role created successfully');
    return superAdminRole;
  } catch (error) {
    console.error('Error creating Super Admin role:', error);
    process.exit(1);
  }
}

// Create admin user
async function createAdminUser(role) {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create admin user
    await User.create({
      email: '<EMAIL>',
      password: 'admin123', // This will be hashed by the pre-save hook
      firstName: 'Admin',
      lastName: 'User',
      role: role._id,
      isActive: true
    });
    
    console.log('Admin user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

// Run the script
async function main() {
  try {
    const superAdminRole = await createSuperAdminRole();
    await createAdminUser(superAdminRole);
    console.log('Setup completed successfully');
  } catch (error) {
    console.error('Setup failed:', error);
  } finally {
    mongoose.disconnect();
  }
}

main(); 