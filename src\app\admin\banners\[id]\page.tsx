'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Calendar, LinkIcon, Edit, AlertTriangle } from 'lucide-react';
import { getImageUrl } from '@/lib/cloudfront';

interface Banner {
  _id: string;
  title: string;
  description: string;
  imageUrl: string;
  linkUrl: string;
  type: 'banner' | 'popup' | 'advertisement';
  position: string;
  status: 'active' | 'inactive' | 'scheduled';
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
}

export default function BannerDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const bannerId = params.id as string;
  const [banner, setBanner] = useState<Banner | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBanner();
  }, []);

  const fetchBanner = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/banners/${bannerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch banner details');
      }
      
      const data = await response.json();
      setBanner(data);
    } catch (err) {
      console.error('Error fetching banner:', err);
      setError('Failed to load banner details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Active</span>;
      case 'inactive':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Inactive</span>;
      case 'scheduled':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Scheduled</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">{status}</span>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'banner':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Banner</span>;
      case 'popup':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Popup</span>;
      case 'advertisement':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">Ad</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">{type}</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link
          href="/admin/banners"
          className="mr-4 p-2 rounded-md hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold">Banner Details</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md flex items-start">
          <AlertTriangle className="mr-2 h-5 w-5 flex-shrink-0" />
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center my-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : banner ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="relative aspect-[21/9] bg-gray-100">
            <img
              src={getImageUrl(banner.imageUrl)}
              alt={banner.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 right-4 flex space-x-2">
              {getStatusBadge(banner.status)}
              {isExpired(banner.endDate) && (
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                  Expired
                </span>
              )}
            </div>
          </div>

          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-xl font-bold mb-2">{banner.title}</h2>
                <div className="flex items-center space-x-2 mb-2">
                  {getTypeBadge(banner.type)}
                  <span className="text-sm text-gray-500 capitalize">Position: {banner.position}</span>
                </div>
              </div>
              
              <Link
                href={`/admin/banners/edit/${banner._id}`}
                className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 flex items-center"
              >
                <Edit size={16} className="mr-1" />
                Edit
              </Link>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-4">{banner.description || 'No description provided.'}</p>
              
              {banner.linkUrl && (
                <div className="flex items-center text-blue-600 mb-4">
                  <LinkIcon size={16} className="mr-2" />
                  <a href={banner.linkUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    {banner.linkUrl}
                  </a>
                </div>
              )}
              
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Calendar size={16} className="mr-1" />
                  <span>Start: {formatDate(banner.startDate)}</span>
                </div>
                <div className="flex items-center">
                  <Calendar size={16} className="mr-1" />
                  <span>End: {formatDate(banner.endDate)}</span>
                </div>
              </div>
            </div>

            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Additional Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Banner ID</p>
                  <p className="font-mono">{banner._id}</p>
                </div>
                <div>
                  <p className="text-gray-500">Created At</p>
                  <p>{formatDate(banner.createdAt)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Last Updated</p>
                  <p>{formatDate(banner.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center p-8 border rounded-lg bg-gray-50">
          <p className="text-gray-500">Banner not found</p>
        </div>
      )}
    </div>
  );
} 