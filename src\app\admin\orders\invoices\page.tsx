'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, Download, Printer, Eye } from 'lucide-react';
import Link from 'next/link';
import InvoiceView from '@/components/admin/InvoiceView';
import { Document, Page, PDFDownloadLink } from '@react-pdf/renderer';
import { InvoicePDF } from '@/components/admin/InvoicePDF';

type OrderStatus = 'all' | 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

type OrderItem = {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  imageUrl?: string;
};

type Order = {
  _id: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
  };
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: Exclude<OrderStatus, 'all'>;
  paymentStatus: 'paid' | 'unpaid' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
};

export default function OrderInvoicesPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<OrderStatus>('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showInvoice, setShowInvoice] = useState(false);
  
  // Fetch orders data
  useEffect(() => {
    fetchOrders();
  }, []);
  
  // Apply filters whenever search query or status filter changes
  useEffect(() => {
    applyFilters();
  }, [orders, searchQuery, statusFilter, startDate, endDate]);
  
  const fetchOrders = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query parameters for API request
      const params = new URLSearchParams();
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      
      if (searchQuery.trim()) {
        params.append('q', searchQuery.trim());
      }
      
      if (startDate) {
        params.append('startDate', startDate);
      }
      
      if (endDate) {
        params.append('endDate', endDate);
      }
      
      // Fetch orders from API
      const response = await fetch(`/api/orders?${params.toString()}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch orders');
      }
      
      setOrders(data.orders);
      setFilteredOrders(data.orders);
    } catch (err) {
      setError('Failed to fetch orders. Please try again later.');
      console.error('Error fetching orders:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const applyFilters = () => {
    // Local filtering for UI response
    let filtered = [...orders];
    
    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }
    
    // Filter by search query (order number or customer name/email)
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(order => 
        order.orderNumber.toLowerCase().includes(query) ||
        order.customer.name.toLowerCase().includes(query) ||
        order.customer.email.toLowerCase().includes(query)
      );
    }
    
    // Filter by date range
    if (startDate) {
      const startDateTime = new Date(startDate).getTime();
      filtered = filtered.filter(order => 
        new Date(order.createdAt).getTime() >= startDateTime
      );
    }
    
    if (endDate) {
      const endDateTime = new Date(endDate + 'T23:59:59').getTime();
      filtered = filtered.filter(order => 
        new Date(order.createdAt).getTime() <= endDateTime
      );
    }
    
    setFilteredOrders(filtered);
  };
  
  // Handle form submission for filters
  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchOrders();
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };
  
  const viewInvoice = (order: Order) => {
    setSelectedOrder(order);
    setShowInvoice(true);
  };
  
  const closeInvoice = () => {
    setShowInvoice(false);
    setSelectedOrder(null);
  };
  
  // Direct print function without opening modal
  const printInvoice = (order: Order) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Invoice - ${order.orderNumber}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
              .invoice-container { max-width: 800px; margin: 0 auto; }
              .header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
              .flex { display: flex; }
              .justify-between { justify-content: space-between; }
              .items-start { align-items: flex-start; }
              .text-right { text-align: right; }
              .text-center { text-align: center; }
              .text-3xl { font-size: 1.875rem; }
              .text-sm { font-size: 0.875rem; }
              .font-bold { font-weight: bold; }
              .font-semibold { font-weight: 600; }
              .text-gray-600 { color: #666; }
              .mb-2 { margin-bottom: 0.5rem; }
              .mb-4 { margin-bottom: 1rem; }
              .mb-6 { margin-bottom: 1.5rem; }
              .mb-8 { margin-bottom: 2rem; }
              .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
              .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
              .grid { display: grid; }
              .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
              .gap-8 { gap: 2rem; }
              table { width: 100%; border-collapse: collapse; }
              th { text-align: left; font-weight: 600; }
              .border-b { border-bottom: 1px solid #eee; }
              .border-t { border-top: 1px solid #eee; }
              .w-full { width: 100%; }
              .w-1/3 { width: 33.333333%; }
              @media print { body { print-color-adjust: exact; -webkit-print-color-adjust: exact; } }
            </style>
          </head>
          <body>
            <div class="invoice-container">
              <div class="header">
                <div class="flex justify-between items-start mb-6">
                  <h1 class="text-3xl font-bold">INVOICE</h1>
                  <div class="text-right text-sm text-gray-600">
                    <p class="font-semibold">Afghan International Gems</p>
                    <p>123 Gem Street, Kabul, Afghanistan</p>
                    <p><EMAIL></p>
                    <p>+93 123 456 7890</p>
                  </div>
                </div>
                <div class="flex justify-between">
                  <div>
                    <p class="font-semibold text-sm">Invoice Number:</p>
                    <p>${order.orderNumber}</p>
                  </div>
                  <div class="text-right">
                    <p class="font-semibold text-sm">Date:</p>
                    <p>${formatDate(order.createdAt)}</p>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-8 mb-8">
                <div>
                  <h2 class="font-semibold text-sm mb-2">Bill To:</h2>
                  <p>${order.customer.name}</p>
                  <p>${order.customer.email}</p>
                  ${order.customer.phone ? `<p>${order.customer.phone}</p>` : ''}
                </div>
                <div>
                  <h2 class="font-semibold text-sm mb-2">Ship To:</h2>
                  <p>${order.shippingAddress.street}</p>
                  <p>${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postalCode}</p>
                  <p>${order.shippingAddress.country}</p>
                </div>
              </div>

              <div class="mb-8">
                <h2 class="font-semibold text-sm mb-2">Order Details</h2>
                <table class="w-full mb-4">
                  <thead>
                    <tr class="border-b text-sm text-gray-700">
                      <th class="py-2 text-left">Product</th>
                      <th class="py-2 text-right">Price</th>
                      <th class="py-2 text-center">Qty</th>
                      <th class="py-2 text-right">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${order.items.map(item => `
                      <tr class="border-b">
                        <td class="py-3">${item.name}</td>
                        <td class="py-3 text-right">${formatCurrency(item.price)}</td>
                        <td class="py-3 text-center">${item.quantity}</td>
                        <td class="py-3 text-right">${formatCurrency(item.price * item.quantity)}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
                
                <div class="flex justify-end">
                  <div class="w-1/3">
                    <div class="flex justify-between py-2">
                      <span class="text-sm text-gray-600">Subtotal:</span>
                      <span>${formatCurrency(order.subtotal)}</span>
                    </div>
                    <div class="flex justify-between py-2">
                      <span class="text-sm text-gray-600">Shipping:</span>
                      <span>${formatCurrency(order.shipping)}</span>
                    </div>
                    <div class="flex justify-between py-2">
                      <span class="text-sm text-gray-600">Tax:</span>
                      <span>${formatCurrency(order.tax)}</span>
                    </div>
                    <div class="flex justify-between py-2 font-bold border-t">
                      <span>Total:</span>
                      <span>${formatCurrency(order.total)}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="mb-8">
                <h2 class="font-semibold text-sm mb-2">Payment Information</h2>
                <div class="grid grid-cols-2 border-b py-2">
                  <div>Payment Method:</div>
                  <div>${order.paymentMethod}</div>
                </div>
                <div class="grid grid-cols-2 border-b py-2">
                  <div>Payment Status:</div>
                  <div>${order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}</div>
                </div>
              </div>
              
              <div class="text-center text-sm text-gray-600 border-t pt-8 mt-8">
                <p>Thank you for your business!</p>
                <p>If you have any questions about this invoice, please contact our customer service.</p>
              </div>
            </div>
            <script>
              window.onload = function() { window.print(); window.close(); }
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // Component for Direct PDF download
  const DownloadPDFLink = ({ order }: { order: Order }) => (
    <PDFDownloadLink 
      document={<InvoicePDF order={order} />} 
      fileName={`invoice-${order.orderNumber}.pdf`}
      className="text-green-600 hover:text-green-900 flex items-center"
    >
      {({ loading }: { loading: boolean }) => (
        <>
          <Download size={16} className="mr-1" />
          {loading ? 'Generating...' : 'Download PDF'}
        </>
      )}
    </PDFDownloadLink>
  );
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Order Invoices</h1>
      </div>
      
      {/* Filters */}
      <form onSubmit={handleFilterSubmit} className="bg-white rounded-md shadow-sm p-4 mb-6">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search order number or customer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          
          {/* Status Filter */}
          <div className="relative w-40">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as OrderStatus)}
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          {/* Start Date */}
          <div className="flex items-center w-48">
            <label htmlFor="start-date" className="whitespace-nowrap text-xs font-medium text-gray-700 mr-2">
              Start:
            </label>
            <input
              id="start-date"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            />
          </div>
          
          {/* End Date */}
          <div className="flex items-center w-48">
            <label htmlFor="end-date" className="whitespace-nowrap text-xs font-medium text-gray-700 mr-2">
              End:
            </label>
            <input
              id="end-date"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            />
          </div>
          
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </form>
      
      {/* Orders Table */}
      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-200 border-t-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading invoices...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
        </div>
      ) : filteredOrders.length === 0 ? (
        <div className="text-center py-8 bg-white rounded-md shadow-sm">
          <p className="text-gray-600">No orders found matching your criteria.</p>
        </div>
      ) : (
        <div className="overflow-x-auto bg-white rounded-md shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order Number
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrders.map((order) => (
                <tr key={order._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">{order.orderNumber}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{order.customer.name}</div>
                    <div className="text-xs text-gray-500">{order.customer.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-500">{formatDate(order.createdAt)}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">{formatCurrency(order.total)}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${order.status === 'delivered' ? 'bg-green-100 text-green-800' : 
                        order.status === 'shipped' ? 'bg-blue-100 text-blue-800' : 
                        order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' : 
                        order.status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                        'bg-gray-100 text-gray-800'}`}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => viewInvoice(order)}
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                      >
                        <Eye size={16} className="mr-1" />
                        View
                      </button>
                      <button
                        onClick={() => printInvoice(order)}
                        className="text-blue-600 hover:text-blue-900 flex items-center"
                      >
                        <Printer size={16} className="mr-1" />
                        Print
                      </button>
                      <DownloadPDFLink order={order} />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Invoice Modal */}
      {showInvoice && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl h-5/6 overflow-hidden flex flex-col">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold">Invoice: {selectedOrder.orderNumber}</h2>
              <div className="flex space-x-3">
                <button 
                  onClick={() => closeInvoice()}
                  className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
            <div className="flex-1 overflow-auto p-6">
              <InvoiceView order={selectedOrder} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 