'use client';

import { useEffect } from 'react';

/**
 * Props for KeyboardShortcuts component
 */
type KeyboardShortcutsProps = {
  onDeveloperModeToggle: (isEnabled: boolean) => void;
  isDeveloperMode: boolean;
};

/**
 * Component to handle keyboard shortcuts
 * Provides keyboard shortcut functionality throughout the application
 */
export default function KeyboardShortcuts({ 
  onDeveloperModeToggle, 
  isDeveloperMode 
}: KeyboardShortcutsProps) {
  
  useEffect(() => {
    /**
     * Handle keydown events to detect keyboard shortcuts
     */
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for Ctrl+Shift+D to toggle developer mode
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        // Get the new state (toggled)
        const newDevModeState = !isDeveloperMode;
        
        // Toggle developer mode
        onDeveloperModeToggle(newDevModeState);
        
        // If enabling developer mode, pre-fetch connection status endpoints
        if (newDevModeState) {
          // Pre-fetch the API endpoints to warm them up
          Promise.all([
            fetch('/api/check-connection').catch(() => {}),
            fetch('/api/check-s3-connections').catch(() => {})
          ]).catch(err => {
            console.error('Failed to pre-fetch connection status:', err);
          });
        }
        
        // Show a toast or notification that developer mode was toggled
        showNotification(
          newDevModeState ? 'Developer Mode Enabled' : 'Developer Mode Disabled'
        );
        
        // Prevent default browser behavior
        event.preventDefault();
      }
    };
    
    // Helper function to show temporary notification
    const showNotification = (message: string) => {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = 
        'fixed top-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-md shadow-lg z-50 transition-all duration-300';
      notification.style.opacity = '0';
      notification.style.transform = 'translate(-50%, -20px)';
      
      // Add icon based on message
      const icon = document.createElement('span');
      icon.className = 'text-2xl mr-3';
      icon.innerHTML = message.includes('Enabled') 
        ? '🛠️' // Developer mode enabled
        : '👋'; // Developer mode disabled
      notification.appendChild(icon);
      
      // Create text container
      const textContainer = document.createElement('div');
      textContainer.className = 'inline-block';
      
      // Add message text
      const messageEl = document.createElement('span');
      messageEl.className = 'text-lg font-medium';
      messageEl.textContent = message;
      textContainer.appendChild(messageEl);
      
      // Add keyboard shortcut info
      const shortcutInfo = document.createElement('div');
      shortcutInfo.className = 'text-sm text-gray-300 mt-1';
      shortcutInfo.textContent = 'Keyboard shortcut: Ctrl+Shift+D';
      textContainer.appendChild(shortcutInfo);
      
      // Add text container to notification
      notification.appendChild(textContainer);
      
      // Add to document
      document.body.appendChild(notification);
      
      // Animate in
      setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translate(-50%, 0)';
      }, 10);
      
      // Remove after delay
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translate(-50%, -20px)';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 300);
      }, 4000);
    };
    
    // Add event listener
    window.addEventListener('keydown', handleKeyDown);
    
    // Cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isDeveloperMode, onDeveloperModeToggle]);
  
  // This component doesn't render anything visible
  return null;
} 