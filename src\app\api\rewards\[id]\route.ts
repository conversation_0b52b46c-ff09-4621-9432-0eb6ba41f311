import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

interface Params {
  params: {
    id: string;
  };
}

export async function DELETE(req: Request, { params }: Params) {
  try {
    const { id } = params;
    const rewardId = parseInt(id, 10);
    
    if (isNaN(rewardId)) {
      return NextResponse.json(
        { error: 'Invalid reward ID' },
        { status: 400 }
      );
    }
    
    await dbConnect();
    
    // Find all customers with this reward and remove it
    const updateResult = await Customer.updateMany(
      { 'currentRewards.id': rewardId },
      { $pull: { currentRewards: { id: rewardId } } }
    );
    
    return NextResponse.json({
      success: true,
      message: 'Reward deleted successfully',
      customersUpdated: updateResult.modifiedCount
    });
  } catch (error) {
    console.error('Error deleting reward:', error);
    return NextResponse.json(
      { error: 'Failed to delete reward' },
      { status: 500 }
    );
  }
} 