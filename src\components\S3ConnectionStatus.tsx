'use client';

import { useState, useEffect } from 'react';
import type { S3ConnectionStatusResponse } from '@/lib/s3';

/**
 * Type definition for the S3 connection status response from the API
 */
type S3ConnectionsResponse = {
  images: S3ConnectionStatusResponse;
  videos: S3ConnectionStatusResponse;
  error?: string;
  details?: string;
};

/**
 * S3ConnectionStatus component displays the connection status to AWS S3 buckets
 */
export default function S3ConnectionStatus() {
  // State to store S3 bucket connection status
  const [connectionsStatus, setConnectionsStatus] = useState<S3ConnectionsResponse | null>(null);
  // State to track if we're currently checking connections
  const [loading, setLoading] = useState<boolean>(true);

  // Effect to check S3 bucket connections when component mounts
  useEffect(() => {
    const checkConnections = async () => {
      try {
        // Call our API endpoint that checks S3 bucket connections
        const response = await fetch('/api/check-s3-connections');
        
        // Parse the JSON response
        const data = await response.json();
        
        // Update state with connection status
        setConnectionsStatus(data);
      } catch (error: any) {
        // Handle client-side fetch errors
        setConnectionsStatus({
          images: {
            status: 'error',
            bucketName: 'Unknown',
            error: {
              message: 'Failed to check S3 connection',
              explanation: 'There was a problem connecting to the server to check the S3 bucket status.'
            }
          },
          videos: {
            status: 'error',
            bucketName: 'Unknown',
            error: {
              message: 'Failed to check S3 connection',
              explanation: 'There was a problem connecting to the server to check the S3 bucket status.'
            }
          }
        });
      } finally {
        // Set loading to false once we're done
        setLoading(false);
      }
    };

    // Call the function to check connections
    checkConnections();
  }, []); // Empty dependency array means this effect runs once when component mounts

  // If we're still loading, show a loading indicator
  if (loading) {
    return (
      <div className="bg-gray-100 p-4 rounded-md shadow-sm">
        <div className="flex items-center">
          <div className="animate-spin h-5 w-5 mr-3 border-2 border-gray-500 border-t-transparent rounded-full"></div>
          <p>Checking S3 bucket connections...</p>
        </div>
      </div>
    );
  }

  // If we have an overall error
  if (connectionsStatus?.error) {
    return (
      <div className="bg-red-100 p-4 rounded-md shadow-sm border border-red-200">
        <div className="flex items-start">
          <svg 
            className="h-5 w-5 text-red-500 mr-3 mt-0.5" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M6 18L18 6M6 6l12 12" 
            />
          </svg>
          <div>
            <p className="text-red-700 font-medium">Failed to check S3 bucket connections</p>
            <p className="text-red-600 text-sm mt-1">{connectionsStatus.error}</p>
            {connectionsStatus.details && (
              <p className="text-red-600 text-sm mt-1">{connectionsStatus.details}</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Component to display individual bucket status
  const BucketStatus = ({ title, status }: { title: string, status: S3ConnectionStatusResponse }) => {
    if (status.status === 'connected') {
      return (
        <div className="bg-green-100 p-4 rounded-md shadow-sm border border-green-200 mb-4">
          <div className="flex items-start">
            <svg 
              className="h-5 w-5 text-green-500 mr-3 mt-0.5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
            <div>
              <p className="text-green-700 font-medium">{title} - Connected to {status.bucketName}</p>
              <p className="text-green-600 text-sm mt-1">
                {status.objects?.count 
                  ? `Found ${status.objects.count} objects in bucket`
                  : 'Bucket is empty'}
              </p>
              {status.objects?.names && status.objects.names.length > 0 && (
                <div className="mt-2">
                  <p className="text-green-700 text-sm font-medium">Sample files:</p>
                  <ul className="text-xs text-green-600 mt-1 pl-2">
                    {status.objects.names.slice(0, 5).map((name, index) => (
                      <li key={index}>{name}</li>
                    ))}
                    {status.objects.names.length > 5 && <li>...</li>}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="bg-red-100 p-4 rounded-md shadow-sm border border-red-200 mb-4">
          <div className="flex items-start">
            <svg 
              className="h-5 w-5 text-red-500 mr-3 mt-0.5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
            <div>
              <p className="text-red-700 font-medium">{title} - Failed to connect to {status.bucketName}</p>
              {status.error && (
                <>
                  <p className="text-red-600 text-sm mt-1">{status.error.explanation}</p>
                  <details className="mt-2 text-xs">
                    <summary className="cursor-pointer text-red-600 font-medium">Technical details</summary>
                    <div className="pl-2 mt-1 text-red-600">
                      <p>Error: {status.error.message}</p>
                      {status.error.code && <p>Code: {status.error.code}</p>}
                      {status.error.name && <p>Type: {status.error.name}</p>}
                    </div>
                  </details>
                </>
              )}
            </div>
          </div>
        </div>
      );
    }
  };

  // Main return - display both buckets' status
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold mb-2">S3 Bucket Connection Status</h2>
      
      {connectionsStatus?.images && (
        <BucketStatus title="Images Bucket" status={connectionsStatus.images} />
      )}
      
      {connectionsStatus?.videos && (
        <BucketStatus title="Videos Bucket" status={connectionsStatus.videos} />
      )}
    </div>
  );
} 