'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, RefreshCw, Eye, Filter, ArrowLeft, FileText, PlusCircle, CheckCircle, Pencil } from 'lucide-react';

type ReturnStatus = 'all' | 'pending' | 'approved' | 'rejected' | 'completed';

type ReturnRequest = {
  _id: string;
  returnNumber: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
  };
  requestDate?: string;
  createdAt: string;
  reason: string;
  items: {
    productId: string;
    name: string;
    quantity: number;
    price: number;
  }[];
  status: Exclude<ReturnStatus, 'all'>;
  refundAmount: number;
};

type Order = {
  _id: string;
  orderNumber: string;
  customer?: {
    name: string;
    email: string;
  };
  items?: Array<{
    productId: string;
    name: string;
    price: number;
    quantity: number;
  }>;
  total?: number;
};

type Tab = 'manage' | 'create';

export default function ReturnsAndRefundsPage() {
  // Tab state
  const [activeTab, setActiveTab] = useState<Tab>('manage');
  
  // Returns management tab state
  const [loading, setLoading] = useState(false);
  const [returns, setReturns] = useState<ReturnRequest[]>([]);
  const [filteredReturns, setFilteredReturns] = useState<ReturnRequest[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<ReturnStatus>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [editingReturnId, setEditingReturnId] = useState<string | null>(null);
  const [editingStatus, setEditingStatus] = useState<Exclude<ReturnStatus, 'all'>>('pending');
  const [updateLoading, setUpdateLoading] = useState(false);
  
  // Create return request tab state
  const [selectedCreateOrderId, setSelectedCreateOrderId] = useState<string>('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [returnReason, setReturnReason] = useState<string>('');
  const [selectedItems, setSelectedItems] = useState<{[key: string]: boolean}>({});
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  
  // Fetch orders data - would be replaced with actual API call
  useEffect(() => {
    // Mock data for orders dropdown
    const mockOrders: Order[] = [
      { 
        _id: '1', 
        orderNumber: 'ORD-00123',
        customer: { name: 'John Doe', email: '<EMAIL>' },
        items: [
          { productId: 'p1', name: 'Emerald Ring', price: 1299.99, quantity: 1 },
          { productId: 'p2', name: 'Ruby Pendant', price: 899.50, quantity: 1 }
        ],
        total: 2199.49
      },
      { 
        _id: '2', 
        orderNumber: 'ORD-00124',
        customer: { name: 'Jane Smith', email: '<EMAIL>' },
        items: [
          { productId: 'p3', name: 'Sapphire Earrings', price: 1599.99, quantity: 1 }
        ],
        total: 1599.99
      },
      { _id: '3', orderNumber: 'ORD-00125' },
      { _id: '4', orderNumber: 'ORD-00126' },
      { _id: '5', orderNumber: 'ORD-00127' },
    ];
    
    setOrders(mockOrders);
  }, []);
  
  // Load existing return requests from MongoDB
  useEffect(() => {
    fetchReturns();
  }, []);
  
  // Apply filters whenever search query or status filter changes
  useEffect(() => {
    applyFilters();
  }, [returns, searchQuery, statusFilter, selectedOrderId]);
  
  const fetchReturns = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query parameters for API request
      const params = new URLSearchParams();
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      
      if (searchQuery.trim()) {
        params.append('q', searchQuery.trim());
      }
      
      if (selectedOrderId !== 'all') {
        const order = orders.find(o => o._id === selectedOrderId);
        if (order) {
          params.append('orderNumber', order.orderNumber);
        }
      }
      
      // Fetch returns from API
      const response = await fetch(`/api/returns?${params.toString()}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch return requests');
      }
      
      setReturns(data.returns);
      setFilteredReturns(data.returns);
    } catch (error: any) {
      setError('Failed to fetch return requests. Please try again later.');
      console.error('Error fetching returns:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const applyFilters = () => {
    // Local filtering for UI response (API can also filter but this gives immediate feedback)
    let filtered = [...returns];
    
    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(returnRequest => returnRequest.status === statusFilter);
    }
    
    // Filter by order number
    if (selectedOrderId !== 'all') {
      const order = orders.find(o => o._id === selectedOrderId);
      if (order) {
        filtered = filtered.filter(returnRequest => returnRequest.orderNumber === order.orderNumber);
      }
    }
    
    // Filter by search query (return number or customer name/email)
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(returnRequest => 
        returnRequest.returnNumber.toLowerCase().includes(query) ||
        returnRequest.customer.name.toLowerCase().includes(query) ||
        returnRequest.customer.email.toLowerCase().includes(query)
      );
    }
    
    setFilteredReturns(filtered);
  };
  
  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchReturns(); // Refetch with new filters
  };
  
  const handleCreateReturnRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedOrder) return;
    
    // Check if at least one item is selected
    const hasSelectedItems = Object.values(selectedItems).some(selected => selected);
    if (!hasSelectedItems) {
      alert('Please select at least one item to return');
      return;
    }
    
    if (!returnReason.trim()) {
      alert('Please provide a return reason');
      return;
    }
    
    setFormSubmitting(true);
    
    try {
      // Prepare the return request data
      const returnItems = selectedOrder.items?.filter(item => selectedItems[item.productId]) || [];
      
      const returnData = {
        orderNumber: selectedOrder.orderNumber,
        customer: {
          name: selectedOrder.customer?.name || 'Customer',
          email: selectedOrder.customer?.email || '<EMAIL>'
        },
        items: returnItems,
        reason: returnReason,
        status: 'pending',
        refundAmount: returnItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      };
      
      // Send data to API
      const response = await fetch('/api/returns', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(returnData)
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create return request');
      }
      
      // Add the new return to the list and switch to the manage tab
      setReturns(prev => [result.returnRequest, ...prev]);
      setFilteredReturns(prev => [result.returnRequest, ...prev]);
      setActiveTab('manage');
      
      // Reset form
      setSelectedCreateOrderId('');
      setSelectedOrder(null);
      setReturnReason('');
      setSelectedItems({});
      setFormSuccess(true);
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => setFormSuccess(false), 3000);
      
    } catch (error: any) {
      console.error('Error creating return request:', error);
      alert('Failed to create return request: ' + (error.message || 'Unknown error'));
    } finally {
      setFormSubmitting(false);
    }
  };
  
  const handleEditClick = (returnId: string, currentStatus: Exclude<ReturnStatus, 'all'>) => {
    setEditingReturnId(returnId);
    setEditingStatus(currentStatus);
  };
  
  const handleStatusUpdate = async (returnId: string) => {
    setUpdateLoading(true);
    
    try {
      const response = await fetch(`/api/returns/${returnId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: editingStatus })
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update return status');
      }
      
      // Update the returns list with the updated status
      setReturns(prev => 
        prev.map(returnItem => 
          returnItem._id === returnId 
            ? { ...returnItem, status: editingStatus } 
            : returnItem
        )
      );
      
      // Also update filtered returns
      setFilteredReturns(prev => 
        prev.map(returnItem => 
          returnItem._id === returnId 
            ? { ...returnItem, status: editingStatus } 
            : returnItem
        )
      );
      
      // Reset editing state
      setEditingReturnId(null);
    } catch (error: any) {
      console.error('Error updating return status:', error);
      alert('Failed to update status: ' + (error.message || 'Unknown error'));
    } finally {
      setUpdateLoading(false);
    }
  };
  
  const handleCancelEdit = () => {
    setEditingReturnId(null);
  };
  
  const handleOrderSelect = (orderId: string) => {
    setSelectedCreateOrderId(orderId);
    const order = orders.find(o => o._id === orderId) || null;
    setSelectedOrder(order);
    
    // Reset selected items when order changes
    if (order && order.items) {
      const newSelectedItems: {[key: string]: boolean} = {};
      order.items.forEach(item => {
        newSelectedItems[item.productId] = false;
      });
      setSelectedItems(newSelectedItems);
    } else {
      setSelectedItems({});
    }
  };
  
  const toggleItemSelection = (productId: string) => {
    setSelectedItems(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };
  
  const getStatusColor = (status: Exclude<ReturnStatus, 'all'>) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link href="/admin/orders" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-semibold">Returns & Refunds</h1>
        </div>
        {activeTab === 'manage' && (
          <button 
            onClick={fetchReturns}
            className="flex items-center py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
        )}
      </div>
      
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('manage')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'manage'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Manage Returns
          </button>
          <button
            onClick={() => setActiveTab('create')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'create'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Create Return Request
          </button>
        </nav>
      </div>
      
      {/* Manage Returns Tab */}
      {activeTab === 'manage' && (
        <>
          {/* Filters */}
          <form onSubmit={handleFilterSubmit} className="bg-white rounded-md shadow-sm p-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={18} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search return number or customer..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              
              {/* Order Number Filter */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FileText size={18} className="text-gray-400" />
                </div>
                <select
                  value={selectedOrderId}
                  onChange={(e) => setSelectedOrderId(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">All Orders</option>
                  {orders.map(order => (
                    <option key={order._id} value={order._id}>
                      {order.orderNumber}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Status Filter */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter size={18} className="text-gray-400" />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as ReturnStatus)}
                  className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
              
              <div>
                <button 
                  type="submit"
                  className="h-full w-full px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Apply
                </button>
              </div>
            </div>
          </form>
          
          {/* Error message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">
              {error}
            </div>
          )}
          
          {/* Returns Table */}
          <div className="bg-white rounded-md shadow overflow-hidden">
            {loading ? (
              <div className="p-8 flex justify-center">
                <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
              </div>
            ) : filteredReturns.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <p className="mb-2 text-lg font-medium">No return requests found</p>
                <p>When customers request returns, they will appear here</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {/* Sample data would be mapped here when available */}
                    {filteredReturns.map((returnRequest) => (
                      <tr key={returnRequest._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          {returnRequest.returnNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {returnRequest.orderNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{returnRequest.customer.name}</div>
                          <div className="text-sm text-gray-500">{returnRequest.customer.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(returnRequest.requestDate || returnRequest.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(returnRequest.status)}`}>
                            {returnRequest.status.charAt(0).toUpperCase() + returnRequest.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          ${returnRequest.refundAmount.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          {editingReturnId === returnRequest._id ? (
                            <div className="flex items-center justify-end space-x-2">
                              <select
                                value={editingStatus}
                                onChange={(e) => setEditingStatus(e.target.value as Exclude<ReturnStatus, 'all'>)}
                                className="px-2 py-1 border rounded text-sm"
                              >
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                                <option value="completed">Completed</option>
                              </select>
                              <button 
                                onClick={() => handleStatusUpdate(returnRequest._id)}
                                disabled={updateLoading}
                                className="px-2 py-1 bg-blue-600 text-white rounded text-xs"
                              >
                                {updateLoading ? 'Updating...' : 'Update'}
                              </button>
                              <button 
                                onClick={handleCancelEdit}
                                className="px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs"
                              >
                                Cancel
                              </button>
                            </div>
                          ) : (
                            <button 
                              onClick={() => handleEditClick(returnRequest._id, returnRequest.status)}
                              className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                            >
                              <Pencil size={16} className="mr-1" />
                              Status Edit
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </>
      )}
      
      {/* Create Return Request Tab */}
      {activeTab === 'create' && (
        <div className="bg-white rounded-md shadow-sm p-6">
          <h2 className="text-lg font-medium mb-6">Create New Return Request</h2>
          
          {formSuccess ? (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-2" size={20} />
                <p className="text-green-700">Return request created successfully!</p>
              </div>
            </div>
          ) : (
            <form onSubmit={handleCreateReturnRequest}>
              {/* Order Selection */}
              <div className="mb-6">
                <label htmlFor="orderSelect" className="block text-sm font-medium text-gray-700 mb-1">
                  Select Order
                </label>
                <select
                  id="orderSelect"
                  value={selectedCreateOrderId}
                  onChange={(e) => handleOrderSelect(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  required
                >
                  <option value="">-- Select an order --</option>
                  {orders.map(order => (
                    <option key={order._id} value={order._id}>
                      {order.orderNumber}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Order Details (shown after selection) */}
              {selectedOrder && (
                <>
                  {/* Customer Info */}
                  {selectedOrder.customer && (
                    <div className="mb-6 p-4 bg-gray-50 rounded-md">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h3>
                      <p className="text-gray-700">{selectedOrder.customer.name}</p>
                      <p className="text-gray-500">{selectedOrder.customer.email}</p>
                    </div>
                  )}
                  
                  {/* Item Selection */}
                  {selectedOrder.items && selectedOrder.items.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Select Items to Return</h3>
                      <div className="border rounded-md overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                Select
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Price
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Quantity
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {selectedOrder.items.map(item => (
                              <tr key={item.productId} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <input
                                    type="checkbox"
                                    checked={!!selectedItems[item.productId]}
                                    onChange={() => toggleItemSelection(item.productId)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {item.name}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                  ${item.price.toFixed(2)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                  {item.quantity}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                  
                  {/* Return Reason */}
                  <div className="mb-6">
                    <label htmlFor="returnReason" className="block text-sm font-medium text-gray-700 mb-1">
                      Return Reason
                    </label>
                    <textarea
                      id="returnReason"
                      value={returnReason}
                      onChange={(e) => setReturnReason(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Provide details about the return request..."
                      required
                    />
                  </div>
                </>
              )}
              
              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={formSubmitting || !selectedOrder}
                  className={`flex items-center py-2 px-4 rounded-md ${
                    formSubmitting || !selectedOrder 
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {formSubmitting ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <PlusCircle size={16} className="mr-2" />
                      Create Return Request
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      )}
    </div>
  );
} 