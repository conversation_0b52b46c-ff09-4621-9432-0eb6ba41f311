import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { PaymentGateway } from '@/models/PaymentGateway';

// GET /api/admin/settings/payment-gateways - Get all payment gateways
export async function GET(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get all payment gateways
    const paymentGateways = await PaymentGateway.find().sort({ name: 1 });
    
    return NextResponse.json({ paymentGateways });
  } catch (error) {
    console.error('Error fetching payment gateways:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment gateways' },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings/payment-gateways - Create a new payment gateway
export async function POST(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Parse request body
    const gatewayData = await req.json();
    
    // Validate required fields
    if (!gatewayData.name || !gatewayData.provider) {
      return NextResponse.json(
        { error: 'Name and provider are required fields' },
        { status: 400 }
      );
    }
    
    // Create new payment gateway
    const newGateway = await PaymentGateway.create(gatewayData);
    
    return NextResponse.json(newGateway, { status: 201 });
  } catch (error) {
    console.error('Error creating payment gateway:', error);
    return NextResponse.json(
      { error: 'Failed to create payment gateway' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings/payment-gateways/:id - Update a payment gateway
export async function PUT(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get payment gateway ID from URL
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Payment gateway ID is required' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const updateData = await req.json();
    
    // Check if gateway exists
    const gateway = await PaymentGateway.findById(id);
    if (!gateway) {
      return NextResponse.json(
        { error: 'Payment gateway not found' },
        { status: 404 }
      );
    }
    
    // Update and return the payment gateway
    const updatedGateway = await PaymentGateway.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );
    
    return NextResponse.json(updatedGateway);
  } catch (error) {
    console.error('Error updating payment gateway:', error);
    return NextResponse.json(
      { error: 'Failed to update payment gateway' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/settings/payment-gateways/:id - Delete a payment gateway
export async function DELETE(req: NextRequest) {
  try {
    // Note: Authentication is handled by the frontend AdminAuthContext
    // This API relies on frontend permission checks
    
    await dbConnect();
    
    // Get payment gateway ID from URL
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Payment gateway ID is required' },
        { status: 400 }
      );
    }
    
    // Check if gateway exists
    const gateway = await PaymentGateway.findById(id);
    if (!gateway) {
      return NextResponse.json(
        { error: 'Payment gateway not found' },
        { status: 404 }
      );
    }
    
    // Delete the payment gateway
    await PaymentGateway.findByIdAndDelete(id);
    
    return NextResponse.json(
      { message: 'Payment gateway deleted successfully' }
    );
  } catch (error) {
    console.error('Error deleting payment gateway:', error);
    return NextResponse.json(
      { error: 'Failed to delete payment gateway' },
      { status: 500 }
    );
  }
} 