'use client';

import { 
  Document, 
  Page, 
  Text, 
  View, 
  StyleSheet
} from '@react-pdf/renderer';

// Define Order types
type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

type OrderItem = {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  imageUrl?: string;
};

type Order = {
  _id: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
  };
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: 'paid' | 'unpaid' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
};

// PDF styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
  },
  header: {
    marginBottom: 20,
    borderBottom: '1pt solid #EEEEEE',
    paddingBottom: 10,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
  },
  companyInfo: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'right',
  },
  orderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  orderInfoLeft: {
    flex: 1,
  },
  orderInfoRight: {
    flex: 1,
    textAlign: 'right',
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333333',
  },
  section: {
    marginBottom: 15,
  },
  addressSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  addressBox: {
    width: '48%',
  },
  itemsTable: {
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    borderBottomStyle: 'solid',
    paddingBottom: 5,
    marginBottom: 5,
  },
  tableHeaderCell: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#666666',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    borderBottomStyle: 'solid',
    paddingVertical: 8,
  },
  tableCell: {
    fontSize: 10,
    color: '#333333',
  },
  productCol: {
    flex: 3,
  },
  priceCol: {
    flex: 1,
    textAlign: 'right',
  },
  qtyCol: {
    flex: 1,
    textAlign: 'center',
  },
  totalCol: {
    flex: 1,
    textAlign: 'right',
  },
  totalsSection: {
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    borderTopStyle: 'solid',
    paddingTop: 10,
    alignItems: 'flex-end',
  },
  totalRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  totalLabel: {
    fontSize: 10,
    color: '#666666',
    width: 80,
  },
  totalValue: {
    fontSize: 10,
    color: '#333333',
    width: 80,
    textAlign: 'right',
  },
  grandTotal: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333333',
  },
  footer: {
    marginTop: 30,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    borderTopStyle: 'solid',
    paddingTop: 10,
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
  },
});

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// PDF Document template
export const InvoicePDF = ({ order }: { order: Order }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.headerTitle}>INVOICE</Text>
          <View style={styles.companyInfo}>
            <Text>Afghan International Gems</Text>
            <Text>123 Gem Street, Kabul, Afghanistan</Text>
            <Text><EMAIL></Text>
            <Text>+93 123 456 7890</Text>
          </View>
        </View>
        <View style={styles.orderInfo}>
          <View style={styles.orderInfoLeft}>
            <Text style={styles.sectionTitle}>Invoice Number:</Text>
            <Text>{order.orderNumber}</Text>
          </View>
          <View style={styles.orderInfoRight}>
            <Text style={styles.sectionTitle}>Date:</Text>
            <Text>{formatDate(order.createdAt)}</Text>
          </View>
        </View>
      </View>

      <View style={styles.addressSection}>
        <View style={styles.addressBox}>
          <Text style={styles.sectionTitle}>Bill To:</Text>
          <Text>{order.customer.name}</Text>
          <Text>{order.customer.email}</Text>
          {order.customer.phone && <Text>{order.customer.phone}</Text>}
        </View>
        <View style={styles.addressBox}>
          <Text style={styles.sectionTitle}>Ship To:</Text>
          <Text>{order.shippingAddress.street}</Text>
          <Text>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}</Text>
          <Text>{order.shippingAddress.country}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Order Details</Text>
        <View style={styles.itemsTable}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableHeaderCell, styles.productCol]}>Product</Text>
            <Text style={[styles.tableHeaderCell, styles.priceCol]}>Price</Text>
            <Text style={[styles.tableHeaderCell, styles.qtyCol]}>Qty</Text>
            <Text style={[styles.tableHeaderCell, styles.totalCol]}>Total</Text>
          </View>
          
          {order.items.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableCell, styles.productCol]}>{item.name}</Text>
              <Text style={[styles.tableCell, styles.priceCol]}>{formatCurrency(item.price)}</Text>
              <Text style={[styles.tableCell, styles.qtyCol]}>{item.quantity}</Text>
              <Text style={[styles.tableCell, styles.totalCol]}>
                {formatCurrency(item.price * item.quantity)}
              </Text>
            </View>
          ))}
        </View>
        
        <View style={styles.totalsSection}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal:</Text>
            <Text style={styles.totalValue}>{formatCurrency(order.subtotal)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Shipping:</Text>
            <Text style={styles.totalValue}>{formatCurrency(order.shipping)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Tax:</Text>
            <Text style={styles.totalValue}>{formatCurrency(order.tax)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={[styles.totalLabel, styles.grandTotal]}>Total:</Text>
            <Text style={[styles.totalValue, styles.grandTotal]}>{formatCurrency(order.total)}</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Payment Information</Text>
        <View style={styles.tableRow}>
          <Text style={[styles.tableCell, { flex: 1 }]}>Payment Method:</Text>
          <Text style={[styles.tableCell, { flex: 2 }]}>{order.paymentMethod}</Text>
        </View>
        <View style={styles.tableRow}>
          <Text style={[styles.tableCell, { flex: 1 }]}>Payment Status:</Text>
          <Text style={[styles.tableCell, { flex: 2 }]}>
            {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
          </Text>
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text>Thank you for your business!</Text>
        <Text>If you have any questions about this invoice, please contact our customer service.</Text>
      </View>
    </Page>
  </Document>
); 