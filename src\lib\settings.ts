import dbConnect from './dbConnect';
import { Settings as SettingsModel } from '@/models/Settings';

/**
 * Get all settings from the database
 * Returns a cached version if available to reduce database calls
 */
let cachedSettings: any = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function getSettings() {
  // Check if we have a recent cached version
  const now = Date.now();
  if (cachedSettings && now - lastFetchTime < CACHE_DURATION) {
    return cachedSettings;
  }
  
  // Otherwise fetch from database
  await dbConnect();
  
  // Use the model's static method
  // @ts-ignore: The getSingleton method is defined in the model but TypeScript doesn't recognize it
  const settings = await SettingsModel.getSingleton();
  
  // Update cache
  cachedSettings = settings.toObject();
  lastFetchTime = now;
  
  return cachedSettings;
}

/**
 * Clear the settings cache to force a fresh fetch next time
 */
export function clearSettingsCache() {
  cachedSettings = null;
}

/**
 * Get a specific setting value with dot notation path
 * Example: getSetting('store.storeName')
 */
export async function getSetting(path: string) {
  const settings = await getSettings();
  
  const parts = path.split('.');
  let value = settings;
  
  for (const part of parts) {
    if (value === undefined || value === null) {
      return undefined;
    }
    value = value[part];
  }
  
  return value;
}

/**
 * Get store settings
 */
export async function getStoreSettings() {
  const settings = await getSettings();
  return settings.store;
}

/**
 * Get payment settings
 */
export async function getPaymentSettings() {
  const settings = await getSettings();
  return settings.payment;
}

/**
 * Get shipping settings
 */
export async function getShippingSettings() {
  const settings = await getSettings();
  return settings.shipping;
}

/**
 * Get store currency
 */
export async function getStoreCurrency() {
  return getSetting('store.storeCurrency');
}

/**
 * Format price based on store currency
 */
export async function formatPrice(price: number) {
  const currency = await getStoreCurrency() || 'USD';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(price);
}

/**
 * Check if a payment method is enabled
 */
export async function isPaymentMethodEnabled(method: 'stripe' | 'paypal' | 'cashOnDelivery') {
  const methodMap = {
    stripe: 'payment.stripeEnabled',
    paypal: 'payment.paypalEnabled',
    cashOnDelivery: 'payment.cashOnDeliveryEnabled',
  };
  
  return getSetting(methodMap[method]);
}

/**
 * Get default shipping method
 */
export async function getDefaultShippingMethod() {
  return getSetting('shipping.defaultShippingMethod');
}

/**
 * Calculate shipping cost based on current settings and order total
 */
export async function calculateShippingCost(orderTotal: number) {
  const shipping = await getShippingSettings();
  
  // Free shipping check
  if (shipping.freeShippingEnabled && orderTotal >= shipping.freeShippingMinimumOrder) {
    return 0;
  }
  
  // Flat rate shipping
  if (shipping.flatRateEnabled) {
    return shipping.flatRateAmount;
  }
  
  // Default to 0 (e.g., if only custom shipping is enabled, which would be handled elsewhere)
  return 0;
} 