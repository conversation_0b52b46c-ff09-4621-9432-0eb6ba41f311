'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useLanguage, SupportedLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';

interface LanguageSwitcherProps {
  textClassName?: string;
  dropdownClassName?: string;
  iconColor?: string;
  isHeroSection?: boolean;
  useAbbreviation?: boolean;
}

export default function LanguageSwitcher({ textClassName, dropdownClassName, iconColor, isHeroSection, useAbbreviation }: LanguageSwitcherProps) {
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    // Add event listener when dropdown is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleLanguageSelect = (selectedLanguage: SupportedLanguage) => {
    setLanguage(selectedLanguage);
    setIsOpen(false);
  };

  const languages: SupportedLanguage[] = ['English', 'French', 'Italian'];

  const getLanguageAbbreviation = (lang: SupportedLanguage): string => {
    switch (lang) {
      case 'English': return 'EN';
      case 'French': return 'FR';
      case 'Italian': return 'IT';
      default: return lang;
    }
  };

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className={`flex items-center space-x-1 transition-colors py-1 px-2 pr-0 rounded-md ${
          isHeroSection
            ? 'text-white hover:text-blue-300 hover:bg-white/10'
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
        }`}
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <Globe size={16} color={iconColor} />
        <span className={textClassName}>
          {useAbbreviation ? getLanguageAbbreviation(language) : language}
        </span>
        <svg
          className={`w-3 h-3 transform ${isOpen ? 'rotate-180' : ''} transition-transform`}
          fill="none"
          stroke={iconColor || "currentColor"}
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      
      {isOpen && (
        <div
          className={`absolute z-10 right-0 mt-2 w-32 rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 focus:outline-none ${dropdownClassName || 'bg-white'}`}
          role="menu"
          aria-orientation="vertical"
        >
          {languages.map((lang, index) => (
            <button
              key={index}
              onClick={() => handleLanguageSelect(lang)}
              className={`w-full text-left px-4 py-2 pr-0 text-sm flex items-center space-x-2 ${
                isHeroSection
                  ? language === lang
                    ? 'bg-white/20 text-white'
                    : 'text-white hover:bg-white/10'
                  : language === lang
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-700 hover:bg-gray-100'
              }`}
              role="menuitem"
            >
              <Globe size={16} color={iconColor} />
              <span className={textClassName}>{lang}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 