'use client';

import { useState } from 'react';
import { 
  BarChart, Bar, PieChart, Pie, Cell, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowUp, ArrowDown, TrendingUp, TrendingDown } from "lucide-react";

// Mock data - replace with actual API calls in production
const mockProductData = {
  topSelling: [
    { name: 'Diamond Ring', sales: 548, revenue: 84500 },
    { name: 'Ruby Necklace', sales: 350, revenue: 52500 },
    { name: 'Emerald Earrings', sales: 280, revenue: 42000 },
    { name: 'Sapphire Bracelet', sales: 245, revenue: 36750 },
    { name: 'Gold Chain', sales: 220, revenue: 33000 }
  ],
  underperforming: [
    { name: 'Amber Pendant', sales: 15, revenue: 2250 },
    { name: 'Opal Set', sales: 22, revenue: 3300 },
    { name: '<PERSON> Pin', sales: 25, revenue: 3750 },
    { name: 'Jade Bracelet', sales: 30, revenue: 4500 },
    { name: 'Turquoise Ring', sales: 35, revenue: 5250 }
  ],
  categoryPerformance: [
    { name: 'Rings', value: 35 },
    { name: 'Necklaces', value: 25 },
    { name: 'Earrings', value: 15 },
    { name: 'Bracelets', value: 18 },
    { name: 'Watches', value: 7 }
  ],
  productTrends: [
    { month: 'Jan', diamonds: 100, gold: 80, silver: 60 },
    { month: 'Feb', diamonds: 120, gold: 70, silver: 65 },
    { month: 'Mar', diamonds: 140, gold: 90, silver: 70 },
    { month: 'Apr', diamonds: 130, gold: 95, silver: 75 },
    { month: 'May', diamonds: 150, gold: 100, silver: 80 },
    { month: 'Jun', diamonds: 170, gold: 110, silver: 85 }
  ]
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function ProductPerformanceScreen() {
  const [timeRange, setTimeRange] = useState<string>('month');
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Product Performance</h1>
        
        <Select 
          value={timeRange} 
          onValueChange={(value: string) => setTimeRange(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Top and Underperforming Products */}
      <Tabs defaultValue="top" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="top">Top Selling Products</TabsTrigger>
          <TabsTrigger value="under">Underperforming Products</TabsTrigger>
        </TabsList>
        
        <TabsContent value="top">
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales (Units)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trend</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mockProductData.topSelling.map((product, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.sales}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${product.revenue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="flex items-center text-green-600">
                            <TrendingUp className="h-4 w-4 mr-1" />
                            <span>+{Math.floor(Math.random() * 20) + 5}%</span>
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="under">
          <Card>
            <CardHeader>
              <CardTitle>Underperforming Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales (Units)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trend</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mockProductData.underperforming.map((product, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.sales}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${product.revenue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="flex items-center text-red-600">
                            <TrendingDown className="h-4 w-4 mr-1" />
                            <span>-{Math.floor(Math.random() * 15) + 5}%</span>
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Category Distribution & Product Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={mockProductData.categoryPerformance}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {mockProductData.categoryPerformance.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Product Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={mockProductData.productTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="diamonds" fill="#8884d8" name="Diamonds" />
                  <Bar dataKey="gold" fill="#82ca9d" name="Gold" />
                  <Bar dataKey="silver" fill="#ffc658" name="Silver" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 