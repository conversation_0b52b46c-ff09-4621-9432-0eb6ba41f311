'use client';

import Link from 'next/link';
import Image from 'next/image';
import localFont from 'next/font/local';
import { usePathname } from 'next/navigation';
import { SubscriptionForm } from '@/components/forms/SubscriptionForm';
import { useLanguage } from '@/contexts/LanguageContext';
import './Footer.css';

// Load <PERSON> font from downloaded fonts
const dosisFont = localFont({
  src: '../../../downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf',
  variable: '--font-dosis',
  display: 'swap',
  weight: '400 700'
});

// Load Segoe UI font from downloaded fonts
const segoeUIFont = localFont({
  src: '../../../downloaded fonts/segoe-ui/segoeuithis.ttf',
  variable: '--font-segoe-ui',
  display: 'swap',
  weight: '400'
});

export default function Footer() {
  const { translations } = useLanguage();
  const pathname = usePathname();
  
  return (
    <>
      {/* Subscribe to Newsletter Section */}
      <section className={`footer-subscribe-section ${dosisFont.variable} ${segoeUIFont.variable}`}>
        {/* Background Image and Overlay for subscribe section - removed as requested */}
        
        <div className="footer-subscribe-container">
          <h2 className="footer-subscribe-heading">
            {translations.newsletter_heading}
          </h2>
          <div className="footer-subscribe-form-container">
             <SubscriptionForm />
          </div>
        </div>
      </section>

      {/* Section Spacer - Adjusted to be between Subscribe and Contact Info */}
      <div className="footer-section-spacer"></div>

      {/* Contact Info and Useful Links Section */}
      <section className={`footer-contact-section ${dosisFont.variable}`}>
        <div className="footer-contact-container">
          <div className="footer-contact-content">
            {/* Contact Info */}
            <div className="footer-contact-info">
              <h2 className="footer-contact-heading" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '15px', letterSpacing: '0.7px'}}>
                {translations.contact_info}
              </h2>
              <div className="footer-contact-details">
                <div className="footer-contact-item">
                  <span className="footer-contact-label" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>
                    {translations.contact_label}
                  </span>
                  <span className="footer-contact-value" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>+39 3452421202</span>
                </div>
                <div className="footer-contact-item">
                  <span className="footer-contact-label" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>
                    {translations.contact_label}
                  </span>
                  <span className="footer-contact-value" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>+39 3483594687</span>
                </div>
                <div className="footer-contact-item">
                  <span className="footer-contact-label" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>
                    {translations.email_label}
                  </span>
                  <span className="footer-contact-value" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}><EMAIL></span>
                </div>
                <div className="footer-contact-item">
                  <span className="footer-contact-label" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>
                    {translations.address_label}
                  </span>
                  <span className="footer-contact-value" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}>Bangkok, Turin, Italy</span>
                </div>
              </div>
            </div>

            {/* Useful Links */}
            <div className="footer-useful-links">
              <h2 className="footer-links-heading" style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '15px', letterSpacing: '0.7px'}}>
                {translations.useful_links}
              </h2>
              <div className="footer-links-list">
                {[
                  { text: translations.customer_review, href: '/reviews' },
                  { text: translations.privacy_policy, href: '/privacy-policy' },
                  { text: translations.returning_policy, href: '/returning-policy' },
                  { text: translations.terms_and_conditions, href: '/terms-and-conditions' }
                ].map((link, index) => (
                  <Link
                    key={index}
                    href={link.href}
                    className="footer-link-item relative group inline-block transition-all duration-150 ease-in-out active:scale-95 active:text-black"
                    style={{fontFamily: 'var(--font-dosis), sans-serif', fontSize: '13px', letterSpacing: '0.7px'}}
                  >
                    {link.text}
                    <span
                      className={`absolute bottom-0 left-0 w-full h-[1px] bg-black rounded-full transform transition-transform duration-300 ease-out ${
                        pathname === link.href ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
                      }`}
                    ></span>
                  </Link>
                ))}
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* Section Spacer - This might be adjusted or removed depending on final layout in RootLayout */}
      <div className="footer-section-spacer"></div>

      {/* Footer Section (Payment and Copyright) */}
      <footer className={`footer-main-section ${dosisFont.variable}`}>
        <div className="footer-main-container">
          {/* Social Media Icons Row */}
          <div className="footer-social-section">
            <div className="footer-social-heading-container">
              <h2 className="footer-social-heading" style={{fontFamily: 'var(--font-dosis), sans-serif'}}>
                {translations.follow_us}
              </h2>
            </div>
            <div className="footer-social-icons">
              <Link href="https://facebook.com" className="footer-social-icon facebook-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="7.08" height="13.22" viewBox="0 0 320 512" fill="white" className="social-icon-img">
                  <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
                </svg>
              </Link>
              <Link href="https://twitter.com" className="footer-social-icon twitter-icon">
                <Image
                  src="/images/icons/twitter-icon.svg"
                  alt="Twitter"
                  width={24}
                  height={24}
                  className="social-icon-img"
                />
              </Link>
              <Link href="https://instagram.com" className="footer-social-icon instagram-icon">
                <Image
                  src="/images/icons/instagram-icon.svg"
                  alt="Instagram"
                  width={24}
                  height={24}
                  className="social-icon-img"
                />
              </Link>
              <Link href="https://youtube.com" className="footer-social-icon youtube-icon">
                <Image
                  src="/images/icons/youtube-icon.svg"
                  alt="YouTube"
                  width={24}
                  height={24}
                  className="social-icon-img"
                />
              </Link>
            </div>
          </div>

          {/* Payment Icons */}
          <div className="footer-payment-icons">
            <Image 
              src="/images/payment-icons/visa-icon.svg" 
              alt="Visa" 
              width={48.01} 
              height={32.42}
            />
            <Image 
              src="/images/payment-icons/mastercard-icon.svg" 
              alt="Mastercard" 
              width={48.01} 
              height={32.42}
            />
            <Image 
              src="/images/payment-icons/paypal-icon.svg" 
              alt="PayPal" 
              width={48.01} 
              height={32.42}
            />
            <Image 
              src="/images/payment-icons/discover-icon.svg" 
              alt="Discover" 
              width={48.01} 
              height={32.42}
            />
            <Image 
              src="/images/payment-icons/amazon-pay-icon.svg" 
              alt="Amazon Pay" 
              width={48.01} 
              height={32.42}
            />
          </div>

          {/* Copyright Text */}
          <div className="footer-copyright-container">
            <p className="footer-copyright-text">
              {translations.copyright_text}
            </p>
          </div>
        </div>
      </footer>
    </>
  );
} 