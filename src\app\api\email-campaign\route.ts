import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import Campaign from '@/models/Campaign';
import connectDB from '@/lib/mongoose';

// Create a transporter with Gmail SMTP settings
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_APP_PASSWORD,
  },
});

export async function POST(request: NextRequest) {
  try {
    // Connect to the database
    await connectDB();

    // Parse the request body
    const { 
      subject, 
      content, 
      recipientGroup, 
      manualRecipients,
      scheduledDate,
      scheduledTime,
      sendNow,
      campaignType
    } = await request.json();

    // Validate the request
    if (campaignType === 'email' && !subject) {
      return NextResponse.json(
        { error: 'Subject is required for email campaigns' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      );
    }

    // Handle recipient determination
    let recipients: string[] = [];

    if (recipientGroup === 'manual') {
      if (!manualRecipients) {
        return NextResponse.json(
          { error: `Please provide at least one ${campaignType === 'email' ? 'email address' : 'phone number'} for manual sending` },
          { status: 400 }
        );
      }
      // Split the comma-separated list of emails or phone numbers
      recipients = manualRecipients.split(',').map((recipient: string) => recipient.trim());
      
      // Validate recipient format based on campaign type
      if (campaignType === 'email') {
        // Simple email validation
        const invalidEmails = recipients.filter(email => !validateEmail(email));
        if (invalidEmails.length > 0) {
          return NextResponse.json(
            { error: `Invalid email format for: ${invalidEmails.join(', ')}` },
            { status: 400 }
          );
        }
      } else {
        // Phone number validation
        const invalidPhones = recipients.filter(phone => !validatePhoneNumber(phone));
        if (invalidPhones.length > 0) {
          return NextResponse.json(
            { error: `Invalid phone number format for: ${invalidPhones.join(', ')}. Use E.164 format (e.g. +12025551234)` },
            { status: 400 }
          );
        }
      }
    } else {
      // Get recipients from database based on the selected group and campaign type
      recipients = await getRecipientsFromDatabase(recipientGroup, campaignType);
    }

    // Check if we have recipients
    if (recipients.length === 0) {
      return NextResponse.json(
        { error: 'No recipients found for the selected group' },
        { status: 400 }
      );
    }

    // Create a new campaign in the database
    const campaign = new Campaign({
      type: campaignType,
      subject: campaignType === 'email' ? subject : undefined,
      content,
      recipientGroup,
      recipients,
      status: sendNow ? 'sent' : 'scheduled',
      scheduledAt: !sendNow ? new Date(`${scheduledDate}T${scheduledTime}`) : undefined,
      sentAt: sendNow ? new Date() : null,
    });

    // Save the campaign
    await campaign.save();

    // If it's a scheduled campaign and not to be sent immediately
    if (!sendNow) {
      return NextResponse.json({ 
        success: true, 
        message: `Campaign scheduled for ${scheduledDate} at ${scheduledTime}`,
        campaignId: campaign._id
      });
    }

    // For campaigns to be sent immediately
    try {
      if (campaignType === 'email') {
        // Send email campaign
        await sendEmailCampaign(subject, content, recipients);
      } else {
        // For SMS campaigns, you would integrate with an SMS provider
        // This is a placeholder for that implementation
        await sendSmsCampaign(content, recipients);
      }

      // Update the campaign status to sent
      campaign.status = 'sent';
      campaign.sentAt = new Date();
      await campaign.save();

      return NextResponse.json({ 
        success: true, 
        message: `${campaignType === 'email' ? 'Email' : 'SMS'} campaign sent successfully to ${recipients.length} recipients`,
        campaignId: campaign._id
      });
    } catch (error) {
      // Update the campaign status to failed
      campaign.status = 'failed';
      await campaign.save();

      throw error; // Re-throw to be caught by the outer catch block
    }
  } catch (error) {
    console.error('Error sending campaign:', error);
    return NextResponse.json(
      { error: 'Failed to send campaign: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// Helper function to send email campaign
async function sendEmailCampaign(subject: string, content: string, recipients: string[]) {
  // For a real implementation, you might want to send emails in batches
  // Here we're sending them individually with BCC to hide recipient emails from each other
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    bcc: recipients.join(','),
    subject,
    // You can use HTML in the email content
    html: content,
  };

  await transporter.sendMail(mailOptions);
}

// Helper function to send SMS campaign (placeholder)
async function sendSmsCampaign(content: string, recipients: string[]) {
  // This would be implemented with an SMS provider like Twilio
  console.log(`SMS sending is not implemented yet. Would send to ${recipients.length} recipients`);
  // Throw an error for now since it's not implemented
  throw new Error('SMS sending is not implemented yet. Please use email campaigns for testing.');
}

// Function to get recipients from database based on group and campaign type
async function getRecipientsFromDatabase(recipientGroup: string, campaignType: string): Promise<string[]> {
  try {
    // Connect to the database
    await connectDB();

    // This is just a simplified example. In a real application, you would
    // query your customers collection based on the specified group criteria.

    // For a real implementation with separate email and phone queries:
    // const field = campaignType === 'email' ? 'email' : 'phoneNumber';
    // const optInField = campaignType === 'email' ? 'emailOptIn' : 'smsOptIn';
    // const customers = await Customer.find({ 
    //   ...getFilterForGroup(recipientGroup),
    //   [optInField]: true // Only include customers who have opted in for this type of marketing
    // }).select(field);
    // return customers.map(c => c[field]);

    // For now, return test data based on the group and campaign type
    if (campaignType === 'email') {
      switch(recipientGroup) {
        case 'all':
          return ['<EMAIL>'];
        case 'new':
          return ['<EMAIL>'];
        case 'active':
          return ['<EMAIL>'];
        case 'inactive':
          return ['<EMAIL>'];
        case 'vip':
          return ['<EMAIL>'];
        default:
          return ['<EMAIL>'];
      }
    } else {
      // Return phone numbers in E.164 format for SMS
      switch(recipientGroup) {
        case 'all':
          return ['+12025551001'];
        case 'new':
          return ['+12025551002'];
        case 'active':
          return ['+12025551003'];
        case 'inactive':
          return ['+12025551004'];
        case 'vip':
          return ['+12025551005'];
        default:
          return ['+12025551000'];
      }
    }
  } catch (error) {
    console.error('Error getting recipients:', error);
    return [];
  }
}

// Email validation function
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Phone number validation function (E.164 format)
function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
} 