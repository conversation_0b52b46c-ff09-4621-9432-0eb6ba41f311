import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Video bucket name from environment variables
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';

// Extract S3 key from CloudFront URL
const extractS3KeyFromUrl = (url: string): string | null => {
  // If it's a CloudFront URL, extract just the path part
  const cloudfrontDomain = process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN || '';
  if (cloudfrontDomain && url.includes(cloudfrontDomain)) {
    const urlObj = new URL(url);
    return urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
  }
  
  // If it's a direct S3 API URL
  if (url.includes('/api/test-s3-video/')) {
    return url.split('/api/test-s3-video/')[1];
  }
  
  // If we can't determine the key, return null
  return null;
};

/**
 * Get a specific hero video by ID
 * 
 * GET /api/hero-videos/[id]
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const heroVideo = await HeroVideo.findById(params.id);
    
    if (!heroVideo) {
      return NextResponse.json({
        success: false,
        error: 'Hero video not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      heroVideo
    });
  } catch (error: any) {
    console.error('Error fetching hero video:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch hero video'
    }, { status: 500 });
  }
}

/**
 * Update a specific hero video by ID
 * 
 * PUT /api/hero-videos/[id]
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const data = await request.json();
    const heroVideo = await HeroVideo.findById(params.id);
    
    if (!heroVideo) {
      return NextResponse.json({
        success: false,
        error: 'Hero video not found'
      }, { status: 404 });
    }
    
    // If changing active status to true, deactivate other videos of the same type
    if (data.isActive === true && data.isActive !== heroVideo.isActive) {
      await HeroVideo.updateMany(
        { 
          _id: { $ne: params.id },
          type: heroVideo.type,
          isActive: true 
        },
        { isActive: false }
      );
    }
    
    // Update hero video
    Object.assign(heroVideo, data);
    await heroVideo.save();
    
    return NextResponse.json({
      success: true,
      heroVideo,
      message: 'Hero video updated successfully'
    });
  } catch (error: any) {
    console.error('Error updating hero video:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update hero video'
    }, { status: 500 });
  }
}

/**
 * Delete a specific hero video by ID
 * 
 * DELETE /api/hero-videos/[id]
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const heroVideo = await HeroVideo.findById(params.id);
    
    if (!heroVideo) {
      return NextResponse.json({
        success: false,
        error: 'Hero video not found'
      }, { status: 404 });
    }
    
    // Try to delete the video file from S3
    try {
      // Extract the S3 key from the videoUrl
      const s3Key = extractS3KeyFromUrl(heroVideo.videoUrl);
      
      if (s3Key) {
        const client = getS3Client();
        const deleteCommand = new DeleteObjectCommand({
          Bucket: videosBucketName,
          Key: s3Key
        });
        
        await client.send(deleteCommand);
      }
    } catch (s3Error) {
      // Log the error but continue with deleting the database record
      console.error('Error deleting video file from S3:', s3Error);
    }
    
    // Delete the hero video from the database
    await HeroVideo.findByIdAndDelete(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Hero video deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting hero video:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete hero video'
    }, { status: 500 });
  }
} 