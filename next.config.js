const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: ['d3ldmxmv6paguv.cloudfront.net', 'd39l10jdryt7ww.cloudfront.net'],
  },
  // PERFORMANCE OPTIMIZATION: Webpack optimizations for smaller bundles
  webpack: (config, { isServer }) => {
    // Optimize AWS SDK imports - only include what we need
    config.resolve.alias = {
      ...config.resolve.alias,
      // Use lightweight CloudFront client for client-side
      '@aws-sdk/client-cloudfront': isServer ? '@aws-sdk/client-cloudfront' : false,
    };
    
    // Enable tree shaking for better bundle optimization (conservative approach)
    if (config.optimization) {
      config.optimization.usedExports = true;
      // sideEffects: false can break some packages, so we'll be more careful
    }
    
    // Split chunks more efficiently
    if (!isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          // Separate AWS SDK into its own chunk
          aws: {
            test: /[\\/]node_modules[\\/]@aws-sdk[\\/]/,
            name: 'aws-sdk',
            chunks: 'all',
            priority: 10,
          },
          // Separate video/media libraries
          media: {
            test: /[\\/]node_modules[\\/](hls\.js|video\.js|plyr)[\\/]/,
            name: 'media-libs',
            chunks: 'all',
            priority: 9,
          },
        },
      };
    }
    
    return config;
  },
  // PERFORMANCE OPTIMIZATION: Experimental features for better performance
  experimental: {
    // optimizeCss: true, // Temporarily disabled due to critters module issue
    // optimizeServerReact: true,
  },
}

module.exports = withBundleAnalyzer(nextConfig) 