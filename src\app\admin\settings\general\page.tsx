'use client';

import { useState, useEffect } from 'react';
import { Save, CreditCard, Truck, Store } from 'lucide-react';

interface StoreSettings {
  storeName: string;
  storeEmail: string;
  storePhone: string;
  storeCurrency: string;
  storeTimeZone: string;
  storeDefaultLanguage: string;
}

interface PaymentSettings {
  stripeEnabled: boolean;
  stripePublicKey: string;
  stripeSecretKey: string;
  paypalEnabled: boolean;
  paypalClientId: string;
  paypalSecretKey: string;
  cashOnDeliveryEnabled: boolean;
}

interface ShippingSettings {
  defaultShippingMethod: string;
  flatRateEnabled: boolean;
  flatRateAmount: number;
  freeShippingEnabled: boolean;
  freeShippingMinimumOrder: number;
  customShippingRatesEnabled: boolean;
}

interface Settings {
  store: StoreSettings;
  payment: PaymentSettings;
  shipping: ShippingSettings;
}

export default function GeneralSettingsPage() {
  const [activeTab, setActiveTab] = useState<'store' | 'payment' | 'shipping'>('store');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Store settings state
  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    storeName: '',
    storeEmail: '',
    storePhone: '',
    storeCurrency: 'USD',
    storeTimeZone: 'UTC',
    storeDefaultLanguage: 'en',
  });
  
  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    stripeEnabled: false,
    stripePublicKey: '',
    stripeSecretKey: '',
    paypalEnabled: false,
    paypalClientId: '',
    paypalSecretKey: '',
    cashOnDeliveryEnabled: true,
  });
  
  // Shipping settings state
  const [shippingSettings, setShippingSettings] = useState<ShippingSettings>({
    defaultShippingMethod: 'flat_rate',
    flatRateEnabled: true,
    flatRateAmount: 10,
    freeShippingEnabled: false,
    freeShippingMinimumOrder: 100,
    customShippingRatesEnabled: false,
  });
  
  // Fetch settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/admin/settings');
        
        if (!response.ok) {
          throw new Error('Failed to fetch settings');
        }
        
        const data = await response.json();
        
        // Update state with fetched data
        if (data.store) {
          setStoreSettings(data.store);
        }
        
        if (data.payment) {
          setPaymentSettings(data.payment);
        }
        
        if (data.shipping) {
          setShippingSettings(data.shipping);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
        setError('Failed to load settings. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSettings();
  }, []);
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setIsSaved(false);
    setError(null);
    
    try {
      // Prepare data for API
      const settings: Settings = {
        store: storeSettings,
        payment: paymentSettings,
        shipping: shippingSettings,
      };
      
      // Send update to API
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update settings');
      }
      
      // Show success message
      setIsSaved(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setIsSaved(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">General Settings</h1>
        <button
          onClick={handleSubmit}
          disabled={isLoading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <Save size={18} className="mr-2" />
              Save Changes
            </>
          )}
        </button>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {isSaved && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Settings saved successfully!
        </div>
      )}
      
      {/* Tabs */}
      <div className="border-b mb-6">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('store')}
            className={`pb-2 px-1 ${
              activeTab === 'store'
                ? 'border-b-2 border-blue-600 text-blue-600 font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <Store size={18} className="mr-2" />
              Store Settings
            </div>
          </button>
          <button
            onClick={() => setActiveTab('payment')}
            className={`pb-2 px-1 ${
              activeTab === 'payment'
                ? 'border-b-2 border-blue-600 text-blue-600 font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <CreditCard size={18} className="mr-2" />
              Payment Configuration
            </div>
          </button>
          <button
            onClick={() => setActiveTab('shipping')}
            className={`pb-2 px-1 ${
              activeTab === 'shipping'
                ? 'border-b-2 border-blue-600 text-blue-600 font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center">
              <Truck size={18} className="mr-2" />
              Shipping Configuration
            </div>
          </button>
        </div>
      </div>
      
      {/* Store Settings Tab */}
      {activeTab === 'store' && (
        <div className="bg-white rounded-md shadow p-6">
          <h2 className="text-lg font-medium mb-4">Store Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store Name
              </label>
              <input
                type="text"
                value={storeSettings.storeName}
                onChange={(e) => setStoreSettings({ ...storeSettings, storeName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Your Store Name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store Email
              </label>
              <input
                type="email"
                value={storeSettings.storeEmail}
                onChange={(e) => setStoreSettings({ ...storeSettings, storeEmail: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store Phone
              </label>
              <input
                type="text"
                value={storeSettings.storePhone}
                onChange={(e) => setStoreSettings({ ...storeSettings, storePhone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="+****************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Currency
              </label>
              <select
                value={storeSettings.storeCurrency}
                onChange={(e) => setStoreSettings({ ...storeSettings, storeCurrency: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
                <option value="AFN">Afghan Afghani (AFN)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time Zone
              </label>
              <select
                value={storeSettings.storeTimeZone}
                onChange={(e) => setStoreSettings({ ...storeSettings, storeTimeZone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time (ET)</option>
                <option value="America/Chicago">Central Time (CT)</option>
                <option value="America/Denver">Mountain Time (MT)</option>
                <option value="America/Los_Angeles">Pacific Time (PT)</option>
                <option value="Asia/Kabul">Afghanistan Time (AFT)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Default Language
              </label>
              <select
                value={storeSettings.storeDefaultLanguage}
                onChange={(e) => setStoreSettings({ ...storeSettings, storeDefaultLanguage: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="en">English</option>
                <option value="fr">French</option>
                <option value="es">Spanish</option>
                <option value="de">German</option>
                <option value="ps">Pashto</option>
                <option value="fa">Dari</option>
              </select>
            </div>
          </div>
        </div>
      )}
      
      {/* Payment Settings Tab */}
      {activeTab === 'payment' && (
        <div className="bg-white rounded-md shadow p-6">
          <h2 className="text-lg font-medium mb-4">Payment Methods</h2>
          
          {/* Stripe */}
          <div className="mb-6 border-b pb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <h3 className="font-medium">Stripe</h3>
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  Credit/Debit Cards
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={paymentSettings.stripeEnabled}
                  onChange={(e) => setPaymentSettings({ ...paymentSettings, stripeEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {paymentSettings.stripeEnabled && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Stripe Public Key
                  </label>
                  <input
                    type="text"
                    value={paymentSettings.stripePublicKey}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, stripePublicKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Stripe Secret Key
                  </label>
                  <input
                    type="password"
                    value={paymentSettings.stripeSecretKey}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, stripeSecretKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="sk_test_..."
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* PayPal */}
          <div className="mb-6 border-b pb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <h3 className="font-medium">PayPal</h3>
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  PayPal Balance
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={paymentSettings.paypalEnabled}
                  onChange={(e) => setPaymentSettings({ ...paymentSettings, paypalEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {paymentSettings.paypalEnabled && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PayPal Client ID
                  </label>
                  <input
                    type="text"
                    value={paymentSettings.paypalClientId}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, paypalClientId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Client ID"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PayPal Secret Key
                  </label>
                  <input
                    type="password"
                    value={paymentSettings.paypalSecretKey}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, paypalSecretKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Secret Key"
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Cash on Delivery */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <h3 className="font-medium">Cash on Delivery</h3>
                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  Offline Payment
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={paymentSettings.cashOnDeliveryEnabled}
                  onChange={(e) => setPaymentSettings({ ...paymentSettings, cashOnDeliveryEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      )}
      
      {/* Shipping Settings Tab */}
      {activeTab === 'shipping' && (
        <div className="bg-white rounded-md shadow p-6">
          <h2 className="text-lg font-medium mb-4">Shipping Methods</h2>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Default Shipping Method
            </label>
            <select
              value={shippingSettings.defaultShippingMethod}
              onChange={(e) => setShippingSettings({ ...shippingSettings, defaultShippingMethod: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="flat_rate">Flat Rate</option>
              <option value="free_shipping">Free Shipping</option>
              <option value="custom_rates">Custom Rates</option>
            </select>
          </div>
          
          {/* Flat Rate */}
          <div className="mb-6 border-b pb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Flat Rate Shipping</h3>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={shippingSettings.flatRateEnabled}
                  onChange={(e) => setShippingSettings({ ...shippingSettings, flatRateEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {shippingSettings.flatRateEnabled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Flat Rate Amount
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    value={shippingSettings.flatRateAmount}
                    onChange={(e) => setShippingSettings({ ...shippingSettings, flatRateAmount: parseFloat(e.target.value) })}
                    className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md"
                    placeholder="10.00"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Free Shipping */}
          <div className="mb-6 border-b pb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Free Shipping</h3>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={shippingSettings.freeShippingEnabled}
                  onChange={(e) => setShippingSettings({ ...shippingSettings, freeShippingEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {shippingSettings.freeShippingEnabled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Order Amount for Free Shipping
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    value={shippingSettings.freeShippingMinimumOrder}
                    onChange={(e) => setShippingSettings({ ...shippingSettings, freeShippingMinimumOrder: parseFloat(e.target.value) })}
                    className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md"
                    placeholder="100.00"
                    min="0"
                    step="0.01"
                  />
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  Set to 0 for unconditional free shipping
                </p>
              </div>
            )}
          </div>
          
          {/* Custom Shipping Rates */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Custom Shipping Rates</h3>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={shippingSettings.customShippingRatesEnabled}
                  onChange={(e) => setShippingSettings({ ...shippingSettings, customShippingRatesEnabled: e.target.checked })}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {shippingSettings.customShippingRatesEnabled && (
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  Custom shipping rates are configured based on weight, dimensions, and destination.
                </p>
                <button
                  type="button"
                  className="px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  Configure Custom Rates
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 