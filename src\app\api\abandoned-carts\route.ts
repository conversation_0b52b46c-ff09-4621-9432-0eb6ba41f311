import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import AbandonedCart from '@/models/AbandonedCart';

/**
 * Get all abandoned carts
 * 
 * GET /api/abandoned-carts
 */
export async function GET(request: Request) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const recovered = searchParams.get('recovered');
    const email = searchParams.get('email');
    const days = searchParams.get('days');
    const query: any = {};
    
    // Apply filters if provided
    if (recovered === 'true') {
      query.recovered = true;
    } else if (recovered === 'false') {
      query.recovered = false;
    }
    
    if (email) {
      query['customerInfo.email'] = { $regex: email, $options: 'i' };
    }
    
    // Filter by days (abandoned carts from the last X days)
    if (days && !isNaN(Number(days))) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - Number(days));
      query.createdAt = { $gte: daysAgo };
    }
    
    // Get all abandoned carts
    const abandonedCarts = await AbandonedCart.find(query).sort({ createdAt: -1 });
    
    // Return success response
    return NextResponse.json({
      success: true,
      count: abandonedCarts.length,
      carts: abandonedCarts
    });
  } catch (error: any) {
    console.error('Error retrieving abandoned carts:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to retrieve abandoned carts'
    }, { status: 500 });
  }
}

/**
 * Mark an abandoned cart as recovered or send recovery email
 * 
 * PATCH /api/abandoned-carts/:id
 */
export async function PATCH(request: Request) {
  try {
    // Parse the request body
    const { id, action } = await request.json();
    
    if (!id || !action) {
      return NextResponse.json({
        success: false,
        error: 'Cart ID and action are required'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    let updateData = {};
    
    // Handle different actions
    if (action === 'markRecovered') {
      updateData = { recovered: true };
    } else if (action === 'sendRecoveryEmail') {
      updateData = { 
        recoveryEmailSent: true, 
        recoveryEmailDate: new Date() 
      };
      // In a real implementation, would also send an actual email here
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }
    
    // Update the cart
    const updatedCart = await AbandonedCart.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );
    
    if (!updatedCart) {
      return NextResponse.json({
        success: false,
        error: 'Abandoned cart not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: `Cart ${action === 'markRecovered' ? 'marked as recovered' : 'recovery email sent'} successfully`,
      cart: updatedCart
    });
    
  } catch (error: any) {
    console.error('Error updating abandoned cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update abandoned cart'
    }, { status: 500 });
  }
} 