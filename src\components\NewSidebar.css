/* NewSidebar.css */

/* Override rc-slider default styles to match the desired look */

/* Slider rail - the light blue background */
.rc-slider-rail {
  background-color: #A8D1FF !important; /* Light blue, as per image */
  height: 4px !important;
  border-radius: 2px !important; /* Slightly rounded ends for the rail */
}

/* Slider track - the darker blue active range */
.rc-slider-track {
  background-color: #0D6EFD !important; /* Darker blue, as per image */
  height: 4px !important;
  border-radius: 2px !important; /* Slightly rounded ends for the track */
}

/* Slider handle - the circular thumbs */
.rc-slider-handle {
  background-color: white !important;
  border: 2px solid #0D6EFD !important; /* Blue border for handle */
  width: 18px !important;
  height: 18px !important;
  margin-top: -7px !important; /* Vertically center the handle on the track */
  opacity: 1 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important; /* Optional: subtle shadow for depth */
}

.rc-slider-handle:focus,
.rc-slider-handle:active {
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.2) !important; /* Focus ring, similar to Bootstrap */
  border-color: #0D6EFD !important;
}

/* Remove default box-shadow on hover/focus if not desired */
.rc-slider-handle:hover {
  border-color: #0056b3 !important; /* Darker blue on hover */
}

/* Ensure the containing div for the slider has some padding if needed */
/* The px-1 pt-2 pb-1 in NewSidebar.tsx on the wrapper div for Range helps with spacing */ 

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
} 