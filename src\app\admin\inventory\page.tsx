'use client';

import { useState, useEffect } from 'react';
import { Search, AlertTriangle, FileDown, Loader2, RefreshCw, Plus, Package, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

type Product = {
  _id: string;
  name: string;
  price: number;
  category: string | { _id: string; name: string };
  imageUrl?: string;
  inventory?: {
    inStock: number;
    lowStockThreshold: number;
  };
};

type CategoryData = {
  _id: string;
  name: string;
  subcategories: Array<{_id: string; name: string}>;
};

export default function InventoryPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<'all' | 'in-stock' | 'low-stock' | 'out-of-stock'>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);
  const [categories, setCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [updatingInventory, setUpdatingInventory] = useState<boolean>(false);
  const { user } = useAdminAuth();

  // Fetch products on component mount
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, []);

  // Filter and sort products when products, search query, or filters change
  useEffect(() => {
    filterProducts();
  }, [products, searchQuery, categoryFilter, stockFilter, sortBy, sortOrder]);

  // Update pagination when filtered products change
  useEffect(() => {
    setTotalPages(Math.ceil(filteredProducts.length / itemsPerPage));
    setPage(1); // Reset to first page when filters change
  }, [filteredProducts, itemsPerPage]);

  // Fetch products from the API
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/products');
      
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      
      const result = await response.json();
      
      // Check if the API returned a success response with products array
      if (!result.success || !result.products) {
        throw new Error('Invalid response format from API');
      }
      
      // Only add default inventory data for products that don't have it
      const productsWithInventory = result.products.map((product: Product) => {
        if (!product.inventory) {
          return {
            ...product,
            inventory: {
              inStock: 0,
              lowStockThreshold: 5
            }
          };
        }
        return product;
      });
      
      setProducts(productsWithInventory);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to load products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      
      // Ensure data is an array and extract unique category names safely
      if (Array.isArray(data)) {
        const categoryNames = [...new Set(
          data
            .filter(category => category && typeof category === 'object' && category.name)
            .map(category => String(category.name))
        )];
        setCategories(categoryNames);
      } else {
        console.error('Invalid categories data format:', data);
        setCategories([]);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setCategories([]); // Ensure categories is always an array of strings
    }
  };

  // Filter products based on search query and filters
  const filterProducts = () => {
    let filtered = [...products];
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(product => {
        const productCategoryName = typeof product.category === 'string' 
          ? product.category 
          : product.category?.name;
        return productCategoryName === categoryFilter;
      });
    }
    
    // Apply stock filter
    if (stockFilter !== 'all') {
      filtered = filtered.filter(product => {
        const inventory = product.inventory || { inStock: 0, lowStockThreshold: 5 };
        
        switch (stockFilter) {
          case 'in-stock':
            return inventory.inStock > inventory.lowStockThreshold;
          case 'low-stock':
            return inventory.inStock > 0 && inventory.inStock <= inventory.lowStockThreshold;
          case 'out-of-stock':
            return inventory.inStock === 0;
          default:
            return true;
        }
      });
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      if (sortBy === 'name') {
        return sortOrder === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === 'price') {
        return sortOrder === 'asc'
          ? a.price - b.price
          : b.price - a.price;
      } else if (sortBy === 'stock' || sortBy === 'inStock') {
        const aStock = a.inventory?.inStock || 0;
        const bStock = b.inventory?.inStock || 0;
        return sortOrder === 'asc' ? aStock - bStock : bStock - aStock;
      } else if (sortBy === 'category') {
        const aCategoryName = typeof a.category === 'string' ? a.category : (a.category?.name || '');
        const bCategoryName = typeof b.category === 'string' ? b.category : (b.category?.name || '');
        return sortOrder === 'asc'
          ? aCategoryName.localeCompare(bCategoryName)
          : bCategoryName.localeCompare(aCategoryName);
      }
      return 0;
    });
    
    setFilteredProducts(filtered);
  };

  // Get current products for the current page
  const getCurrentPageProducts = () => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredProducts.slice(startIndex, endIndex);
  };

  // Update inventory data for a product
  const updateInventory = async (productId: string, inventoryData: Product['inventory']) => {
    if (!inventoryData || !user) return;
    
    try {
      setUpdatingInventory(true);
      
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          inventory: inventoryData,
          userId: user._id,
          userName: `${user.firstName} ${user.lastName}`
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update inventory');
      }
      
      // Update local state
      setProducts(prevProducts => 
        prevProducts.map(product => 
          product._id === productId 
            ? { ...product, inventory: inventoryData }
            : product
        )
      );
      
      setEditingProductId(null);
    } catch (err) {
      console.error('Error updating inventory:', err);
      setError('Failed to update inventory. Please try again.');
    } finally {
      setUpdatingInventory(false);
    }
  };

  // Export inventory data as CSV
  const exportInventoryCSV = () => {
    // Create CSV content
    const headers = ['Product ID', 'Name', 'Category', 'In Stock', 'Low Stock Threshold'];
    const rows = products.map(product => [
      product._id,
      product.name,
      typeof product.category === 'string' ? product.category : (product.category?.name || 'Unknown Category'),
      product.inventory?.inStock || 0,
      product.inventory?.lowStockThreshold || 5
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create and download the CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `inventory-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Get stock status for a product
  const getStockStatus = (product: Product) => {
    const inventory = product.inventory || { inStock: 0, lowStockThreshold: 5 };
    
    if (inventory.inStock === 0) {
      return { status: 'Out of stock', color: 'text-red-600 bg-red-100 border-red-300' };
    } else if (inventory.inStock <= inventory.lowStockThreshold) {
      return { status: 'Low stock', color: 'text-amber-600 bg-amber-100 border-amber-300' };
    } else {
      return { status: 'In stock', color: 'text-green-600 bg-green-100 border-green-300' };
    }
  };

  // Handle edit mode for a product
  const handleEdit = (productId: string) => {
    setEditingProductId(productId);
  };

  // Handle saving inventory changes
  const handleSave = (product: Product) => {
    if (product.inventory) {
      updateInventory(product._id, product.inventory);
    }
  };

  // Handle cancel edit
  const handleCancel = () => {
    setEditingProductId(null);
  };

  // Function to handle inventory field changes with proper type definitions
  const handleInventoryChange = (
    productId: string, 
    field: 'inStock' | 'lowStockThreshold', 
    value: any
  ) => {
    setProducts(prevProducts => 
      prevProducts.map(product => {
        if (product._id !== productId) return product;
        
        return {
          ...product,
          inventory: {
            ...(product.inventory || { inStock: 0, lowStockThreshold: 5 }),
            [field]: parseInt(value) || 0
          }
        };
      })
    );
  };

  // Get current page items
  const getCurrentPageItems = () => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredProducts.slice(startIndex, endIndex);
  };

  // Pagination handlers
  const goToNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };
  
  const goToPrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        <div className="flex space-x-2">
          <button 
            onClick={fetchProducts}
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          <button 
            onClick={exportInventoryCSV}
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <FileDown size={16} className="mr-2" />
            Export CSV
          </button>
        </div>
      </div>
      
      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
          </div>
          
          {/* Category Filter */}
          <div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => {
                // Ensure category is a string, not an object
                const categoryName = typeof category === 'string' ? category : String(category?.name || category);
                return (
                  <option key={categoryName} value={categoryName}>{categoryName}</option>
                );
              })}
            </select>
          </div>
          
          {/* Stock Filter */}
          <div>
            <select
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Stock Levels</option>
              <option value="in-stock">In Stock</option>
              <option value="low-stock">Low Stock</option>
              <option value="out-of-stock">Out of Stock</option>
            </select>
          </div>
          
          {/* Sort By */}
          <div className="flex space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="name">Name</option>
              <option value="price">Price</option>
              <option value="category">Category</option>
              <option value="stock">Stock Level</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-100 rounded-md text-red-600 flex items-center">
          <AlertTriangle size={20} className="mr-2" />
          {error}
        </div>
      )}
      
      {/* Inventory Table */}
      {loading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 size={40} className="animate-spin text-blue-500" />
        </div>
      ) : (
        <>
          {filteredProducts.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <Package size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || categoryFilter !== 'all' || stockFilter !== 'all'
                  ? 'Try adjusting your filters to see more results.'
                  : 'Start by adding products to your inventory.'}
              </p>
              <Link 
                href="/admin/add-product"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Plus size={16} className="mr-2" />
                Add New Product
              </Link>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        In Stock
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Low Stock Threshold
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getCurrentPageProducts().map((product) => {
                      const stockStatus = getStockStatus(product);
                      const isEditing = editingProductId === product._id;
                      
                      return (
                        <tr key={product._id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {product.imageUrl ? (
                                <div className="h-10 w-10 flex-shrink-0 mr-3">
                                  <Image
                                    width={40}
                                    height={40}
                                    className="h-10 w-10 rounded-full object-cover"
                                    src={`${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${product.imageUrl}`}
                                    alt={product.name}
                                  />
                                </div>
                              ) : (
                                <div className="h-10 w-10 flex-shrink-0 mr-3 bg-gray-200 rounded-full flex items-center justify-center">
                                  <Package size={20} className="text-gray-500" />
                                </div>
                              )}
                              <div className="ml-2 truncate max-w-xs">
                                <div className="text-sm font-medium text-gray-900 truncate">{product.name}</div>
                                <div className="text-xs text-gray-500">ID: {product._id.substring(0, 8)}...</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {typeof product.category === 'string' 
                                ? product.category 
                                : (product.category?.name || 'Unknown Category')
                              }
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">${product.price.toFixed(2)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${stockStatus.color}`}>
                              {stockStatus.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {isEditing ? (
                              <input
                                type="number"
                                min="0"
                                value={product.inventory?.inStock || 0}
                                onChange={(e) => handleInventoryChange(product._id, 'inStock', e.target.value)}
                                className="w-20 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">{product.inventory?.inStock || 0}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {isEditing ? (
                              <input
                                type="number"
                                min="0"
                                value={product.inventory?.lowStockThreshold || 5}
                                onChange={(e) => handleInventoryChange(product._id, 'lowStockThreshold', e.target.value)}
                                className="w-20 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">{product.inventory?.lowStockThreshold || 5}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {isEditing ? (
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => {
                                    const productToUpdate = products.find(p => p._id === product._id);
                                    if (productToUpdate?.inventory) {
                                      updateInventory(product._id, productToUpdate.inventory);
                                    }
                                  }}
                                  disabled={updatingInventory}
                                  className={`px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors ${updatingInventory ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                  Save
                                </button>
                                <button
                                  onClick={() => setEditingProductId(null)}
                                  className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                                >
                                  Cancel
                                </button>
                              </div>
                            ) : (
                              <button
                                onClick={() => setEditingProductId(product._id)}
                                className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                              >
                                Update
                              </button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                  <div className="flex-1 flex justify-between items-center">
                    <button
                      onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                      disabled={page === 1}
                      className={`px-3 py-1 flex items-center rounded-md ${page === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
                    >
                      <ChevronLeft size={16} className="mr-1" />
                      Previous
                    </button>
                    <div className="text-sm text-gray-700">
                      Page <span className="font-medium">{page}</span> of <span className="font-medium">{totalPages}</span>
                    </div>
                    <button
                      onClick={() => setPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={page === totalPages}
                      className={`px-3 py-1 flex items-center rounded-md ${page === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
                    >
                      Next
                      <ChevronRight size={16} className="ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
} 