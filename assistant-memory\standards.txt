standards for font size, spacing, spacing between sections, spacing between name, price and button.

Now check the gap between elements for between name price and button, spacing, sizes 

that it is according to standards of professional ecommerce websites, standards of design systems, standards of web development and standards of tailwind css or not for default and sm screens, mobile screen, mobile first.

steps.
first analyze my whole project. check any already existence of any work related to this.
then make the plane that will word.
then implement the task.