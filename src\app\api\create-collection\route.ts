import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

/**
 * API route to create a new collection in MongoDB
 * 
 * POST /api/create-collection
 * 
 * Request body:
 * {
 *   name: string // Name of the collection to create
 * }
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const { name } = await request.json();
    
    // Validate collection name
    if (!name || typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'Please provide a valid collection name'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    try {
      // Get the MongoDB connection
      const connection = mongoose.connection;
      
      // Check if connection.db exists
      if (!connection.db) {
        return NextResponse.json({
          success: false,
          error: 'Database not initialized yet',
        }, { status: 500 });
      }
      
      // Create a new collection
      // In MongoDB, collections are created implicitly when first document is inserted,
      // but we'll use createCollection to explicitly create it
      await connection.db.createCollection(name);
      
      // Return success response
      return NextResponse.json({
        success: true,
        message: `Collection '${name}' created successfully`,
      });
    } catch (error: any) {
      // Handle specific MongoDB errors
      if (error.code === 48) {
        // Error code 48 means the collection already exists
        return NextResponse.json({
          success: false,
          error: `Collection '${name}' already exists`
        }, { status: 400 });
      }
      
      // Other database errors
      console.error('Error creating collection:', error);
      return NextResponse.json({
        success: false,
        error: error.message || 'Failed to create collection'
      }, { status: 500 });
    }
  } catch (error: any) {
    // Handle unexpected errors
    console.error('Unexpected error in create-collection API:', error);
    return NextResponse.json({
      success: false,
      error: 'An unexpected error occurred'
    }, { status: 500 });
  }
} 