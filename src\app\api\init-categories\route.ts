import { NextResponse } from 'next/server';
import { initializeCategories } from '@/lib/categoryInit';

export async function GET() {
  try {
    await initializeCategories();
    return NextResponse.json({ success: true, message: 'Categories initialized' });
  } catch (error) {
    console.error('Error initializing categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to initialize categories' },
      { status: 500 }
    );
  }
} 