import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { SystemLog } from '@/models/SystemLog';

// GET /api/admin/logs - Get paginated logs with optional filters
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    
    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const userId = url.searchParams.get('userId');
    const action = url.searchParams.get('action');
    const resource = url.searchParams.get('resource');
    const status = url.searchParams.get('status');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    
    // Build filter object
    const filter: any = {};
    
    if (userId) filter.userId = userId;
    if (action) filter.action = action;
    if (resource) filter.resource = resource;
    if (status) filter.status = status;
    
    // Add date range filter if provided
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const logs = await SystemLog.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await SystemLog.countDocuments(filter);
    
    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch logs' },
      { status: 500 }
    );
  }
}

// POST /api/admin/logs - Create a new log entry
export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.userId || !data.userName || !data.action || !data.resource) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Add IP address from request headers if not provided
    if (!data.ipAddress) {
      // Try standard headers first, then X-Forwarded-For
      const forwardedFor = req.headers.get('x-forwarded-for');
      data.ipAddress = forwardedFor ? forwardedFor.split(',')[0] : req.headers.get('x-real-ip') || 'unknown';
    }
    
    // Add user agent if not provided
    if (!data.userAgent) {
      data.userAgent = req.headers.get('user-agent') || 'unknown';
    }
    
    // Create the log entry
    // @ts-ignore - TypeScript doesn't recognize the static method
    const log = await SystemLog.logActivity(data);
    
    return NextResponse.json(log, { status: 201 });
  } catch (error) {
    console.error('Error creating log entry:', error);
    return NextResponse.json(
      { error: 'Failed to create log entry' },
      { status: 500 }
    );
  }
} 