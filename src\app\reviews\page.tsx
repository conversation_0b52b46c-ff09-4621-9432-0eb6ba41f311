'use client';

import React from 'react';
import localFont from 'next/font/local';

// Load Dosis font from downloaded fonts
const dosisFont = localFont({
  src: '../../../downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf',
  variable: '--font-dosis',
  display: 'swap',
  weight: '400 700'
});

export default function ReviewsPage() {
  return (
    <div className={`min-h-screen ${dosisFont.variable}`} style={{ backgroundColor: '#f8f8f8' }}>
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8 sm:py-12 md:py-16">
        <h1 className="text-3xl sm:text-4xl font-bold mb-6 sm:mb-8 text-center" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>Customer Reviews</h1>
        <div className="p-6 sm:p-8 min-h-[300px]" style={{ backgroundColor: '#f8f8f8' }}>
          <p className="text-lg text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            This page is under construction. Check back soon for customer reviews!
          </p>
          {/* Placeholder for future review components */}
        </div>
      </div>
    </div>
  );
}