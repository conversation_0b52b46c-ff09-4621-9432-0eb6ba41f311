import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';
import Order from '@/models/Order';

export async function GET() {
  try {
    await dbConnect();
    
    // Fetch all customers, newest first
    const customers = await Customer.find({})
      .sort({ createdAt: -1 })
      .lean();
    
    // Fetch all orders to count by customer email
    const orders = await Order.find({}).lean();
    
    // Map of customer email to order count
    const orderCountsByEmail = orders.reduce((counts, order) => {
      const email = order.customer?.email?.toLowerCase();
      if (email) {
        counts[email] = (counts[email] || 0) + 1;
      }
      return counts;
    }, {} as Record<string, number>);
    
    // Add order count to each customer
    const customersWithOrderCounts = customers.map(customer => {
      const email = customer.email.toLowerCase();
      return {
        ...customer,
        orderCount: orderCountsByEmail[email] || 0
      };
    });
    
    return NextResponse.json(customersWithOrderCounts);
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
} 