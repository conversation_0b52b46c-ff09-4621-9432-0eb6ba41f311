import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Discount from '@/models/Discount';

export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    
    const data = await req.json();
    const { code, cartTotal } = data;
    
    // Validate input
    if (!code) {
      return NextResponse.json(
        { error: 'Discount code is required' },
        { status: 400 }
      );
    }
    
    // Find the discount by code (case insensitive)
    const discount = await Discount.findOne({ 
      code: { $regex: new RegExp(`^${code}$`, 'i') },
      status: 'active'
    });
    
    // If discount not found or inactive
    if (!discount) {
      return NextResponse.json(
        { error: 'Invalid or expired discount code' },
        { status: 404 }
      );
    }
    
    // Check if discount has expired
    const currentDate = new Date();
    if (new Date(discount.startDate) > currentDate || new Date(discount.endDate) < currentDate) {
      return NextResponse.json(
        { error: 'This discount code has expired' },
        { status: 400 }
      );
    }
    
    // Check if discount has reached max usage
    if (discount.maxUses > 0 && discount.usedCount >= discount.maxUses) {
      return NextResponse.json(
        { error: 'This discount code has reached its usage limit' },
        { status: 400 }
      );
    }
    
    // Check minimum purchase requirement
    if (discount.minPurchase > 0 && cartTotal < discount.minPurchase) {
      return NextResponse.json(
        { 
          error: `This discount requires a minimum purchase of $${discount.minPurchase.toFixed(2)}`,
          minPurchase: discount.minPurchase
        },
        { status: 400 }
      );
    }
    
    // Calculate discount amount
    let discountAmount = 0;
    if (discount.type === 'percentage') {
      discountAmount = (cartTotal * discount.value) / 100;
    } else { // fixed amount
      discountAmount = discount.value;
    }
    
    // Ensure discount doesn't exceed cart total
    discountAmount = Math.min(discountAmount, cartTotal);
    
    // Return discount information
    return NextResponse.json({
      success: true,
      discount: {
        _id: discount._id,
        code: discount.code,
        type: discount.type,
        value: discount.value,
        discountAmount: discountAmount,
        description: discount.description
      }
    });
    
  } catch (error) {
    console.error('Error verifying discount:', error);
    return NextResponse.json(
      { error: 'Failed to verify discount code' },
      { status: 500 }
    );
  }
} 