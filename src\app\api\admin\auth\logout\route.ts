import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // For a more robust solution, we could clear cookies here if using cookie-based auth
    
    // Simply return success as the actual logout happens on the client side
    // by clearing localStorage in the AdminAuthContext
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Admin logout error:', error);
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    );
  }
} 