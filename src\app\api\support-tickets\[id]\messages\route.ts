import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import dbConnect from '@/lib/dbConnect';
import { SupportTicket } from '@/models/SupportTicket';

interface TicketDocument {
  _id: string;
  messages: Array<{
    sender: string;
    message: string;
    timestamp: Date;
    attachmentUrl?: string;
  }>;
  [key: string]: any;
}

/**
 * GET /api/support-tickets/[id]/messages
 * Retrieve all messages for a specific support ticket
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const id = params.id;
    
    // Validate if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid ticket ID format' },
        { status: 400 }
      );
    }
    
    // Find the ticket
    const ticket = await SupportTicket.findById(id).lean() as TicketDocument | null;
    
    // Check if ticket exists
    if (!ticket) {
      return NextResponse.json(
        { error: 'Support ticket not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(ticket.messages || []);
  } catch (error) {
    console.error('Error fetching ticket messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ticket messages' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/support-tickets/[id]/messages
 * Add a new message to a support ticket
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const id = params.id;
    
    // Validate if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid ticket ID format' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.message || !body.sender) {
      return NextResponse.json(
        { error: 'Message and sender are required' },
        { status: 400 }
      );
    }
    
    // Validate sender type
    if (!['customer', 'admin'].includes(body.sender)) {
      return NextResponse.json(
        { error: 'Sender must be either "customer" or "admin"' },
        { status: 400 }
      );
    }
    
    // Create new message
    const newMessage = {
      sender: body.sender,
      message: body.message,
      timestamp: new Date(),
      attachmentUrl: body.attachmentUrl || null
    };
    
    // Find ticket and push new message
    const updatedTicket = await SupportTicket.findByIdAndUpdate(
      id,
      { 
        $push: { messages: newMessage },
        lastUpdated: new Date(),
        // If admin replies, set status to waiting-for-customer
        // If customer replies, set status to open
        status: body.sender === 'admin' ? 'waiting-for-customer' : 'open'
      },
      { new: true, runValidators: true }
    );
    
    // Check if ticket exists
    if (!updatedTicket) {
      return NextResponse.json(
        { error: 'Support ticket not found' },
        { status: 404 }
      );
    }
    
    // Send email notification when admin replies to customer
    if (body.sender === 'admin') {
      try {
        // In a real implementation, this would send an actual email
        // This is just a placeholder for the email sending logic
        console.log('Sending email notification to customer:', updatedTicket.customer.email);
        console.log('Email subject: Update on your support ticket #' + updatedTicket.ticketNumber);
        console.log('Email body: A member of our support team has replied to your ticket. Please log in to your account to view the response and reply if needed.');
        
        // Here you would typically call your email service
        // await sendEmail({
        //   to: updatedTicket.customer.email,
        //   subject: `Update on your support ticket #${updatedTicket.ticketNumber}`,
        //   text: `A member of our support team has replied to your ticket. Please log in to your account to view the response and reply if needed.`,
        //   html: `<p>A member of our support team has replied to your ticket.</p>
        //          <p>Please <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account/tickets">log in to your account</a> to view the response and reply if needed.</p>`
        // });
      } catch (emailError) {
        // Log email error but don't fail the request
        console.error('Error sending notification email:', emailError);
      }
    }
    
    return NextResponse.json(
      { message: 'Message added successfully', ticket: updatedTicket },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error adding message to ticket:', error);
    return NextResponse.json(
      { error: 'Failed to add message to ticket' },
      { status: 500 }
    );
  }
} 