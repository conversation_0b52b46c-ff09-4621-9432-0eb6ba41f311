'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Clock, ShoppingBag, Calendar, Heart, MapPin, Mail, Phone } from 'lucide-react';

interface Order {
  _id: string;
  orderNumber: string;
  total: number;
  status: string;
  createdAt: string;
  items: {
    productId: string;
    name: string;
    price: number;
    quantity: number;
  }[];
}

interface Product {
  _id: string;
  name: string;
  price: number;
  imageUrl?: string;
  description: string;
}

interface Customer {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  address: {
    street: string;
    city: string;
    province: string;
    country: string;
    postalCode: string;
  };
  createdAt: string;
  orders: Order[];
  wishlist: Product[];
}

export default function CustomerDetails() {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('info');
  const params = useParams();
  const router = useRouter();
  const customerId = params.id as string;

  useEffect(() => {
    if (customerId) {
      fetchCustomerDetails(customerId);
    }
  }, [customerId]);

  const fetchCustomerDetails = async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/customers/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch customer details');
      }
      
      const data = await response.json();
      setCustomer(data);
    } catch (err) {
      setError('Error loading customer details. Please try again.');
      console.error('Error fetching customer details:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getOrderStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'canceled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="container mx-auto px-4 py-10 text-center">
        <p className="text-red-500 mb-4">{error || 'Customer not found'}</p>
        <button 
          onClick={() => router.push('/admin/customers/view')}
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Customers
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.push('/admin/customers/view')}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-2xl font-bold">Customer Details</h1>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Customer header */}
        <div className="bg-blue-50 p-6 border-b">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-xl font-semibold">{customer.firstName} {customer.lastName}</h2>
              <div className="flex items-center mt-2 text-gray-600">
                <Calendar size={16} className="mr-2" />
                <span>Customer since {formatDate(customer.createdAt)}</span>
              </div>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="flex items-center">
                <Mail size={16} className="mr-2 text-gray-500" />
                <span>{customer.email}</span>
              </div>
              <div className="flex items-center mt-2">
                <Phone size={16} className="mr-2 text-gray-500" />
                <span>{customer.phone}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="border-b">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('info')}
              className={`px-6 py-4 text-sm font-medium ${
                activeTab === 'info'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Basic Information
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`px-6 py-4 text-sm font-medium ${
                activeTab === 'orders'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Order History
            </button>
            <button
              onClick={() => setActiveTab('wishlist')}
              className={`px-6 py-4 text-sm font-medium ${
                activeTab === 'wishlist'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Wishlist
            </button>
          </nav>
        </div>
        
        {/* Tab content */}
        <div className="p-6">
          {activeTab === 'info' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Email Address</p>
                  <p>{customer.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Phone Number</p>
                  <p>{customer.phone}</p>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold mt-8 mb-4">Address</h3>
              <div className="bg-gray-50 p-4 rounded-lg border">
                <div className="flex items-start">
                  <MapPin size={18} className="mr-2 mt-1 text-gray-500" />
                  <div>
                    <p>{customer.address.street}</p>
                    <p>{customer.address.city}, {customer.address.province} {customer.address.postalCode}</p>
                    <p>{customer.address.country}</p>
                  </div>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold mt-8 mb-4">Account Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <ShoppingBag size={20} className="mr-3 text-blue-600" />
                    <div>
                      <p className="text-2xl font-bold">{customer.orders?.length || 0}</p>
                      <p className="text-sm text-gray-600">Total Orders</p>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Heart size={20} className="mr-3 text-purple-600" />
                    <div>
                      <p className="text-2xl font-bold">{customer.wishlist?.length || 0}</p>
                      <p className="text-sm text-gray-600">Wishlist Items</p>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Clock size={20} className="mr-3 text-green-600" />
                    <div>
                      <p className="text-2xl font-bold">{Math.floor((new Date().getTime() - new Date(customer.createdAt).getTime()) / (1000 * 60 * 60 * 24))} days</p>
                      <p className="text-sm text-gray-600">Customer Age</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'orders' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Order History</h3>
              
              {customer.orders && customer.orders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                        <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {customer.orders.map((order) => (
                        <tr key={order._id}>
                          <td className="py-3 px-4 whitespace-nowrap">
                            <span>{order.orderNumber || order._id.substring(0, 8)}</span>
                          </td>
                          <td className="py-3 px-4 whitespace-nowrap">
                            {formatDate(order.createdAt)}
                          </td>
                          <td className="py-3 px-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getOrderStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="py-3 px-4 whitespace-nowrap">
                            ${order.total.toFixed(2)}
                          </td>
                          <td className="py-3 px-4 whitespace-nowrap">
                            {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                          </td>
                          <td className="py-3 px-4 whitespace-nowrap">
                            <Link 
                              href={`/admin/orders/${order._id}`}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              View Details
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <ShoppingBag size={32} className="mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500">This customer hasn't placed any orders yet.</p>
                </div>
              )}
            </div>
          )}
          
          {activeTab === 'wishlist' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Wishlist</h3>
              
              {customer.wishlist && customer.wishlist.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {customer.wishlist.map((product) => (
                    <div key={product._id} className="border rounded-lg overflow-hidden">
                      <div className="h-48 relative">
                        {product.imageUrl ? (
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            fill
                            style={{ objectFit: 'cover' }}
                          />
                        ) : (
                          <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-400">No Image</span>
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <h4 className="font-medium mb-2">{product.name}</h4>
                        <p className="text-gray-500 text-sm mb-3 line-clamp-2">{product.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="font-bold">${product.price.toFixed(2)}</span>
                          <Link 
                            href={`/admin/products/${product._id}`}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            View Product
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <Heart size={32} className="mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500">This customer hasn't added any products to their wishlist yet.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 