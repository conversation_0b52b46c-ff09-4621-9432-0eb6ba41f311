.latest-products-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .latest-products-section {
        padding-top: 3rem;
        padding-bottom: 3rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .latest-products-section {
        padding-top: 4rem;
        padding-bottom: 4rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.latest-products-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
}

.latest-products-heading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
}

@media (min-width: 640px) {
    .latest-products-heading-container {
        margin-bottom: 3rem;
    }
}

.latest-products-heading {
    font-family: var(--font-dosis), sans-serif;
    font-size: 25px;
    font-weight: 500;
    line-height: 36px;
    letter-spacing: 1.25px;
    color: rgb(0, 0, 0);
    text-align: center;
    margin-bottom: 0.75rem;
    text-transform: capitalize;
}

@media (min-width: 640px) {
    .latest-products-heading {
        font-size: 1.875rem;
    }
}



.latest-products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media (min-width: 640px) {
    .latest-products-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }
}

@media (min-width: 1024px) {
    .latest-products-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

.latest-products-card {
    grid-column: span 1;
}

/* Empty state styling */
.latest-products-grid .col-span-full {
    grid-column: 1 / -1;
    padding: 3rem 1rem;
}

.latest-products-grid .col-span-full p {
    font-family: var(--font-dosis), sans-serif;
    font-size: 18px;
    letter-spacing: 0.5px;
}

