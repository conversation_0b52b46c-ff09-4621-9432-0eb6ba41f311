import mongoose from 'mongoose';

/**
 * Global variable to cache the database connection
 * This prevents multiple connections in development with hot reload
 */
interface ConnectionCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// Initialize the connection cache
const globalForMongoose = global as unknown as {
  mongoose: ConnectionCache;
};

if (!globalForMongoose.mongoose) {
  globalForMongoose.mongoose = { conn: null, promise: null };
}

/**
 * MongoDB connection string - should be stored in environment variables for security
 * Format: mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
 */
const MONGODB_URI = process.env.MONGODB_URI || '*************************************************************************************************************';

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}

/**
 * Function to connect to MongoDB
 * Returns information about the connection status and any errors
 */
export async function connectToDatabase() {
  if (globalForMongoose.mongoose.conn) {
    return { 
      db: globalForMongoose.mongoose.conn,
      status: 'connected',
      error: null
    };
  }

  if (!globalForMongoose.mongoose.promise) {
    const opts = {
      bufferCommands: false,
    };

    globalForMongoose.mongoose.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }

  try {
    globalForMongoose.mongoose.conn = await globalForMongoose.mongoose.promise;
    return { 
      db: globalForMongoose.mongoose.conn,
      status: 'connected',
      error: null
    };
  } catch (e) {
    globalForMongoose.mongoose.promise = null;
    return {
      db: null,
      status: 'error',
      error: e
    };
  }
}

/**
 * Function to get database information including the database name
 * Returns information about the connected database
 */
export async function getDatabaseInfo() {
  try {
    // First connect to the database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return {
        status: 'error',
        error: dbConnection.error,
        dbInfo: null
      };
    }
    
    // Get the mongoose connection
    const connection = mongoose.connection;
    
    // Check if connection.db exists before accessing it
    if (!connection.db) {
      return {
        status: 'error',
        error: {
          message: 'Database not initialized',
          name: 'ConnectionError',
          explanation: 'Connected to MongoDB server but database is not initialized yet.'
        },
        dbInfo: null
      };
    }
    
    // Extract database information
    const dbInfo = {
      name: connection.db.databaseName,
      collections: await connection.db.listCollections().toArray(),
      host: connection.host,
      port: connection.port,
      readyState: connection.readyState === 1 ? 'connected' : 'disconnected'
    };
    
    return {
      status: 'connected',
      error: null,
      dbInfo
    };
  } catch (error: any) {
    console.error('Error getting database information:', error);
    
    return {
      status: 'error',
      error: {
        message: error.message,
        code: error.code,
        name: error.name,
        explanation: 'Failed to get database information. The connection might be established but we could not retrieve database details.'
      },
      dbInfo: null
    };
  }
}

/**
 * Function to provide user-friendly explanations for common MongoDB connection errors
 */
function getErrorExplanation(error: any): string {
  // Authentication failed
  if (error.message.includes('Authentication failed')) {
    return 'The username or password in your MongoDB connection string is incorrect.';
  }
  
  // Connection timeout
  if (error.message.includes('timed out')) {
    return 'The connection to MongoDB timed out. This could be due to network issues or firewall restrictions.';
  }
  
  // Invalid connection string
  if (error.message.includes('Invalid connection string')) {
    return 'The MongoDB connection string format is invalid. Please check the format.';
  }
  
  // Server not found
  if (error.message.includes('getaddrinfo ENOTFOUND')) {
    return 'The MongoDB server could not be found. The hostname may be incorrect or the server is down.';
  }
  
  // Connection refused
  if (error.message.includes('ECONNREFUSED')) {
    return 'The connection was refused. The MongoDB server might not be running or is not accessible.';
  }
  
  // Default explanation
  return 'An error occurred while connecting to the database. Please check your connection settings and try again.';
} 