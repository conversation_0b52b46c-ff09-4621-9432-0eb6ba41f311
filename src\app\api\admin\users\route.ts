import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';

// GET /api/admin/users - Get all users
export async function GET() {
  try {
    await dbConnect();
    
    const users = await User.find()
      .select('-password') // Exclude password field
      .populate('role', 'name') // Only include role name
      .sort({ createdAt: -1 })
      .lean();
    
    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create a new user
export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const userData = await req.json();
    
    // Validate required fields
    if (!userData.email || !userData.password || !userData.firstName || !userData.lastName || !userData.role) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if email already exists
    const existingUser = await User.findOne({ email: userData.email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { error: 'A user with this email already exists' },
        { status: 409 }
      );
    }
    
    // Create the user
    const newUser = new User(userData);
    await newUser.save();
    
    // Return user without password
    const userWithoutPassword = newUser.toObject();
    delete userWithoutPassword.password;
    
    return NextResponse.json(userWithoutPassword, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
} 