import { NextResponse } from 'next/server';
import { checkS3BucketConnection, S3ConnectionStatusResponse } from '@/lib/s3';

/**
 * API route to check S3 bucket connections
 * 
 * GET /api/check-s3-connections
 */
export async function GET() {
  try {
    // Define bucket names - In production these should come from environment variables
    const imagesBucketName = process.env.S3_IMAGES_BUCKET || 'afghangemstones-images';
    const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'afghangemstones-videos';
    
    // Check connections in parallel
    const [imagesConnectionStatus, videosConnectionStatus] = await Promise.all([
      checkS3BucketConnection(imagesBucketName),
      checkS3BucketConnection(videosBucketName)
    ]);
    
    // Return results
    return NextResponse.json({
      images: imagesConnectionStatus,
      videos: videosConnectionStatus
    });
  } catch (error: any) {
    // Handle unexpected errors
    console.error('Unexpected error in check-s3-connections API:', error);
    return NextResponse.json({
      error: 'An unexpected error occurred while checking S3 bucket connections',
      details: error.message
    }, { status: 500 });
  }
} 