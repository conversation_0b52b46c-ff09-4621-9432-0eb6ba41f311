import { connectToDatabase } from './mongodb';
import { Category } from '@/models/Category';
import { Product } from '@/models/Product';
import mongoose from 'mongoose';

export async function seedProducts() {
  try {
    await connectToDatabase();
    
    // Check if products already exist
    const productCount = await Product.countDocuments();
    
    if (productCount > 0) {
      console.log('Products already exist, skipping seed...');
      return;
    }
    
    console.log('Fetching categories for product seeding...');
    
    // Get all categories first
    const categories = await Category.find();
    
    if (categories.length === 0) {
      console.log('No categories found. Please seed categories first.');
      return;
    }
    
    console.log(`Found ${categories.length} categories. Creating sample products...`);
    
    // Sample product data for each category
    const productPromises = [];
    
    // Loop through categories to create products
    for (const category of categories) {
      // Create category-level products
      const categoryProductData = {
        name: `${category.name} Collection`,
        description: `High-quality ${category.name.toLowerCase()} sourced from Afghanistan.`,
        price: Math.floor(Math.random() * 500) + 100,
        category: category._id,
        images: [
          '/images/sample/product1.jpg',
          '/images/sample/product2.jpg',
        ],
        inventory: {
          inStock: Math.floor(Math.random() * 20) + 5,
          lowStockThreshold: 3
        }
      };
      
      productPromises.push(Product.create(categoryProductData));
      
      // Create products for each subcategory
      if (category.subcategories && category.subcategories.length > 0) {
        for (const subcategory of category.subcategories) {
          const subcategoryProductData = {
            name: `${subcategory.name} ${category.name}`,
            description: `Premium ${subcategory.name} from our ${category.name.toLowerCase()} collection.`,
            price: Math.floor(Math.random() * 300) + 50,
            category: category._id,
            subcategory: subcategory._id,
            images: [
              '/images/sample/product3.jpg',
              '/images/sample/product4.jpg',
            ],
            inventory: {
              inStock: Math.floor(Math.random() * 15) + 2,
              lowStockThreshold: 2
            }
          };
          
          productPromises.push(Product.create(subcategoryProductData));
          
          // Add a second product for each subcategory
          const secondSubcategoryProduct = {
            name: `${subcategory.name} Special Edition`,
            description: `Special edition ${subcategory.name.toLowerCase()} with unique features and premium quality.`,
            price: Math.floor(Math.random() * 800) + 200,
            category: category._id,
            subcategory: subcategory._id,
            images: [
              '/images/sample/product5.jpg',
              '/images/sample/product6.jpg',
            ],
            inventory: {
              inStock: Math.floor(Math.random() * 5) + 1,
              lowStockThreshold: 1
            }
          };
          
          productPromises.push(Product.create(secondSubcategoryProduct));
        }
      }
    }
    
    // Wait for all products to be created
    await Promise.all(productPromises);
    
    const finalCount = await Product.countDocuments();
    console.log(`Successfully seeded ${finalCount} products.`);
    
  } catch (error) {
    console.error('Error seeding products:', error);
  }
}

// Call this function from an API route or script to seed the database
// seedProducts(); 