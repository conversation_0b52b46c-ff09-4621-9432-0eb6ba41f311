/* ProductDetail.css - Typography styles from Figma design */

/* Product Name - Dosis 400, 24px, 5% letter-spacing */
#product-name {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 5%;
}

/* Weight Text - Dosis 400, 16px, 7% letter-spacing */
#product-weight {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 400;
  font-size: 16px;
  letter-spacing: 7%;
}

/* Price Label - Dosis 500, 20px, 3% letter-spacing */
#price-label {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 3%;
  text-transform: capitalize;
}

/* Price Value - Dosis 500, 20px, 0% letter-spacing */
#product-price {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0%;
}

/* Quantity Label - Dosis 400, 20px, 5% letter-spacing */
#product-quantity-heading {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 400;
  font-size: 20px;
  letter-spacing: 5%;
}

/* Two-column layout structure - only on mobile devices */
.product-info-two-column {
  /* Default: vertical layout for tablet and laptop */
  display: block;
}

#product-info-left {
  width: 100%;
}

#product-info-right {
  width: 100%;
}

/* Two-column layout for screens below 768px */
@media (max-width: 768px) {
  .product-info-two-column {
    max-width: 356px; /* Total width from Figma design */
    display: grid;
    grid-template-columns: 171fr 119fr; /* Maintain exact proportions from Figma */
    gap: 66px; /* Gap calculated: 237px - 171px = 66px */
  }
  
  #product-info-left {
    /* Grid item - no width needed */
  }
  
  #product-info-right {
    /* Grid item - no width needed */
    padding-top: 27px;
  }
  
  #product-quantity-heading {
    text-align: center;
  }
}

/* Responsive grid for very small screens */
@media (max-width: 356px) {
  .product-info-two-column {
    max-width: 100%;
    display: grid;
    grid-template-columns: 1.44fr 1fr; /* Maintain proportion ratio (171/119 = 1.44) */
    gap: min(66px, 15vw); /* Responsive gap that scales with screen size */
  }
  
  #product-info-right {
    padding-top: 27px;
  }
}

/* Extra small screens - stack vertically if needed */
@media (max-width: 280px) {
  .product-info-two-column {
    display: grid;
    grid-template-columns: 1fr; /* Single column */
    gap: 20px;
  }
  
  #product-info-right {
    padding-top: 0; /* Remove top padding when stacked */
  }
  
  #product-quantity-heading {
    text-align: left; /* Align left when stacked */
  }
} 