'use client';

import { useState, useEffect } from 'react';
import { Bell, CheckCircle, Clock, AlertTriangle, Settings, Users, ShoppingBag, Tag, Save } from 'lucide-react';

export default function PushNotificationsPage() {
  // Tab state
  const [activeTab, setActiveTab] = useState('order'); // 'order', 'promotional', 'settings', 'send', 'tokens'
  
  // Notification types
  const [notificationSettings, setNotificationSettings] = useState({
    // Order notifications
    orderPlaced: true,
    orderConfirmed: true,
    orderShipped: true,
    orderDelivered: true,
    orderCancelled: true,
    
    // Promotional notifications
    newProducts: false,
    priceDrops: true,
    flashSales: true,
    couponExpiration: true,
    backInStock: true,
    
    // User activity notifications
    accountCreated: true,
    passwordChanged: true,
    reviewPosted: false,
    wishlistUpdates: false,
  });

  // Template messages
  const [notificationTemplates, setNotificationTemplates] = useState({
    orderPlaced: 'Your order #{{orderNumber}} has been placed successfully!',
    orderConfirmed: 'Good news! Your order #{{orderNumber}} has been confirmed.',
    orderShipped: 'Your order #{{orderNumber}} has been shipped. Track it here: {{trackingLink}}',
    orderDelivered: 'Your order #{{orderNumber}} has been delivered!',
    orderCancelled: 'Your order #{{orderNumber}} has been cancelled. Contact support for assistance.',
    
    newProducts: 'Check out our new arrivals in {{category}}!',
    priceDrops: 'Price drop alert! Items in your wishlist are now on sale.',
    flashSales: 'Flash Sale! 24 hours only - Get up to 50% off select items.',
    couponExpiration: 'Your coupon {{couponCode}} is about to expire in {{days}} days!',
    backInStock: '{{productName}} is back in stock! Get it before it\'s gone again.',
    
    accountCreated: 'Welcome to Afghan Gems! Your account has been created successfully.',
    passwordChanged: 'Your password has been changed successfully.',
    reviewPosted: 'Thank you for your review! It helps other customers.',
    wishlistUpdates: 'An item in your wishlist is now on sale!',
  });

  // Settings
  const [scheduleSettings, setScheduleSettings] = useState({
    deliveryTime: '11:00',
    maxNotificationsPerDay: 3,
    maxPromotionalPerWeek: 5,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    enableQuietHours: true,
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add state for notification sending
  const [notification, setNotification] = useState({
    type: 'order',
    subType: 'orderPlaced',
    title: '',
    body: '',
    data: {},
    recipientGroup: 'all',
    manualRecipients: '',
    scheduledDate: new Date().toISOString().split('T')[0],
    scheduledTime: '09:00',
    sendNow: true,
  });
  
  // Add state for notification history
  const [notificationHistory, setNotificationHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  
  // Add state for FCM tokens list
  const [fcmTokens, setFcmTokens] = useState([]);
  const [tokensLoading, setTokensLoading] = useState(false);
  const [testNotificationStatus, setTestNotificationStatus] = useState<Record<string, { loading: boolean, success?: boolean, message?: string }>>({});
  const [notificationInputs, setNotificationInputs] = useState<Record<string, { title: string, body: string }>>({});
  
  // Fetch notification history on component mount
  useEffect(() => {
    fetchNotificationHistory();
  }, []);
  
  // Fetch FCM tokens 
  const fetchFcmTokens = async () => {
    try {
      setTokensLoading(true);
      const response = await fetch('/api/push-notifications/tokens');
      const data = await response.json();
      
      if (data.success) {
        setFcmTokens(data.tokens);
      }
    } catch (error) {
      console.error('Error fetching FCM tokens:', error);
    } finally {
      setTokensLoading(false);
    }
  };
  
  // Load tokens when tab is selected
  useEffect(() => {
    if (activeTab === 'tokens') {
      fetchFcmTokens();
    }
  }, [activeTab]);
  
  // Function to fetch notification history
  const fetchNotificationHistory = async () => {
    try {
      setHistoryLoading(true);
      const response = await fetch('/api/push-notifications?limit=10');
      const data = await response.json();
      
      if (data.success) {
        setNotificationHistory(data.notifications);
      }
    } catch (error) {
      console.error('Error fetching notification history:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleToggle = (key: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const handleTemplateChange = (e: React.ChangeEvent<HTMLTextAreaElement>, key: string) => {
    setNotificationTemplates(prev => ({
      ...prev,
      [key]: e.target.value
    }));
  };

  const handleSettingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setScheduleSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Add a type for template keys to fix TypeScript errors
  type TemplateKey = 'orderPlaced' | 'orderConfirmed' | 'orderShipped' | 'orderDelivered' | 'orderCancelled' | 
                     'newProducts' | 'priceDrops' | 'flashSales' | 'couponExpiration' | 'backInStock' |
                     'accountCreated' | 'passwordChanged' | 'reviewPosted' | 'wishlistUpdates';
  
  // Function to send notification
  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      // Determine notification content based on the selected template
      let title = notification.title;
      let body = notification.body;
      
      // If no custom title/body is provided, use the template
      if (!title || !body) {
        const templateKey = notification.subType as TemplateKey;
        if (notificationTemplates[templateKey]) {
          body = notificationTemplates[templateKey];
          
          // Generate a title based on the notification type
          switch(templateKey) {
            case 'orderPlaced':
              title = 'Order Confirmation';
              break;
            case 'orderConfirmed':
              title = 'Order Confirmed';
              break;
            case 'orderShipped':
              title = 'Your Order is On Its Way';
              break;
            case 'orderDelivered':
              title = 'Order Delivered';
              break;
            case 'orderCancelled':
              title = 'Order Cancelled';
              break;
            case 'newProducts':
              title = 'New Products Available';
              break;
            case 'priceDrops':
              title = 'Price Drop Alert';
              break;
            case 'flashSales':
              title = 'Flash Sale - Limited Time';
              break;
            case 'couponExpiration':
              title = 'Your Coupon is Expiring Soon';
              break;
            case 'backInStock':
              title = 'Item Back in Stock';
              break;
            default:
              title = 'Afghan Gems Notification';
          }
        }
      }
      
      // Send the notification
      const response = await fetch('/api/push-notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...notification,
          title,
          body,
        }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to send notification');
      }
      
      setSuccess(true);
      
      // Refresh notification history
      fetchNotificationHistory();
      
      // Reset notification form if it was sent successfully
      if (notification.sendNow) {
        setNotification({
          ...notification,
          title: '',
          body: '',
        });
      }
    } catch (err) {
      setError((err instanceof Error ? err.message : 'Unknown error'));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  // Function to handle notification field changes
  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setNotification(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };
  
  // Function to handle notification type/subtype selection
  const handleNotificationTypeChange = (type: string) => {
    // Update notification type and reset subType based on the selected type
    let subType = '';
    
    switch(type) {
      case 'order':
        subType = 'orderPlaced';
        break;
      case 'promotional':
        subType = 'newProducts';
        break;
      case 'user_activity':
        subType = 'accountCreated';
        break;
    }
    
    setNotification(prev => ({
      ...prev,
      type,
      subType,
    }));
  };
  
  // Function to handle the subType change based on the current type
  const handleNotificationSubTypeChange = (subType: string) => {
    setNotification(prev => ({
      ...prev,
      subType,
      // Add type assertion for the template key
      body: notificationTemplates[subType as TemplateKey] || '',
    }));
  };

  // Tab rendering functions
  const renderOrderNotifications = () => (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex items-center mb-4">
        <ShoppingBag className="h-5 w-5 mr-2 text-blue-500" />
        <h2 className="text-lg font-semibold">Order Notifications</h2>
      </div>

      <div className="space-y-4">
        {/* Order notification switches */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Order Placed</span>
              <p className="text-sm text-gray-500">Notify when customer places a new order</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.orderPlaced}
                onChange={() => handleToggle('orderPlaced')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Order Confirmed</span>
              <p className="text-sm text-gray-500">Notify when order is confirmed</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.orderConfirmed}
                onChange={() => handleToggle('orderConfirmed')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Order Shipped</span>
              <p className="text-sm text-gray-500">Notify when order is shipped</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.orderShipped}
                onChange={() => handleToggle('orderShipped')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Order Delivered</span>
              <p className="text-sm text-gray-500">Notify when order is delivered</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.orderDelivered}
                onChange={() => handleToggle('orderDelivered')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Order Cancelled</span>
              <p className="text-sm text-gray-500">Notify when order is cancelled</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.orderCancelled}
                onChange={() => handleToggle('orderCancelled')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Order notification templates */}
        <div className="mt-6">
          <h3 className="text-md font-medium mb-3">Notification Templates</h3>
          <div className="space-y-4">
            {notificationSettings.orderPlaced && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order Placed</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.orderPlaced}
                  onChange={(e) => handleTemplateChange(e, 'orderPlaced')}
                />
              </div>
            )}
            
            {notificationSettings.orderConfirmed && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order Confirmed</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.orderConfirmed}
                  onChange={(e) => handleTemplateChange(e, 'orderConfirmed')}
                />
              </div>
            )}
            
            {notificationSettings.orderShipped && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order Shipped</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.orderShipped}
                  onChange={(e) => handleTemplateChange(e, 'orderShipped')}
                />
              </div>
            )}
            
            {notificationSettings.orderDelivered && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order Delivered</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.orderDelivered}
                  onChange={(e) => handleTemplateChange(e, 'orderDelivered')}
                />
              </div>
            )}
            
            {notificationSettings.orderCancelled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order Cancelled</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.orderCancelled}
                  onChange={(e) => handleTemplateChange(e, 'orderCancelled')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderPromotionalNotifications = () => (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex items-center mb-4">
        <Tag className="h-5 w-5 mr-2 text-blue-500" />
        <h2 className="text-lg font-semibold">Promotional Notifications</h2>
      </div>

      <div className="space-y-4">
        {/* Promotional notification switches */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">New Products</span>
              <p className="text-sm text-gray-500">Notify about new product arrivals</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.newProducts}
                onChange={() => handleToggle('newProducts')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Price Drops</span>
              <p className="text-sm text-gray-500">Notify about price reductions</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.priceDrops}
                onChange={() => handleToggle('priceDrops')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Flash Sales</span>
              <p className="text-sm text-gray-500">Notify about limited-time sales</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.flashSales}
                onChange={() => handleToggle('flashSales')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Coupon Expiration</span>
              <p className="text-sm text-gray-500">Notify when coupons are about to expire</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.couponExpiration}
                onChange={() => handleToggle('couponExpiration')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border rounded-md">
            <div>
              <span className="font-medium">Back in Stock</span>
              <p className="text-sm text-gray-500">Notify when out-of-stock items are available again</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={notificationSettings.backInStock}
                onChange={() => handleToggle('backInStock')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Promotional notification templates */}
        <div className="mt-6">
          <h3 className="text-md font-medium mb-3">Notification Templates</h3>
          <div className="space-y-4">
            {notificationSettings.newProducts && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">New Products</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.newProducts}
                  onChange={(e) => handleTemplateChange(e, 'newProducts')}
                />
              </div>
            )}
            
            {notificationSettings.priceDrops && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price Drops</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.priceDrops}
                  onChange={(e) => handleTemplateChange(e, 'priceDrops')}
                />
              </div>
            )}
            
            {notificationSettings.flashSales && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Flash Sales</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.flashSales}
                  onChange={(e) => handleTemplateChange(e, 'flashSales')}
                />
              </div>
            )}
            
            {notificationSettings.couponExpiration && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Coupon Expiration</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.couponExpiration}
                  onChange={(e) => handleTemplateChange(e, 'couponExpiration')}
                />
              </div>
            )}
            
            {notificationSettings.backInStock && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Back in Stock</label>
                <textarea
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={notificationTemplates.backInStock}
                  onChange={(e) => handleTemplateChange(e, 'backInStock')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex items-center mb-4">
        <Settings className="h-5 w-5 mr-2 text-blue-500" />
        <h2 className="text-lg font-semibold">Notification Settings</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Preferred Delivery Time
          </label>
          <div className="flex items-center">
            <Clock className="h-5 w-5 mr-2 text-gray-400" />
            <input
              type="time"
              name="deliveryTime"
              value={scheduleSettings.deliveryTime}
              onChange={handleSettingChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Best time to deliver non-urgent notifications like new product launches, price drops, and promotional updates
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Notifications Per Day
          </label>
          <input
            type="number"
            name="maxNotificationsPerDay"
            value={scheduleSettings.maxNotificationsPerDay}
            onChange={handleSettingChange}
            min="0"
            max="10"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            Limit the total number of push notifications per day
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Promotional Per Week
          </label>
          <input
            type="number"
            name="maxPromotionalPerWeek"
            value={scheduleSettings.maxPromotionalPerWeek}
            onChange={handleSettingChange}
            min="0"
            max="20"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            Limit promotional notifications per week to avoid overwhelming customers
          </p>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableQuietHours"
            name="enableQuietHours"
            checked={scheduleSettings.enableQuietHours}
            onChange={handleSettingChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="enableQuietHours" className="ml-2 block text-sm text-gray-700">
            Enable Quiet Hours
          </label>
        </div>

        {scheduleSettings.enableQuietHours && (
          <>
            <p className="text-xs text-gray-500 mt-1 md:col-span-2">
              Prevent notifications during specific hours to respect customer rest time
            </p>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quiet Hours Start
              </label>
              <input
                type="time"
                name="quietHoursStart"
                value={scheduleSettings.quietHoursStart}
                onChange={handleSettingChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Time when notifications should stop being sent
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quiet Hours End
              </label>
              <input
                type="time"
                name="quietHoursEnd"
                value={scheduleSettings.quietHoursEnd}
                onChange={handleSettingChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Time when notifications can resume being sent
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );

  // Add notification sender and history components
  const renderNotificationSender = () => (
    <div className="bg-white shadow-md rounded-lg p-6">
      <h2 className="text-lg font-semibold mb-4">Send Push Notification</h2>
      
      <form onSubmit={handleSendNotification}>
        <div className="space-y-4">
          {/* Notification Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Notification Type</label>
            <div className="flex space-x-2">
              <button
                type="button"
                className={`px-4 py-2 rounded-md ${notification.type === 'order' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
                onClick={() => handleNotificationTypeChange('order')}
              >
                Order Updates
              </button>
              <button
                type="button"
                className={`px-4 py-2 rounded-md ${notification.type === 'promotional' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
                onClick={() => handleNotificationTypeChange('promotional')}
              >
                Promotional
              </button>
              <button
                type="button"
                className={`px-4 py-2 rounded-md ${notification.type === 'user_activity' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
                onClick={() => handleNotificationTypeChange('user_activity')}
              >
                User Activity
              </button>
            </div>
          </div>
          
          {/* Notification SubType Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
            <select
              name="subType"
              value={notification.subType}
              onChange={(e) => handleNotificationSubTypeChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              {notification.type === 'order' && (
                <>
                  <option value="orderPlaced">Order Placed</option>
                  <option value="orderConfirmed">Order Confirmed</option>
                  <option value="orderShipped">Order Shipped</option>
                  <option value="orderDelivered">Order Delivered</option>
                  <option value="orderCancelled">Order Cancelled</option>
                </>
              )}
              
              {notification.type === 'promotional' && (
                <>
                  <option value="newProducts">New Products</option>
                  <option value="priceDrops">Price Drops</option>
                  <option value="flashSales">Flash Sales</option>
                  <option value="couponExpiration">Coupon Expiration</option>
                  <option value="backInStock">Back In Stock</option>
                </>
              )}
              
              {notification.type === 'user_activity' && (
                <>
                  <option value="accountCreated">Account Created</option>
                  <option value="passwordChanged">Password Changed</option>
                  <option value="reviewPosted">Review Posted</option>
                  <option value="wishlistUpdates">Wishlist Updates</option>
                </>
              )}
            </select>
          </div>
          
          {/* Title and Body Fields */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Notification Title</label>
            <input
              type="text"
              name="title"
              value={notification.title}
              onChange={handleNotificationChange}
              placeholder="Enter notification title (optional - will use default if empty)"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Notification Content</label>
            <textarea
              rows={3}
              name="body"
              value={notification.body || notificationTemplates[notification.subType as TemplateKey] || ''}
              onChange={handleNotificationChange}
              placeholder="Enter notification content"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              You can use placeholder syntax like {"{{orderNumber}}"} that will be replaced with actual values when sent.
            </p>
          </div>
          
          {/* Recipient Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Recipients</label>
            <select
              name="recipientGroup"
              value={notification.recipientGroup}
              onChange={handleNotificationChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Users</option>
              <option value="new">New Users (last 7 days)</option>
              <option value="active">Active Users (last 30 days)</option>
              <option value="inactive">Inactive Users (30+ days)</option>
              <option value="manual">Manual Selection</option>
            </select>
          </div>
          
          {/* Manual Recipients Field */}
          {notification.recipientGroup === 'manual' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Manual Recipients (User IDs)</label>
              <textarea
                rows={2}
                name="manualRecipients"
                value={notification.manualRecipients}
                onChange={handleNotificationChange}
                placeholder="Enter user IDs separated by commas"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="mt-1 text-xs text-gray-500">
                Enter user IDs separated by commas. Only users who have granted notification permission will receive the notification.
              </p>
            </div>
          )}
          
          {/* Scheduling Options */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                name="sendNow"
                checked={notification.sendNow}
                onChange={(e) => handleNotificationChange(e)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Send Immediately</span>
            </label>
          </div>
          
          {/* Scheduled Date/Time */}
          {!notification.sendNow && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  name="scheduledDate"
                  value={notification.scheduledDate}
                  onChange={handleNotificationChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Time</label>
                <input
                  type="time"
                  name="scheduledTime"
                  value={notification.scheduledTime}
                  onChange={handleNotificationChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
          
          {/* Submit Button */}
          <div className="mt-6">
            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {loading ? 'Sending...' : notification.sendNow ? 'Send Notification' : 'Schedule Notification'}
            </button>
          </div>
          
          {/* Success/Error Messages */}
          {success && (
            <div className="mt-3 p-3 bg-green-50 text-green-800 rounded-md flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span>{notification.sendNow ? 'Notification sent successfully!' : 'Notification scheduled successfully!'}</span>
            </div>
          )}
          
          {error && (
            <div className="mt-3 p-3 bg-red-50 text-red-800 rounded-md flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          )}
        </div>
      </form>
    </div>
  );

  const renderNotificationHistory = () => (
    <div className="mt-8 bg-white shadow-md rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Notification History</h2>
        <button 
          onClick={fetchNotificationHistory}
          className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm flex items-center"
          disabled={historyLoading}
        >
          {historyLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>
      
      {historyLoading ? (
        <div className="py-4 text-center">Loading notification history...</div>
      ) : notificationHistory.length === 0 ? (
        <div className="py-4 text-center text-gray-500">No notifications have been sent yet.</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent At</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {notificationHistory.map((notification: any) => (
                <tr key={notification._id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {notification.type}/{notification.subType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {notification.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      notification.status === 'sent' ? 'bg-green-100 text-green-800' :
                      notification.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                      notification.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {notification.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {notification.stats?.total || 0} total / {notification.stats?.sent || 0} sent
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {notification.sentAt ? new Date(notification.sentAt).toLocaleString() : 
                     notification.scheduledAt ? `Scheduled for ${new Date(notification.scheduledAt).toLocaleString()}` : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );

  // Add rendering function for the tokens tab
  const renderUserTokens = () => (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">User FCM Tokens (Development Only)</h2>
        <button 
          onClick={fetchFcmTokens}
          className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm flex items-center"
          disabled={tokensLoading}
        >
          {tokensLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>
      
      <div className="text-sm mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
        <p className="text-amber-800">
          <AlertTriangle className="h-4 w-4 inline mr-1" />
          This section is for development purposes only. It displays user FCM tokens for testing notifications.
        </p>
      </div>
      
      {tokensLoading ? (
        <div className="py-4 text-center">Loading tokens...</div>
      ) : fcmTokens.length === 0 ? (
        <div className="py-4 text-center text-gray-500">No FCM tokens registered yet.</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token (first 20 chars)</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {fcmTokens.map((item: any) => (
                <tr key={item._id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.userId || 'Anonymous'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <code className="bg-gray-100 p-1 rounded">{item.token.substring(0, 20)}...</code>
                      <button 
                        onClick={() => navigator.clipboard.writeText(item.token)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                        title="Copy full token"
                      >
                        Copy
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(item.createdAt).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(item.updatedAt).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex flex-col space-y-2">
                      {/* Title input */}
                      <input
                        type="text"
                        placeholder="Notification Title"
                        className="px-2 py-1 border rounded text-sm w-full"
                        value={notificationInputs[item._id]?.title || 'Afghan Gems'}
                        onChange={(e) => setNotificationInputs(prev => ({
                          ...prev,
                          [item._id]: { 
                            ...(prev[item._id] || { body: '' }),
                            title: e.target.value 
                          }
                        }))}
                      />
                      
                      {/* Body input */}
                      <input
                        type="text"
                        placeholder="Notification Body"
                        className="px-2 py-1 border rounded text-sm w-full"
                        value={notificationInputs[item._id]?.body || 'Check out our latest products'}
                        onChange={(e) => setNotificationInputs(prev => ({
                          ...prev,
                          [item._id]: { 
                            ...(prev[item._id] || { title: 'Afghan Gems' }),
                            body: e.target.value 
                          }
                        }))}
                      />
                      
                      {/* Send button */}
                      <button 
                        disabled={testNotificationStatus[item._id]?.loading}
                        onClick={async () => {
                          // Get the custom title and body or use defaults
                          const title = notificationInputs[item._id]?.title || 'Afghan Gems';
                          const body = notificationInputs[item._id]?.body || 'Check out our latest products';
                          
                          // Set loading state for this specific token
                          setTestNotificationStatus(prev => ({
                            ...prev,
                            [item._id]: { loading: true }
                          }));
                          
                          try {
                            const response = await fetch('/api/push-notifications/test', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify({
                                token: item.token,
                                title,
                                body
                              }),
                            });
                            
                            const result = await response.json();
                            
                            if (result.success) {
                              setTestNotificationStatus(prev => ({
                                ...prev,
                                [item._id]: { 
                                  loading: false, 
                                  success: true,
                                  message: `Sent! Message ID: ${result.messageId.substring(0, 8)}...`
                                }
                              }));
                              
                              // Clear success message after 5 seconds
                              setTimeout(() => {
                                setTestNotificationStatus(prev => ({
                                  ...prev,
                                  [item._id]: { loading: false }
                                }));
                              }, 5000);
                            } else {
                              setTestNotificationStatus(prev => ({
                                ...prev,
                                [item._id]: { 
                                  loading: false, 
                                  success: false,
                                  message: result.error || 'Failed to send'
                                }
                              }));
                            }
                          } catch (error) {
                            console.error('Error sending test notification:', error);
                            setTestNotificationStatus(prev => ({
                              ...prev,
                              [item._id]: { 
                                loading: false, 
                                success: false,
                                message: error instanceof Error ? error.message : 'Unknown error'
                              }
                            }));
                          }
                        }}
                        className={`px-3 py-1 rounded ${
                          testNotificationStatus[item._id]?.loading 
                            ? 'bg-gray-200 text-gray-500' 
                            : testNotificationStatus[item._id]?.success === true
                              ? 'bg-green-100 text-green-700'
                              : testNotificationStatus[item._id]?.success === false
                                ? 'bg-red-100 text-red-700'
                                : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        }`}
                      >
                        {testNotificationStatus[item._id]?.loading 
                          ? 'Sending...' 
                          : testNotificationStatus[item._id]?.message || 'Test Send'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );

  return (
    <div className="px-4 py-6 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Push Notifications</h1>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <ul className="flex -mb-px">
          <li className="mr-1">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'order' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('order')}
            >
              <div className="flex items-center">
                <ShoppingBag className="h-4 w-4 mr-2" />
                Order Notifications
              </div>
            </button>
          </li>
          <li className="mr-1">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'promotional' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('promotional')}
            >
              <div className="flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Promotional Notifications
              </div>
            </button>
          </li>
          <li className="mr-1">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'settings' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('settings')}
            >
              <div className="flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Notification Settings
              </div>
            </button>
          </li>
          <li className="mr-1">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'send' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('send')}
            >
              <div className="flex items-center">
                <Bell className="h-4 w-4 mr-2" />
                Send Notification
              </div>
            </button>
          </li>
          <li className="mr-1">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'tokens' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('tokens')}
            >
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                User FCM Tokens
              </div>
            </button>
          </li>
        </ul>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'order' && renderOrderNotifications()}
        {activeTab === 'promotional' && renderPromotionalNotifications()}
        {activeTab === 'settings' && renderNotificationSettings()}
        {activeTab === 'send' && (
          <div>
            {renderNotificationSender()}
            {renderNotificationHistory()}
          </div>
        )}
        {activeTab === 'tokens' && renderUserTokens()}
      </div>
    </div>
  );
} 