import { NextRequest, NextResponse } from 'next/server';
import { firebaseAdmin } from '@/lib/firebase-admin';

// Initialize Firebase Admin SDK if not already initialized
if (!firebaseAdmin.apps.length) {
  try {
    // Make sure to trim any whitespace from credentials
    const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY
      ? process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n').trim()
      : undefined;

    const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID?.trim();
    const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL?.trim();

    firebaseAdmin.initializeApp({
      credential: firebaseAdmin.credential.cert({
        projectId,
        clientEmail,
        privateKey,
      }),
    });
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error);
  }
}

/**
 * API route to verify FCM tokens
 */
export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    // Attempt to verify the token by sending a test message
    // This is sent with dry-run=true so it doesn't actually deliver to the device
    try {
      const response = await firebaseAdmin.messaging().send(
        {
          token,
          data: {
            test: 'true'
          }
        },
        true // dryRun=true
      );
      
      console.log('FCM token validation successful:', response);
      return NextResponse.json({ valid: true });
    } catch (error: any) {
      console.error('FCM token validation failed:', error);
      
      return NextResponse.json({
        valid: false,
        error: error.message || 'Token validation failed'
      });
    }
  } catch (error: any) {
    console.error('Error in token verification:', error);
    return NextResponse.json(
      { valid: false, error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
} 