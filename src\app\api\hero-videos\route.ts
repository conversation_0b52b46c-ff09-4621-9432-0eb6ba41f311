import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';

/**
 * Get all hero videos
 * 
 * GET /api/hero-videos
 */
export async function GET(request: Request) {
  try {
    await connectToDatabase();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // Optional filter by type (mobile/desktop)
    const activeOnly = searchParams.get('activeOnly') === 'true';
    
    // Build query
    let query: any = {};
    
    if (type) {
      query.type = type;
    }
    
    if (activeOnly) {
      query.isActive = true;
    }
    
    // Fetch hero videos
    const heroVideos = await HeroVideo.find(query).sort({ createdAt: -1 });
    
    return NextResponse.json({
      success: true,
      heroVideos
    });
  } catch (error: any) {
    console.error('Error fetching hero videos:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch hero videos'
    }, { status: 500 });
  }
}

/**
 * Create a new hero video (without file upload)
 * Use the upload endpoint for file uploads
 * 
 * POST /api/hero-videos
 */
export async function POST(request: Request) {
  try {
    await connectToDatabase();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.videoUrl || !data.type) {
      return NextResponse.json({
        success: false,
        error: 'Video URL and type are required'
      }, { status: 400 });
    }
    
    // Check if there's already an active video of this type
    if (data.isActive) {
      await HeroVideo.updateMany(
        { type: data.type, isActive: true },
        { isActive: false }
      );
    }
    
    // Create new hero video
    const heroVideo = await HeroVideo.create(data);
    
    return NextResponse.json({
      success: true,
      heroVideo
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating hero video:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create hero video'
    }, { status: 500 });
  }
} 