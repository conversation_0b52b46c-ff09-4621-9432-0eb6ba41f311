'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Check, 
  X, 
  AlertTriangle, 
  Filter, 
  Search, 
  Loader2, 
  MessageSquare, 
  Trash2,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import Image from 'next/image';

type Review = {
  _id: string;
  productId: string;
  productName?: string;
  productImageUrl?: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  reviewText: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  moderatedBy?: string;
  moderationDate?: string;
  moderationNotes?: string;
};

export default function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [moderatingReviewId, setModeratingReviewId] = useState<string | null>(null);
  const [moderationNotes, setModerationNotes] = useState<string>('');
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);
  const [processingAction, setProcessingAction] = useState(false);
  const [expandedReviews, setExpandedReviews] = useState<Record<string, boolean>>({});

  // Fetch reviews on component mount and when filters change
  useEffect(() => {
    fetchReviews();
  }, [statusFilter, sortBy, sortOrder]);

  // Fetch reviews from the API
  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let url = '/api/reviews';
      const params = new URLSearchParams();
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      
      params.append('sortBy', sortBy);
      params.append('sortOrder', sortOrder);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch reviews');
      }
      
      setReviews(data.reviews);
    } catch (err: any) {
      console.error('Error fetching reviews:', err);
      setError(err.message || 'An error occurred while fetching reviews');
    } finally {
      setLoading(false);
    }
  };

  // Filter reviews by search query
  const filteredReviews = reviews.filter(review => {
    if (!searchQuery) return true;
    
    const searchLower = searchQuery.toLowerCase();
    return (
      review.customerName.toLowerCase().includes(searchLower) ||
      review.customerEmail.toLowerCase().includes(searchLower) ||
      review.reviewText.toLowerCase().includes(searchLower) ||
      (review.productName && review.productName.toLowerCase().includes(searchLower))
    );
  });

  // Handle review moderation (approve/reject)
  const handleModerateReview = async (reviewId: string, newStatus: 'approved' | 'rejected') => {
    try {
      setProcessingAction(true);
      
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          moderatedBy: 'Admin', // Replace with actual admin name if available
          moderationNotes: moderationNotes,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${newStatus} review`);
      }
      
      // Update the review in the local state
      setReviews(prevReviews => 
        prevReviews.map(review => 
          review._id === reviewId 
            ? { 
                ...review, 
                status: newStatus,
                moderatedBy: 'Admin',
                moderationDate: new Date().toISOString(),
                moderationNotes: moderationNotes 
              } 
            : review
        )
      );
      
      // Reset moderation state
      setModeratingReviewId(null);
      setModerationNotes('');
      setAction(null);
    } catch (err: any) {
      console.error(`Error ${newStatus} review:`, err);
      setError(err.message || `An error occurred while ${newStatus} the review`);
    } finally {
      setProcessingAction(false);
    }
  };

  // Handle review deletion
  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return;
    }
    
    try {
      setProcessingAction(true);
      
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete review');
      }
      
      // Remove the review from the local state
      setReviews(prevReviews => prevReviews.filter(review => review._id !== reviewId));
    } catch (err: any) {
      console.error('Error deleting review:', err);
      setError(err.message || 'An error occurred while deleting the review');
    } finally {
      setProcessingAction(false);
    }
  };

  // Toggle review expanded state
  const toggleReviewExpanded = (reviewId: string) => {
    setExpandedReviews(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));
  };

  // Render stars for rating
  const renderRatingStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        size={16}
        className={index < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}
      />
    ));
  };

  // Get status badge style
  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border border-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 border border-red-300';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Product Reviews & Ratings</h1>
      
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search reviews..."
            className="w-full pl-9 pr-4 py-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>
        
        <div className="flex gap-2">
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="pl-9 pr-4 py-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
            <Filter className="absolute left-3 top-2.5 text-gray-400" size={16} />
          </div>
          
          <div className="relative">
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [newSortBy, newSortOrder] = e.target.value.split('-');
                setSortBy(newSortBy);
                setSortOrder(newSortOrder as 'asc' | 'desc');
              }}
              className="px-4 py-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
              <option value="rating-desc">Highest Rating</option>
              <option value="rating-asc">Lowest Rating</option>
            </select>
          </div>
          
          <button
            onClick={fetchReviews}
            className="px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
          >
            Refresh
          </button>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="p-4 mb-6 bg-red-100 text-red-700 rounded flex items-center">
          <AlertTriangle size={18} className="mr-2" />
          <p>{error}</p>
        </div>
      )}
      
      {/* Loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="mr-2 h-6 w-6 animate-spin text-blue-500" />
          <p className="text-gray-500">Loading reviews...</p>
        </div>
      ) : filteredReviews.length === 0 ? (
        <div className="p-6 text-center bg-gray-50 border border-gray-200 rounded">
          <MessageSquare className="mx-auto text-gray-400 mb-2" size={48} />
          <p className="text-gray-600">No reviews found</p>
          <p className="text-sm text-gray-500 mt-1">
            {statusFilter !== 'all'
              ? `No ${statusFilter} reviews to display`
              : 'Try adjusting your filters or check back later'}
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Reviews count summary */}
          <div className="text-sm text-gray-500 mb-4">
            Showing {filteredReviews.length} {filteredReviews.length === 1 ? 'review' : 'reviews'}
            {statusFilter !== 'all' ? ` with status: ${statusFilter}` : ''}
          </div>
          
          {/* Reviews list */}
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <div 
                key={review._id} 
                className={`border rounded-lg overflow-hidden ${
                  review.status === 'pending' ? 'border-yellow-300 bg-yellow-50' : 'border-gray-200 bg-white'
                }`}
              >
                {/* Review header - always visible */}
                <div 
                  className="p-4 cursor-pointer flex items-start justify-between"
                  onClick={() => toggleReviewExpanded(review._id)}
                >
                  <div className="flex items-start space-x-4">
                    {/* Product image */}
                    <div className="h-12 w-12 flex-shrink-0">
                      {review.productImageUrl ? (
                        <Image
                          src={`${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${review.productImageUrl}`}
                          alt={review.productName || 'Product'}
                          width={48}
                          height={48}
                          className="h-12 w-12 rounded object-cover"
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-200 rounded flex items-center justify-center">
                          <MessageSquare size={20} className="text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    {/* Review summary */}
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {review.customerName} 
                        <span className="text-sm font-normal text-gray-500 ml-2">
                          ({review.customerEmail})
                        </span>
                      </h3>
                      <div className="flex items-center mt-1 mb-1">
                        <div className="flex mr-2">
                          {renderRatingStars(review.rating)}
                        </div>
                        <span className="text-sm text-gray-600">
                          {new Date(review.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 line-clamp-1">
                        {review.reviewText}
                      </p>
                    </div>
                  </div>
                  
                  {/* Right side with status and expand/collapse */}
                  <div className="flex flex-col items-end">
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusBadgeStyle(review.status)}`}>
                      {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                    </span>
                    <div className="mt-2">
                      {expandedReviews[review._id] ? (
                        <ChevronUp size={16} className="text-gray-400" />
                      ) : (
                        <ChevronDown size={16} className="text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Expanded review details */}
                {expandedReviews[review._id] && (
                  <div className="px-4 pb-4 pt-2 border-t border-gray-100">
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Product</h4>
                      <p className="text-sm text-gray-600">{review.productName || 'Unknown Product'}</p>
                    </div>
                    
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Review</h4>
                      <p className="text-sm text-gray-600 whitespace-pre-line">{review.reviewText}</p>
                    </div>
                    
                    {/* Moderation info for approved/rejected reviews */}
                    {(review.status === 'approved' || review.status === 'rejected') && review.moderationDate && (
                      <div className="mb-4 p-3 bg-gray-50 rounded">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Moderation Info</h4>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Status:</span> {review.status}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Moderated by:</span> {review.moderatedBy || 'Unknown'}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Date:</span> {new Date(review.moderationDate).toLocaleString()}
                        </p>
                        {review.moderationNotes && (
                          <div className="mt-1">
                            <p className="text-sm font-medium text-gray-700">Notes:</p>
                            <p className="text-sm text-gray-600">{review.moderationNotes}</p>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Moderation actions for pending reviews */}
                    {review.status === 'pending' && (
                      <div className="mt-4">
                        {moderatingReviewId === review._id ? (
                          <div className="space-y-3">
                            <textarea
                              value={moderationNotes}
                              onChange={(e) => setModerationNotes(e.target.value)}
                              placeholder="Moderation notes (optional)"
                              className="w-full h-20 px-3 py-2 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => {
                                  setModeratingReviewId(null);
                                  setModerationNotes('');
                                  setAction(null);
                                }}
                                className="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50"
                                disabled={processingAction}
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => handleModerateReview(review._id, action === 'approve' ? 'approved' : 'rejected')}
                                className={`px-3 py-1 text-sm text-white rounded flex items-center ${
                                  action === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
                                }`}
                                disabled={processingAction}
                              >
                                {processingAction ? (
                                  <>
                                    <Loader2 size={14} className="mr-1 animate-spin" />
                                    {action === 'approve' ? 'Approving...' : 'Rejecting...'}
                                  </>
                                ) : (
                                  <>
                                    {action === 'approve' ? (
                                      <>
                                        <Check size={14} className="mr-1" />
                                        Confirm Approval
                                      </>
                                    ) : (
                                      <>
                                        <X size={14} className="mr-1" />
                                        Confirm Rejection
                                      </>
                                    )}
                                  </>
                                )}
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => {
                                setModeratingReviewId(review._id);
                                setAction('reject');
                              }}
                              className="px-3 py-1 bg-red-100 text-red-700 text-sm rounded hover:bg-red-200 flex items-center"
                            >
                              <X size={14} className="mr-1" />
                              Reject
                            </button>
                            <button
                              onClick={() => {
                                setModeratingReviewId(review._id);
                                setAction('approve');
                              }}
                              className="px-3 py-1 bg-green-100 text-green-700 text-sm rounded hover:bg-green-200 flex items-center"
                            >
                              <Check size={14} className="mr-1" />
                              Approve
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Delete action (for all reviews) */}
                    <div className="mt-4 text-right">
                      <button
                        onClick={() => handleDeleteReview(review._id)}
                        className="text-red-600 text-sm hover:text-red-800 flex items-center ml-auto"
                        disabled={processingAction}
                      >
                        <Trash2 size={14} className="mr-1" />
                        Delete Review
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 