'use client';

import { useState, useEffect } from 'react';
import { Bell, BellOff, AlertCircle, CheckCircle } from 'lucide-react';
import usePushNotifications from '@/hooks/usePushNotifications';
import { requestNotificationPermission } from '@/lib/firebase';

interface NotificationPermissionProps {
  userId?: string;
  vapidKey: string;
}

export default function NotificationPermission({ userId, vapidKey }: NotificationPermissionProps) {
  const [showSettings, setShowSettings] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { 
    status, 
    loading: pushNotificationLoading, 
    error: pushNotificationError, 
    requestPermission,
    setNotificationPreferences
  } = usePushNotifications({ 
    userId, 
    vapidKey 
  });
  
  // Check notification permission status on mount
  useEffect(() => {
    if (typeof Notification !== 'undefined') {
      setPermissionStatus(Notification.permission);
    }
  }, []);
  
  const handleRequestPermission = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await requestNotificationPermission();
      
      // Update permission status
      if (typeof Notification !== 'undefined') {
        setPermissionStatus(Notification.permission);
      }
    } catch (err) {
      setError('Failed to enable notifications');
      console.error('Error requesting notification permission:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };
  
  if (typeof Notification === 'undefined') {
    return null;
  }
  
  if (permissionStatus === 'granted') {
    return null;
  }
  
  return (
    <div className="bg-white shadow-md rounded-lg p-4">
      <div className="flex items-center justify-between">
        <p className="text-sm">
          <BellOff className="inline-block h-4 w-4 mr-1 text-gray-500" />
          Enable notifications to stay updated
        </p>
        <button
          onClick={handleRequestPermission}
          disabled={loading || permissionStatus === 'denied'}
          className={`ml-3 px-3 py-1.5 text-xs font-medium rounded-md ${
            permissionStatus === 'denied'
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? 'Enabling...' : 'Enable'}
        </button>
      </div>
      
      {permissionStatus === 'denied' && (
        <p className="mt-2 text-xs text-red-500">
          Notifications are blocked. Please enable them in your browser settings.
        </p>
      )}
      
      {error && (
        <p className="mt-2 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
}

interface NotificationPreferencesFormProps {
  userId?: string;
  onSave: (preferences: any) => Promise<boolean>;
}

function NotificationPreferencesForm({ userId, onSave }: NotificationPreferencesFormProps) {
  // Define types for preferences
  type OrderPreferences = {
    enabled: boolean;
    orderPlaced: boolean;
    orderConfirmed: boolean;
    orderShipped: boolean;
    orderDelivered: boolean;
    orderCancelled: boolean;
  };
  
  type PromotionalPreferences = {
    enabled: boolean;
    newProducts: boolean;
    priceDrops: boolean;
    flashSales: boolean;
    couponExpiration: boolean;
    backInStock: boolean;
  };
  
  type UserActivityPreferences = {
    enabled: boolean;
    accountCreated: boolean;
    passwordChanged: boolean;
    reviewPosted: boolean;
    wishlistUpdates: boolean;
  };
  
  type PreferencesType = {
    order: OrderPreferences;
    promotional: PromotionalPreferences;
    userActivity: UserActivityPreferences;
  };
  
  // Use the defined type for preferences
  const [preferences, setPreferences] = useState<PreferencesType>({
    order: {
      enabled: true,
      orderPlaced: true,
      orderConfirmed: true,
      orderShipped: true,
      orderDelivered: true,
      orderCancelled: true,
    },
    promotional: {
      enabled: false,
      newProducts: false,
      priceDrops: true,
      flashSales: true,
      couponExpiration: true,
      backInStock: true,
    },
    userActivity: {
      enabled: true,
      accountCreated: true,
      passwordChanged: true,
      reviewPosted: false,
      wishlistUpdates: false,
    },
  });
  
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    if (!userId) return;
    
    const fetchPreferences = async () => {
      try {
        const response = await fetch(`/api/push-notifications/register-token?userId=${userId}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.notificationPreferences) {
            setPreferences(data.notificationPreferences);
          }
        }
      } catch (err) {
        console.error('Error fetching notification preferences:', err);
      }
    };
    
    fetchPreferences();
  }, [userId]);
  
  const handleCategoryToggle = (category: keyof PreferencesType) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        enabled: !prev[category].enabled
      }
    }));
  };
  
  const handlePreferenceToggle = (category: keyof PreferencesType, key: string) => {
    setPreferences(prev => {
      const categoryPrefs = {...prev[category]};
      // Use type assertion to safely set the value
      if (key in categoryPrefs) {
        (categoryPrefs as any)[key] = !(categoryPrefs as any)[key];
      }
      
      return {
        ...prev,
        [category]: categoryPrefs
      };
    });
  };
  
  const handleSave = async () => {
    if (!userId) return;
    
    setSaving(true);
    setSuccess(false);
    setError(null);
    
    try {
      const saved = await onSave(preferences);
      
      if (saved) {
        setSuccess(true);
      } else {
        setError('Failed to save preferences');
      }
    } catch (err) {
      setError('Error saving preferences');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };
  
  return (
    <div className="space-y-4">
      {/* Order Notifications */}
      <div className="border rounded-md overflow-hidden">
        <div 
          className="p-3 bg-white border-b flex items-center justify-between cursor-pointer"
          onClick={() => handleCategoryToggle('order')}
        >
          <h5 className="font-medium">Order Notifications</h5>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={preferences.order.enabled}
              onChange={() => {}}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
        
        {preferences.order.enabled && (
          <div className="p-3 bg-gray-50 space-y-2">
            <label className="flex items-center justify-between">
              <span>Order Placed</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.order.orderPlaced}
                onChange={() => handlePreferenceToggle('order', 'orderPlaced')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Order Confirmed</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.order.orderConfirmed}
                onChange={() => handlePreferenceToggle('order', 'orderConfirmed')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Order Shipped</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.order.orderShipped}
                onChange={() => handlePreferenceToggle('order', 'orderShipped')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Order Delivered</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.order.orderDelivered}
                onChange={() => handlePreferenceToggle('order', 'orderDelivered')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Order Cancelled</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.order.orderCancelled}
                onChange={() => handlePreferenceToggle('order', 'orderCancelled')}
              />
            </label>
          </div>
        )}
      </div>
      
      {/* Promotional Notifications */}
      <div className="border rounded-md overflow-hidden">
        <div 
          className="p-3 bg-white border-b flex items-center justify-between cursor-pointer"
          onClick={() => handleCategoryToggle('promotional')}
        >
          <h5 className="font-medium">Promotional Notifications</h5>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={preferences.promotional.enabled}
              onChange={() => {}}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
        
        {preferences.promotional.enabled && (
          <div className="p-3 bg-gray-50 space-y-2">
            <label className="flex items-center justify-between">
              <span>New Products</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.promotional.newProducts}
                onChange={() => handlePreferenceToggle('promotional', 'newProducts')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Price Drops</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.promotional.priceDrops}
                onChange={() => handlePreferenceToggle('promotional', 'priceDrops')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Flash Sales</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.promotional.flashSales}
                onChange={() => handlePreferenceToggle('promotional', 'flashSales')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Coupon Expiration</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.promotional.couponExpiration}
                onChange={() => handlePreferenceToggle('promotional', 'couponExpiration')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Back In Stock</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.promotional.backInStock}
                onChange={() => handlePreferenceToggle('promotional', 'backInStock')}
              />
            </label>
          </div>
        )}
      </div>
      
      {/* User Activity Notifications */}
      <div className="border rounded-md overflow-hidden">
        <div 
          className="p-3 bg-white border-b flex items-center justify-between cursor-pointer"
          onClick={() => handleCategoryToggle('userActivity')}
        >
          <h5 className="font-medium">Account Notifications</h5>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={preferences.userActivity.enabled}
              onChange={() => {}}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
        
        {preferences.userActivity.enabled && (
          <div className="p-3 bg-gray-50 space-y-2">
            <label className="flex items-center justify-between">
              <span>Account Created</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.userActivity.accountCreated}
                onChange={() => handlePreferenceToggle('userActivity', 'accountCreated')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Password Changed</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.userActivity.passwordChanged}
                onChange={() => handlePreferenceToggle('userActivity', 'passwordChanged')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Review Posted</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.userActivity.reviewPosted}
                onChange={() => handlePreferenceToggle('userActivity', 'reviewPosted')}
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span>Wishlist Updates</span>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={preferences.userActivity.wishlistUpdates}
                onChange={() => handlePreferenceToggle('userActivity', 'wishlistUpdates')}
              />
            </label>
          </div>
        )}
      </div>
      
      <div className="mt-4 flex items-center justify-between">
        <div>
          {success && (
            <span className="text-sm text-green-600 flex items-center">
              <CheckCircle className="h-4 w-4 mr-1" />
              Preferences saved
            </span>
          )}
          
          {error && (
            <span className="text-sm text-red-600 flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {error}
            </span>
          )}
        </div>
        
        <button
          onClick={handleSave}
          disabled={saving || !userId}
          className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Preferences'}
        </button>
      </div>
    </div>
  );
} 