import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const { email, password } = await req.json();
    
    // Find the user
    const user = await User.findOne({ email: email.toLowerCase() })
      .populate('role')
      .exec();
    
    if (!user || !user.isActive) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }
    
    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }
    
    // Update last login time
    user.lastLogin = new Date();
    await user.save();
    
    // Return user data without password
    const userData = user.toObject();
    delete userData.password;
    
    return NextResponse.json({
      success: true,
      user: userData
    });
  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json(
      { error: 'Login failed' },
      { status: 500 }
    );
  }
} 