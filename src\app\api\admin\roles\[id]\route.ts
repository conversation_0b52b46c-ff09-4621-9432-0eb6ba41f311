import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Role } from '@/models/User';

interface Params {
  params: {
    id: string;
  };
}

// GET /api/admin/roles/[id] - Get a specific role
export async function GET(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const role = await Role.findById(id).lean();
    
    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(role);
  } catch (error) {
    console.error('Error fetching role:', error);
    return NextResponse.json(
      { error: 'Failed to fetch role' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/roles/[id] - Update a role
export async function PUT(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const updates = await request.json();
    
    // Validate
    if (!updates.name) {
      return NextResponse.json(
        { error: 'Role name is required' },
        { status: 400 }
      );
    }
    
    // Check if name is being changed and if it already exists
    if (updates.name) {
      const existingRole = await Role.findOne({ 
        name: updates.name,
        _id: { $ne: id } // not equal to the current role ID
      });
      
      if (existingRole) {
        return NextResponse.json(
          { error: 'Role with this name already exists' },
          { status: 409 }
        );
      }
    }
    
    // Update the role
    const updatedRole = await Role.findByIdAndUpdate(
      id,
      updates,
      { new: true, runValidators: true }
    ).lean();
    
    if (!updatedRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedRole);
  } catch (error) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/roles/[id] - Delete a role
export async function DELETE(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const deletedRole = await Role.findByIdAndDelete(id).lean();
    
    if (!deletedRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    );
  }
} 