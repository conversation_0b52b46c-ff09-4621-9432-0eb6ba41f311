import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';
import { getCloudFrontChunkedVideoUrl } from '@/lib/cloudfront';

/**
 * Migrate existing hero videos from proxy URLs to direct CloudFront URLs
 * 
 * POST /api/hero-videos/migrate-to-cloudfront
 */
export async function POST() {
  try {
    await connectToDatabase();
    
    // Find all videos using proxy URLs
    const videosWithProxyUrls = await HeroVideo.find({
      chunkedVideoUrl: { 
        $exists: true,
        $regex: /^\/api\/hls-proxy\//
      }
    });

    console.log(`Found ${videosWithProxyUrls.length} videos with proxy URLs to migrate`);

    let migratedCount = 0;
    const migrationResults = [];

    for (const video of videosWithProxyUrls) {
      const oldUrl = video.chunkedVideoUrl;
      
      // Extract S3 key from proxy URL
      const s3Key = oldUrl.replace('/api/hls-proxy/', '');
      
      // Generate direct CloudFront URL
      const directCloudFrontUrl = getCloudFrontChunkedVideoUrl(s3Key);
      
      if (directCloudFrontUrl) {
        video.chunkedVideoUrl = directCloudFrontUrl;
        await video.save();
        
        migratedCount++;
        migrationResults.push({
          videoId: video._id,
          type: video.type,
          oldUrl,
          newUrl: directCloudFrontUrl
        });
        
        console.log(`✅ Migrated ${video.type} video ${video._id}`);
        console.log(`   Old: ${oldUrl}`);
        console.log(`   New: ${directCloudFrontUrl}`);
      } else {
        console.log(`⚠️ Could not generate CloudFront URL for ${video._id}, keeping proxy URL`);
        migrationResults.push({
          videoId: video._id,
          type: video.type,
          oldUrl,
          newUrl: oldUrl,
          skipped: true,
          reason: 'CloudFront not configured'
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully migrated ${migratedCount} videos to direct CloudFront URLs`,
      totalVideosFound: videosWithProxyUrls.length,
      migratedCount,
      skippedCount: videosWithProxyUrls.length - migratedCount,
      migrationResults
    });

  } catch (error: any) {
    console.error('Error migrating videos to CloudFront:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to migrate videos to CloudFront'
    }, { status: 500 });
  }
}

/**
 * Check which videos would be affected by migration
 * 
 * GET /api/hero-videos/migrate-to-cloudfront
 */
export async function GET() {
  try {
    await connectToDatabase();
    
    // Find all videos using proxy URLs
    const videosWithProxyUrls = await HeroVideo.find({
      chunkedVideoUrl: { 
        $exists: true,
        $regex: /^\/api\/hls-proxy\//
      }
    }).select('_id type chunkedVideoUrl fileName isActive');

    // Find all videos already using direct URLs
    const videosWithDirectUrls = await HeroVideo.find({
      chunkedVideoUrl: { 
        $exists: true,
        $regex: /^https:\/\//
      }
    }).select('_id type chunkedVideoUrl fileName isActive');

    return NextResponse.json({
      success: true,
      message: 'Migration status check',
      videosWithProxyUrls: {
        count: videosWithProxyUrls.length,
        videos: videosWithProxyUrls
      },
      videosWithDirectUrls: {
        count: videosWithDirectUrls.length,
        videos: videosWithDirectUrls
      },
      cloudFrontConfigured: !!process.env.NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN,
      cloudFrontDomain: process.env.NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN || 'Not configured'
    });

  } catch (error: any) {
    console.error('Error checking migration status:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to check migration status'
    }, { status: 500 });
  }
} 