import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import ReturnRequest from '@/models/ReturnRequest';

/**
 * Update a return request (PATCH)
 * 
 * PATCH /api/returns/:id
 */
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const returnId = params.id;
    
    // Parse the request body
    const data = await request.json();
    
    // Validate request body
    if (!data.status) {
      return NextResponse.json({
        success: false,
        error: 'Status is required'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Find and update the return request
    const updatedReturn = await ReturnRequest.findByIdAndUpdate(
      returnId,
      { status: data.status },
      { new: true } // Return the updated document
    );
    
    if (!updatedReturn) {
      return NextResponse.json({
        success: false,
        error: 'Return request not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Return request updated successfully',
      returnRequest: updatedReturn
    });
    
  } catch (error: any) {
    console.error('Error updating return request:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update return request'
    }, { status: 500 });
  }
} 