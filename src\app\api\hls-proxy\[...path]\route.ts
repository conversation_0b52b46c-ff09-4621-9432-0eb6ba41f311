import { NextResponse } from 'next/server';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getCloudFrontChunkedVideoUrl } from '@/lib/cloudfront';

// S3 configuration
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

const getS3Client = () => {
  return new S3Client(s3Config);
};

const chunkedVideosBucketName = process.env.S3_CHUNKED_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * HLS Proxy - Serves HLS manifest and segment files with proper CORS headers
 * Now with CloudFront acceleration for faster loading!
 * 
 * GET /api/hls-proxy/[...path]
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const filePath = path.join('/');
    
    if (!filePath) {
      return NextResponse.json({
        success: false,
        error: 'File path is required'
      }, { status: 400 });
    }

    console.log(`🎬 Serving HLS file: ${filePath}`);

    // Check if the path is already a full CloudFront URL
    if (filePath.startsWith('https://')) {
      console.log(`🌐 Direct CloudFront URL detected, fetching with CORS: ${filePath}`);
      
      try {
        // Fetch from CloudFront and serve with CORS headers
        const response = await fetch(filePath, {
          method: 'GET',
          headers: {
            'User-Agent': 'NextJS-HLS-Proxy/1.0',
          },
        });

        if (!response.ok) {
          throw new Error(`CloudFront fetch failed: ${response.status} ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        
        // Determine content type based on file extension or response headers
        let contentType = response.headers.get('content-type') || 'application/octet-stream';
        if (filePath.endsWith('.m3u8')) {
          contentType = 'application/vnd.apple.mpegurl';
        } else if (filePath.endsWith('.ts')) {
          contentType = 'video/MP2T';
        }

        // Return content with proper CORS headers
        return new NextResponse(buffer, {
          headers: {
            'Content-Type': contentType,
            'Content-Length': buffer.byteLength.toString(),
            'Cache-Control': 'public, max-age=3600',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
            'Access-Control-Allow-Headers': 'Range, Content-Type',
            'Access-Control-Expose-Headers': 'Content-Range, Content-Length',
            'Accept-Ranges': 'bytes',
          },
        });
      } catch (fetchError: any) {
        console.error('Error fetching from CloudFront:', fetchError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch from CloudFront',
          details: fetchError.message
        }, { status: 502 });
      }
    }

    // For S3 keys (not full URLs), try CloudFront first
    const cloudFrontUrl = getCloudFrontChunkedVideoUrl(filePath);
    
    if (cloudFrontUrl) {
      console.log(`⚡ Using CloudFront URL: ${cloudFrontUrl}`);
      
      try {
        // Fetch from CloudFront and serve with CORS headers instead of redirecting
        const response = await fetch(cloudFrontUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'NextJS-HLS-Proxy/1.0',
          },
        });

        if (!response.ok) {
          throw new Error(`CloudFront fetch failed: ${response.status} ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        
        // Determine content type based on file extension
        let contentType = response.headers.get('content-type') || 'application/octet-stream';
        if (filePath.endsWith('.m3u8')) {
          contentType = 'application/vnd.apple.mpegurl';
        } else if (filePath.endsWith('.ts')) {
          contentType = 'video/MP2T';
        }

        // Return content with proper CORS headers
        return new NextResponse(buffer, {
          headers: {
            'Content-Type': contentType,
            'Content-Length': buffer.byteLength.toString(),
            'Cache-Control': 'public, max-age=3600',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
            'Access-Control-Allow-Headers': 'Range, Content-Type',
            'Access-Control-Expose-Headers': 'Content-Range, Content-Length',
            'Accept-Ranges': 'bytes',
          },
        });
      } catch (fetchError: any) {
        console.error('Error fetching from CloudFront, falling back to S3:', fetchError);
        // Fall through to S3 fallback
      }
    }

    // Fallback to direct S3 access
    console.log(`📦 Using direct S3 access for: ${filePath}`);
    
    const s3Client = getS3Client();
    
    // Get object from S3
    const getObjectCommand = new GetObjectCommand({
      Bucket: chunkedVideosBucketName,
      Key: filePath
    });
    
    const response = await s3Client.send(getObjectCommand);
    
    if (!response.Body) {
      return NextResponse.json({
        success: false,
        error: 'File not found'
      }, { status: 404 });
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    for await (const chunk of response.Body as any) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    // Determine content type based on file extension
    let contentType = 'application/octet-stream';
    if (filePath.endsWith('.m3u8')) {
      contentType = 'application/vnd.apple.mpegurl';
    } else if (filePath.endsWith('.ts')) {
      contentType = 'video/MP2T';
    }

    // Return file with proper CORS headers for HLS
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': buffer.length.toString(),
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Range, Content-Type',
        'Access-Control-Expose-Headers': 'Content-Range, Content-Length',
        'Accept-Ranges': 'bytes',
      },
    });

  } catch (error: any) {
    console.error('Error serving HLS file:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to serve HLS file',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * Handle OPTIONS request for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
} 