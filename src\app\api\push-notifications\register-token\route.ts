import { NextRequest, NextResponse } from 'next/server';
import UserFcmToken from '@/models/UserFcmToken';
import connectDB from '@/lib/mongoose';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

// Define the FCM token schema (same as in the tokens route)
const FCMTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
  },
  userId: String,
  device: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Get or create the model
const FCMToken = mongoose.models.FCMToken || mongoose.model('FCMToken', FCMTokenSchema, 'fcmTokens');

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token is required' },
        { status: 400 }
      );
    }
    
    // Connect to the database
    const { db, status, error } = await connectToDatabase();
    
    if (status === 'error' || !db) {
      console.error('Failed to connect to database:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to connect to database' },
        { status: 500 }
      );
    }
    
    // Get the user ID from the session if available
    // For now, we'll use null as we don't have session information
    const userId = null;
    
    // Try to find an existing token to update
    const existingToken = await FCMToken.findOne({ token });
    
    if (existingToken) {
      // Update the token record
      existingToken.updatedAt = new Date();
      if (userId) existingToken.userId = userId;
      await existingToken.save();
      
      return NextResponse.json({
        success: true,
        message: 'Token updated successfully',
      });
    } else {
      // Create a new token record
      const fcmToken = new FCMToken({
        token,
        userId,
        device: request.headers.get('user-agent') || 'unknown',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      
      await fcmToken.save();
      
      return NextResponse.json({
        success: true,
        message: 'Token registered successfully',
      });
    }
  } catch (error) {
    console.error('Error registering FCM token:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to register FCM token' },
      { status: 500 }
    );
  }
}

// GET endpoint to get user notification preferences
export async function GET(request: NextRequest) {
  try {
    // Connect to the database
    await connectDB();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const token = searchParams.get('token');
    
    if (!userId && !token) {
      return NextResponse.json(
        { error: 'Either userId or token query parameter is required' },
        { status: 400 }
      );
    }
    
    // Build query
    const query: any = {};
    if (userId) query.userId = userId;
    if (token) query.token = token;
    
    // Get user FCM token
    const fcmToken = await UserFcmToken.findOne(query);
    
    if (!fcmToken) {
      return NextResponse.json(
        { error: 'FCM token not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      notificationPreferences: fcmToken.notificationPreferences,
      quietHours: fcmToken.quietHours,
      notificationPermission: fcmToken.notificationPermission,
    });
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification preferences: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
} 