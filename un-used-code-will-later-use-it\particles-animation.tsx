import React, { useState, useEffect, useId } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { cn } from '@/lib/utils';
import Particles, { initParticlesEngine } from '@tsparticles/react';
import { loadSlim } from '@tsparticles/slim';
import type { Container, Engine } from "@tsparticles/engine";

// Define type for SparklesCore props
type ParticlesProps = {
  id?: string;
  className?: string;
  background?: string;
  particleSize?: number;
  minSize?: number;
  maxSize?: number;
  speed?: number;
  particleColor?: string;
  particleDensity?: number;
};

// SparklesCore Component Code
export const SparklesCore = (props: ParticlesProps) => {
  const {
    id,
    className,
    background,
    minSize = 0.6,
    maxSize = 1.4,
    speed = 4,
    particleColor = "#FFFFFF",
    particleDensity = 100,
  } = props;
  
  const [init, setInit] = useState(false);
  const [particlesEngine, setParticlesEngine] = useState<Engine | null>(null);

  // Initialize the particles engine and load the slim preset
  useEffect(() => {
    const initializeEngine = async () => {
      await initParticlesEngine(async (engine) => {
        await loadSlim(engine);
        setParticlesEngine(engine);
      });
      setInit(true);
    };

    initializeEngine();
  }, []); // Run only once on mount

  const controls = useAnimation();

  const particlesLoaded = async (container?: Container) => {
    if (container) {
      controls.start({
        opacity: 1,
        transition: {
          duration: 1,
        },
      });
    }
  };

  const generatedId = useId();
  return (
    <motion.div animate={controls} className={cn("opacity-0", className)}>
      {init && particlesEngine && (
        <Particles
          id={id || generatedId}
          particlesLoaded={particlesLoaded}
          className={cn("h-full w-full")}
          options={{
            background: {
              color: {
                value: background || "",
              },
            },
            fullScreen: {
              enable: false,
              zIndex: 1,
            },
            fpsLimit: 120,
            interactivity: {
              events: {
                onClick: {
                  enable: true,
                  mode: "push",
                },
                onHover: {
                  enable: false,
                  mode: "repulse",
                },
                resize: {
                  enable: true
                },
              },
              modes: {
                push: {
                  quantity: 4,
                },
                repulse: {
                  distance: 200,
                  duration: 0.4,
                },
              },
            },
            particles: {
              number: {
                value: particleDensity,
              },
              color: {
                value: particleColor,
              },
              size: {
                value: {
                  min: minSize,
                  max: maxSize,
                },
              },
              move: {
                enable: true,
                speed: speed,
              },
            },
            detectRetina: true,
          }}
        />
      )}
    </motion.div>
  );
}; 