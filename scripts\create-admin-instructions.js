/**
 * MANUAL ADMIN USER CREATION INSTRUCTIONS
 * 
 * Since you're experiencing MongoDB connection issues, here are step-by-step
 * instructions to manually create an admin user directly in your MongoDB database.
 */

const bcrypt = require('bcryptjs');

// Generate a hashed password
const password = 'admin123';
const generateHash = async () => {
  const salt = await bcrypt.genSalt(10);
  const hash = await bcrypt.hash(password, salt);
  console.log('\n=========== STEP 1: CREATE SUPER ADMIN ROLE ===========');
  console.log('Use MongoDB Compass or Mongo Shell to insert this document into the "roles" collection:');
  console.log(`
{
  "name": "Super Admin",
  "description": "Full access to all system features",
  "permissions": [
    {"resource": "dashboard", "actions": ["view", "create", "update", "delete"]},
    {"resource": "products", "actions": ["view", "create", "update", "delete"]},
    {"resource": "categories", "actions": ["view", "create", "update", "delete"]},
    {"resource": "orders", "actions": ["view", "create", "update", "delete"]},
    {"resource": "customers", "actions": ["view", "create", "update", "delete"]},
    {"resource": "inventory", "actions": ["view", "create", "update", "delete"]},
    {"resource": "reviews", "actions": ["view", "create", "update", "delete"]},
    {"resource": "analytics", "actions": ["view", "create", "update", "delete"]},
    {"resource": "returns", "actions": ["view", "create", "update", "delete"]},
    {"resource": "users", "actions": ["view", "create", "update", "delete"]}
  ]
}
  `);

  console.log('\n=========== STEP 2: GET THE ROLE ID ===========');
  console.log('After creating the role, note its "_id" value from MongoDB.');
  console.log('You\'ll need this ID to link the user to the role.');

  console.log('\n=========== STEP 3: CREATE ADMIN USER ===========');
  console.log('Insert this document into the "users" collection (replace "ROLE_ID_HERE" with the actual role ID):');
  console.log(`
{
  "email": "<EMAIL>",
  "password": "${hash}",
  "firstName": "Admin",
  "lastName": "User",
  "role": ObjectId("ROLE_ID_HERE"),
  "isActive": true,
  "createdAt": new Date(),
  "updatedAt": new Date()
}
  `);

  console.log('\n=========== ADMIN USER LOGIN CREDENTIALS ===========');
  console.log('Email: <EMAIL>');
  console.log('Password: admin123');
  console.log('\nAfter creating these documents, you should be able to log in at /admin/login');
};

generateHash(); 