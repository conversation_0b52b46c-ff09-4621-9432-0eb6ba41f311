'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  MessageSquare, 
  Clock, 
  AlertCircle,
  CheckCircle, 
  UserCircle,
  Calendar,
  Tag,
  ArrowUpDown
} from 'lucide-react';

interface TicketMessage {
  sender: 'customer' | 'admin';
  message: string;
  timestamp: string;
  attachmentUrl?: string;
}

interface SupportTicket {
  _id: string;
  ticketNumber: string;
  customer: {
    email: string;
    name: string;
    phone?: string;
  };
  subject: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'waiting-for-customer' | 'resolved' | 'closed';
  orderId?: string;
  assignedTo?: string;
  messages: TicketMessage[];
  internalNotes?: string;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

export default function CustomerSupportAdmin() {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<SupportTicket[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [replyMessage, setReplyMessage] = useState('');
  const [isReplying, setIsReplying] = useState(false);
  
  const router = useRouter();

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    applyFiltersAndSearch();
  }, [tickets, searchQuery, statusFilter, priorityFilter, categoryFilter, sortField, sortDirection]);

  const fetchTickets = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/support-tickets');
      
      if (!response.ok) {
        throw new Error('Failed to fetch support tickets');
      }
      
      const data = await response.json();
      setTickets(data);
    } catch (err: any) {
      setError('Error loading support tickets. Please try again.');
      console.error('Error fetching support tickets:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFiltersAndSearch = () => {
    let filtered = [...tickets];
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }
    
    // Apply priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.priority === priorityFilter);
    }
    
    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.category === categoryFilter);
    }
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(ticket => 
        ticket.ticketNumber.toLowerCase().includes(query) ||
        ticket.customer.name.toLowerCase().includes(query) ||
        ticket.customer.email.toLowerCase().includes(query) ||
        ticket.subject.toLowerCase().includes(query) ||
        ticket.description.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch(sortField) {
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'lastUpdated':
          aValue = new Date(a.lastUpdated).getTime();
          bValue = new Date(b.lastUpdated).getTime();
          break;
        case 'priority':
          const priorityOrder: Record<string, number> = { low: 0, medium: 1, high: 2, urgent: 3 };
          aValue = priorityOrder[a.priority] || 0;
          bValue = priorityOrder[b.priority] || 0;
          break;
        case 'status':
          const statusOrder: Record<string, number> = { closed: 0, resolved: 1, 'waiting-for-customer': 2, 'in-progress': 3, open: 4 };
          aValue = statusOrder[a.status] || 0;
          bValue = statusOrder[b.status] || 0;
          break;
        default:
          aValue = (a as any)[sortField] || '';
          bValue = (b as any)[sortField] || '';
      }
      
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    setFilteredTickets(filtered);
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleViewTicket = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
  };

  const handleUpdateStatus = async (id: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/support-tickets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });
      
      if (!response.ok) {
        throw new Error('Failed to update ticket status');
      }
      
      // Update local state
      setTickets(tickets.map(ticket => 
        ticket._id === id ? { ...ticket, status: newStatus as any } : ticket
      ));
      
      // Update selected ticket if it's the one being modified
      if (selectedTicket && selectedTicket._id === id) {
        setSelectedTicket({ ...selectedTicket, status: newStatus as any });
      }
    } catch (err) {
      console.error('Error updating ticket status:', err);
      alert('Failed to update ticket status. Please try again.');
    }
  };

  const handleUpdatePriority = async (id: string, newPriority: string) => {
    try {
      const response = await fetch(`/api/support-tickets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ priority: newPriority })
      });
      
      if (!response.ok) {
        throw new Error('Failed to update ticket priority');
      }
      
      // Update local state
      setTickets(tickets.map(ticket => 
        ticket._id === id ? { ...ticket, priority: newPriority as any } : ticket
      ));
      
      // Update selected ticket if it's the one being modified
      if (selectedTicket && selectedTicket._id === id) {
        setSelectedTicket({ ...selectedTicket, priority: newPriority as any });
      }
    } catch (err) {
      console.error('Error updating ticket priority:', err);
      alert('Failed to update ticket priority. Please try again.');
    }
  };

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTicket || !replyMessage.trim()) return;
    
    setIsReplying(true);
    
    try {
      const response = await fetch(`/api/support-tickets/${selectedTicket._id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sender: 'admin',
          message: replyMessage
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to send reply');
      }
      
      const data = await response.json();
      
      // Update tickets list with the updated ticket
      setTickets(tickets.map(ticket => 
        ticket._id === selectedTicket._id ? data.ticket : ticket
      ));
      
      // Update selected ticket
      setSelectedTicket(data.ticket);
      
      // Clear reply message
      setReplyMessage('');
    } catch (err) {
      console.error('Error sending reply:', err);
      alert('Failed to send reply. Please try again.');
    } finally {
      setIsReplying(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'waiting-for-customer':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Customer Support & Tickets</h1>
        <button 
          onClick={fetchTickets}
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          <RefreshCw size={18} className="mr-2" />
          Refresh
        </button>
      </div>
      
      {/* Filters and Search */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="relative md:col-span-2">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search tickets..."
            className="pl-10 pr-4 py-2 w-full border rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value="open">Open</option>
            <option value="in-progress">In Progress</option>
            <option value="waiting-for-customer">Waiting for Customer</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
        </div>
        
        <div>
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
        
        <div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Categories</option>
            <option value="order">Order</option>
            <option value="product">Product</option>
            <option value="shipping">Shipping</option>
            <option value="payment">Payment</option>
            <option value="website">Website</option>
            <option value="account">Account</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        {/* Tickets List */}
        <div className={`${selectedTicket ? 'w-full md:w-1/2' : 'w-full'} overflow-x-auto`}>
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="text-center py-10 text-red-500">{error}</div>
          ) : filteredTickets.length === 0 ? (
            <div className="text-center py-10 text-gray-500">
              {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all' || categoryFilter !== 'all'
                ? 'No tickets match your search criteria.'
                : 'No support tickets found.'}
            </div>
          ) : (
            <table className="min-w-full bg-white border rounded-lg overflow-hidden">
              <thead className="bg-gray-100 text-gray-700">
                <tr>
                  <th className="py-3 px-4 text-left">
                    <button 
                      onClick={() => handleSort('ticketNumber')}
                      className="flex items-center font-semibold"
                    >
                      ID <ArrowUpDown size={14} className="ml-1" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-left">
                    <button 
                      onClick={() => handleSort('subject')}
                      className="flex items-center font-semibold"
                    >
                      Subject <ArrowUpDown size={14} className="ml-1" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-left">Customer</th>
                  <th className="py-3 px-4 text-left">
                    <button 
                      onClick={() => handleSort('status')}
                      className="flex items-center font-semibold"
                    >
                      Status <ArrowUpDown size={14} className="ml-1" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-left">
                    <button 
                      onClick={() => handleSort('priority')}
                      className="flex items-center font-semibold"
                    >
                      Priority <ArrowUpDown size={14} className="ml-1" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-left">
                    <button 
                      onClick={() => handleSort('createdAt')}
                      className="flex items-center font-semibold"
                    >
                      Created <ArrowUpDown size={14} className="ml-1" />
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredTickets.map((ticket) => (
                  <tr 
                    key={ticket._id} 
                    onClick={() => handleViewTicket(ticket)}
                    className={`hover:bg-gray-50 cursor-pointer ${selectedTicket?._id === ticket._id ? 'bg-blue-50' : ''}`}
                  >
                    <td className="py-3 px-4 font-medium">{ticket.ticketNumber}</td>
                    <td className="py-3 px-4">{ticket.subject}</td>
                    <td className="py-3 px-4">{ticket.customer.name}</td>
                    <td className="py-3 px-4">
                      <span className={`inline-block px-2 py-1 rounded-full text-xs ${getStatusColor(ticket.status)}`}>
                        {ticket.status.replace(/-/g, ' ')}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-block px-2 py-1 rounded-full text-xs ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-sm">
                      {formatDate(ticket.createdAt)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        
        {/* Ticket Details */}
        {selectedTicket && (
          <div className="w-full md:w-1/2 bg-white border rounded-lg p-6">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-xl font-bold">{selectedTicket.subject}</h2>
                <div className="flex items-center text-sm text-gray-500 mt-1">
                  <Tag size={14} className="mr-1" />
                  <span className="capitalize">{selectedTicket.category}</span>
                  <span className="mx-2">•</span>
                  <Calendar size={14} className="mr-1" />
                  <span>{formatDate(selectedTicket.createdAt)}</span>
                </div>
              </div>
              <button
                onClick={() => setSelectedTicket(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2 mb-6">
              <div className="w-full sm:w-auto">
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={selectedTicket.status}
                  onChange={(e) => handleUpdateStatus(selectedTicket._id, e.target.value)}
                  className="px-3 py-1 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="open">Open</option>
                  <option value="in-progress">In Progress</option>
                  <option value="waiting-for-customer">Waiting for Customer</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
              </div>
              
              <div className="w-full sm:w-auto">
                <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                <select
                  value={selectedTicket.priority}
                  onChange={(e) => handleUpdatePriority(selectedTicket._id, e.target.value)}
                  className="px-3 py-1 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>
            
            <div className="mb-6">
              <h3 className="font-semibold mb-2">Customer Information</h3>
              <div className="text-sm">
                <p><span className="font-medium">Name:</span> {selectedTicket.customer.name}</p>
                <p><span className="font-medium">Email:</span> {selectedTicket.customer.email}</p>
                {selectedTicket.customer.phone && (
                  <p><span className="font-medium">Phone:</span> {selectedTicket.customer.phone}</p>
                )}
              </div>
            </div>
            
            <div className="mb-6">
              <h3 className="font-semibold mb-2">Ticket Description</h3>
              <div className="bg-gray-50 p-3 rounded-md text-sm">
                {selectedTicket.description}
              </div>
            </div>
            
            {/* Messages */}
            <div className="mb-6">
              <h3 className="font-semibold mb-2">Conversation</h3>
              {selectedTicket.messages && selectedTicket.messages.length > 0 ? (
                <div className="space-y-4 max-h-80 overflow-y-auto p-1">
                  {selectedTicket.messages.map((message, index) => (
                    <div 
                      key={index} 
                      className={`flex ${message.sender === 'admin' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div 
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.sender === 'admin' 
                            ? 'bg-blue-500 text-white' 
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <div className="text-sm mb-1 flex items-center">
                          {message.sender === 'admin' ? (
                            <>
                              <span>Support Team</span>
                              <UserCircle size={14} className="ml-1" />
                            </>
                          ) : (
                            <>
                              <UserCircle size={14} className="mr-1" />
                              <span>{selectedTicket.customer.name}</span>
                            </>
                          )}
                        </div>
                        <div className="whitespace-pre-wrap">{message.message}</div>
                        <div className="text-xs mt-1 opacity-75">
                          {formatDate(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">No messages yet.</div>
              )}
            </div>
            
            {/* Reply Form */}
            <form onSubmit={handleReplySubmit}>
              <div className="mb-4">
                <label htmlFor="reply" className="block text-sm font-medium text-gray-700 mb-1">
                  Reply to Customer
                </label>
                <textarea
                  id="reply"
                  rows={4}
                  className="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Type your reply here..."
                  value={replyMessage}
                  onChange={(e) => setReplyMessage(e.target.value)}
                  required
                ></textarea>
              </div>
              <button
                type="submit"
                disabled={isReplying}
                className="w-full flex justify-center items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                {isReplying ? (
                  <>
                    <Clock size={18} className="animate-spin mr-2" />
                    Sending...
                  </>
                ) : (
                  <>
                    <MessageSquare size={18} className="mr-2" />
                    Send Reply
                  </>
                )}
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
} 