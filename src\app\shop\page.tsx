import { Suspense } from 'react';
import ShopSkeleton from '@/components/ShopSkeleton';
import type { Product as StoreProduct } from '@/store/useProductStore';

// Client component imports
import ShopClientComponent from './ShopClientComponent';

// Keep all your existing types exactly the same
type Subcategory = {
  _id: string;
  name: string;
};

type Category = {
  _id: string;
  name: string;
  subcategories: Subcategory[];
};

type CategoryObj = {
  _id: string;
  name: string;
};

type Product = StoreProduct & {
  category: string | CategoryObj;
  subcategory?: string | CategoryObj;
  description: {
    en: string;
    fr?: string;
    it?: string;
  };
};

type SortOption = {
  label: string;
  value: string;
};

// Props for the client component
interface ShopClientProps {
  initialProducts: Product[];
  initialCategories: Category[];
}

// Loading placeholder component
function ShopLoading() {
  return <ShopSkeleton />;
}

// Server component that fetches data at BUILD TIME for instant loading
async function fetchShopData() {
  try {
    // Import database connection and models directly
    const { connectToDatabase } = await import('@/lib/mongodb');
    
    // Dynamic imports with error handling
    let ProductModel, CategoryModel;
    try {
      const productModule = await import('@/models/Product');
      const categoryModule = await import('@/models/Category');
      ProductModel = productModule.Product;
      CategoryModel = categoryModule.Category;
    } catch (importError) {
      console.error('Failed to import models:', importError);
      throw new Error('Model import failed');
    }

    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      console.error('Database connection failed:', dbConnection.error);
      return { categories: [], products: [] };
    }

    let categories: Category[] = [];
    let products: Product[] = [];

    // Fetch categories directly from database
    try {
      const categoriesData = await CategoryModel.find({}).sort({ name: 1 }).lean();
      categories = categoriesData.map(cat => ({
        _id: cat._id.toString(),
        name: cat.name,
        subcategories: cat.subcategories || []
      }));
      console.log('Categories fetched directly from DB:', categories.length);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }

    // Fetch products directly from database
    try {
      const productsData = await ProductModel.find({})
        .select('name price imageUrl videoUrl weight shape isFeatured isLatest createdAt category')
        .limit(200)
        .sort({ createdAt: -1 })
        .lean();
      
      products = productsData.map(product => ({
        _id: product._id.toString(),
        name: product.name,
        price: product.price,
        images: product.images || [],
        imageUrl: product.imageUrl,
        videoUrl: product.videoUrl,
        weight: product.weight,
        shape: product.shape,
        category: product.category?.toString() || product.category,
        description: product.description || { en: '' }
      }));
      console.log('Products fetched directly from DB:', products.length);
    } catch (error) {
      console.error('Error fetching products:', error);
    }

    return { categories, products };
  } catch (error) {
    console.error('Error in fetchShopData:', error);
    // Fallback to API calls if direct DB access fails
    return await fetchShopDataViaAPI();
  }
}

// Fallback function using API calls
async function fetchShopDataViaAPI() {
  try {
    // Get the correct base URL for different environments
    const getBaseUrl = () => {
      // In production (Vercel)
      if (process.env.VERCEL_URL) {
        return `https://${process.env.VERCEL_URL}`;
      }
      // Custom production URL
      if (process.env.NEXT_PUBLIC_BASE_URL) {
        return process.env.NEXT_PUBLIC_BASE_URL;
      }
      // Local development
      return 'http://localhost:3000';
    };

    const baseUrl = getBaseUrl();
    console.log('Fallback: Fetching data from API:', baseUrl);
    
    const [categoriesResponse, productsResponse] = await Promise.all([
      fetch(`${baseUrl}/api/categories`, {
        cache: 'force-cache',
        next: { revalidate: 60 }
      }),
      fetch(`${baseUrl}/api/products?fields=homepage&limit=200`, {
        cache: 'force-cache',
        next: { revalidate: 60 }
      })
    ]);

    let categories: Category[] = [];
    let products: Product[] = [];

    if (categoriesResponse.ok) {
      categories = await categoriesResponse.json();
    }

    if (productsResponse.ok) {
      const productsData = await productsResponse.json();
      products = productsData.products ? productsData.products : productsData;
    }

    return { categories, products };
  } catch (error) {
    console.error('API fallback also failed:', error);
    return { categories: [], products: [] };
  }
}

// Wrapper component for client component with Suspense
function ShopClientWrapper({ initialProducts, initialCategories }: ShopClientProps) {
  return (
    <Suspense fallback={<ShopLoading />}>
      <ShopClientComponent initialProducts={initialProducts} initialCategories={initialCategories} />
    </Suspense>
  );
}

// Add revalidation to the page
export const revalidate = 60; // Revalidate every 1 minute

// Main server component - fetches data at BUILD TIME
export default async function ShopPage() {
  // Fetch data at BUILD TIME - no loading for users! 🚀
  const { categories, products } = await fetchShopData();

  return (
      <div id="shop-page-wrapper" className="min-h-screen bg-gradient-to-r from-[#f8f8f8] via-[#f8f8f8] to-[#f8f8f8] ">
      <ShopClientWrapper initialProducts={products} initialCategories={categories} />
      </div>
  );
} 