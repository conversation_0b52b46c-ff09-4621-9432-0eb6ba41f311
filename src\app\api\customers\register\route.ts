import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    // Parse customer data from request
    const customerData = await req.json();
    
    // Validate required fields
    if (!customerData.email || !customerData.firstName || !customerData.lastName || !customerData.phone) {
      return NextResponse.json(
        { error: 'Missing required customer information' },
        { status: 400 }
      );
    }
    
    // Check if customer already exists by email
    const existingCustomer = await Customer.findOne({ email: customerData.email.toLowerCase() });
    
    if (existingCustomer) {
      // Update existing customer information
      existingCustomer.phone = customerData.phone;
      existingCustomer.firstName = customerData.firstName;
      existingCustomer.lastName = customerData.lastName;
      existingCustomer.address = customerData.address;
      
      await existingCustomer.save();
      
      return NextResponse.json({
        message: 'Customer information updated successfully',
        customerId: existingCustomer._id
      });
    } else {
      // Create a new customer
      const newCustomer = new Customer({
        email: customerData.email.toLowerCase(),
        phone: customerData.phone,
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        address: customerData.address,
        orders: [],
        wishlist: []
      });
      
      await newCustomer.save();
      
      return NextResponse.json({
        message: 'Customer registered successfully',
        customerId: newCustomer._id
      }, { status: 201 });
    }
  } catch (error) {
    console.error('Error registering customer:', error);
    return NextResponse.json(
      { error: 'Failed to register customer' },
      { status: 500 }
    );
  }
} 