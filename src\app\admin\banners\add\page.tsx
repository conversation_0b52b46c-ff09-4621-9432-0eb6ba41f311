'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Upload, Image as ImageIcon, AlertTriangle } from 'lucide-react';
import { getImageUrl } from '@/lib/cloudfront';

export default function AddBannerPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Show preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const uploadImage = async (): Promise<string | null> => {
    const file = imageFileInputRef.current?.files?.[0];
    if (!file) return null;

    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/banners/upload', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      if (!result.success) {
        throw new Error('Failed to upload image');
      }
      
      return result.fileKey;
    } catch (err) {
      console.error('Error uploading image:', err);
      throw new Error('Failed to upload image');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get form data
      const formData = new FormData(e.target as HTMLFormElement);
      
      // Upload image if selected
      let imageUrl = uploadedImage;
      if (!imageUrl && imageFileInputRef.current?.files?.[0]) {
        imageUrl = await uploadImage();
        if (!imageUrl) {
          throw new Error('Failed to upload image');
        }
        setUploadedImage(imageUrl);
      }

      // Format date values
      const startDate = formData.get('startDate') as string;
      const endDate = formData.get('endDate') as string;

      // Create banner data object
      const bannerData = {
        title: formData.get('title'),
        description: formData.get('description'),
        type: formData.get('type'),
        position: formData.get('position'),
        status: formData.get('status'),
        linkUrl: formData.get('linkUrl'),
        imageUrl: imageUrl,
        startDate: new Date(startDate).toISOString(),
        endDate: new Date(endDate).toISOString(),
      };

      // Submit the banner data
      const response = await fetch('/api/banners', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bannerData),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to create banner');
      }

      setSuccess(true);
      
      // Navigate back to banners list after delay
      setTimeout(() => {
        router.push('/admin/banners');
      }, 1500);
    } catch (err: any) {
      console.error('Error creating banner:', err);
      setError(err.message || 'Failed to create banner');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link 
          href="/admin/banners" 
          className="mr-4 p-2 rounded-md hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold">Add New Banner</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md flex items-start">
          <AlertTriangle className="mr-2 h-5 w-5 flex-shrink-0" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-100 text-green-700 rounded-md">
          <p>Banner created successfully! Redirecting...</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-medium mb-4">Banner Information</h2>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                    Banner Title *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    required
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter banner title"
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter banner description"
                  ></textarea>
                </div>
                
                <div>
                  <label htmlFor="linkUrl" className="block text-sm font-medium text-gray-700 mb-1">
                    Link URL
                  </label>
                  <input
                    type="url"
                    id="linkUrl"
                    name="linkUrl"
                    className="w-full p-2 border rounded-md"
                    placeholder="https://example.com/page"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                      Type *
                    </label>
                    <select
                      id="type"
                      name="type"
                      required
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Select Type</option>
                      <option value="banner">Banner</option>
                      <option value="popup">Popup</option>
                      <option value="advertisement">Advertisement</option>
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                      Position *
                    </label>
                    <select
                      id="position"
                      name="position"
                      required
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Select Position</option>
                      <option value="hero">Hero (Top)</option>
                      <option value="middle">Middle</option>
                      <option value="bottom">Bottom</option>
                      <option value="popup">Popup</option>
                      <option value="sidebar">Sidebar</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    Status *
                  </label>
                  <select
                    id="status"
                    name="status"
                    required
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="scheduled">Scheduled</option>
                  </select>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date *
                    </label>
                    <input
                      type="date"
                      id="startDate"
                      name="startDate"
                      required
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                      End Date *
                    </label>
                    <input
                      type="date"
                      id="endDate"
                      name="endDate"
                      required
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-lg font-medium mb-4">Banner Image</h2>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
                    Upload Image *
                  </label>
                  <div className="mt-1 flex items-center">
                    <label
                      htmlFor="image-upload"
                      className="flex justify-center items-center p-2 border-2 border-dashed border-gray-300 rounded-md cursor-pointer hover:bg-gray-50"
                    >
                      <div className="space-y-1 text-center">
                        <div className="flex justify-center">
                          <Upload className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="text-sm text-gray-600">
                          <span>Click to upload image</span>
                        </div>
                      </div>
                      <input
                        id="image-upload"
                        name="image"
                        type="file"
                        ref={imageFileInputRef}
                        className="sr-only"
                        accept="image/*"
                        onChange={handleImageChange}
                        required={!uploadedImage}
                      />
                    </label>
                  </div>
                </div>
                
                {imagePreview && (
                  <div className="mt-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Image Preview</h3>
                    <div className="relative overflow-hidden rounded-md aspect-[21/9] bg-gray-100">
                      <img
                        src={imagePreview}
                        alt="Banner preview"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}
                
                {uploadedImage && (
                  <div className="mt-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Uploaded Image</h3>
                    <div className="relative overflow-hidden rounded-md aspect-[21/9] bg-gray-100">
                      <img
                        src={getImageUrl(uploadedImage)}
                        alt="Uploaded banner"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}
                
                <div className="mt-6 bg-yellow-50 p-4 rounded-md text-sm text-yellow-700">
                  <p className="flex items-start">
                    <ImageIcon className="mr-2 h-5 w-5 flex-shrink-0 text-yellow-500" />
                    <span>For best results, use an image with a 21:9 aspect ratio. Recommended size: 1920×810 pixels.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end mt-6 pt-6 border-t">
            <Link
              href="/admin/banners"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-4 hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Banner'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 