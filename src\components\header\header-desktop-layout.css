/* Desktop Header Layout Styles */
@media (max-width: 767px) {
    #header-desktop-layout {
        display: none;
    }
}

@media (min-width: 768px) {
    #header-desktop-layout {
        display: grid;
        grid-template-columns: repeat(12, minmax(0, 1fr));
        align-items: center;
    }

    #header-desktop-logo {
        grid-column: span 2;
        justify-self: start;
    }

    #header-desktop-logo img {
        width: 90px;
        height: auto;
    }

    @media (min-width: 1024px) {
        #header-desktop-logo {
            grid-column: span 2;
        }

        #header-desktop-logo img {
            width: 100px;
        }
    }

    #header-desktop-navigation {
        grid-column: span 8;
        justify-self: center;
    }

    #header-desktop-nav-links {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
    }

    @media (min-width: 768px) {
        #header-desktop-nav-links {
            gap: 0.75rem;
            row-gap: 0.25rem;
        }
    }

    @media (min-width: 1024px) {
        #header-desktop-nav-links {
            gap: 1.25rem;
            row-gap: 0;
        }
    }

    .nav-link {
        padding: 0.25rem 0.5rem;
        text-align: center;
        transition: all 150ms ease-in-out;
        font-size: 0.9375rem;
        white-space: nowrap;
    }

    @media (min-width: 1024px) {
        .nav-link {
            font-size: 1rem;
        }
    }

    .nav-link:active {
        transform: scale(0.95);
    }

    .nav-link-underline {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        border-radius: 9999px;
        transform: scaleX(0);
        transition: transform 300ms ease-out;
    }

    .nav-link:hover .nav-link-underline,
    .nav-link:active .nav-link-underline {
        transform: scaleX(1);
    }

    #header-desktop-actions {
        grid-column: span 2;
        justify-self: end;
    }

    #header-desktop-action-buttons {
        display: flex;
        align-items: center;
        gap: 0;
    }

    .header-action-buttons {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .header-switchers {
        display: flex;
        align-items: center;
    }

    /* Color and state variations */
    .home-hero-text {
        color: white;
    }

    .scrolled-text {
        color: #4b5563;
    }

    .home-hero-bg-transparent {
        background-color: transparent !important;
    }

    .scrolled-bg {
        background-color: #f8f8f8 !important;
    }
}

/* Mobile and Global Header Background Fix */
#main-header.home-hero-bg-transparent {
    background-color: transparent !important;
}

#main-header.scrolled-bg {
    background-color: #f8f8f8 !important;
}

/* Header button styles for all screen sizes */
.home-hero-button {
    color: white;
}

.scrolled-button {
    color: black;
}

.home-hero-button:hover {
    color: #93c5fd;
}

.scrolled-button:hover {
    color: #2563eb;
}

/* Ensure mobile header has correct background */
@media (max-width: 767px) {
    #main-header {
        background-color: #f8f8f8 !important;
    }
    
    #main-header.home-hero-bg-transparent {
        background-color: transparent !important;
    }
}

/* Add styles for dropdown chevron */
.nav-link {
    display: flex;
    align-items: center;
}

.nav-link svg {
    margin-left: 0.25rem;
    transition: transform 0.2s ease;
}

.nav-link:hover svg {
    transform: rotate(180deg);
}

/* Responsive Nav Link Sizing and Spacing in Pixels */
@media (min-width: 768px) and (max-width: 1023px) {
    /* Tablet Styles */
    #header-desktop-nav-links {
        gap: 10px;  /* Smaller gap between nav links */
        row-gap: 4px;
    }

    .nav-link {
        font-size: 14px;  /* Slightly smaller font size */
        padding: 4px 8px;
        margin: 0 4px;  /* Add some horizontal margin */
    }
}

@media (min-width: 1024px) {
    /* Laptop and Larger Screens */
    #header-desktop-nav-links {
        gap: 0px;  /* Wider spacing between nav links */
        row-gap: 0;
    }

    .nav-link {
        font-size: 15px;  /* Standard font size */
        padding: 8px 12px;
        margin: 0 8px;  /* Add horizontal margin */
    }
}

/* Desktop Header Grid Layout */
@media (min-width: 768px) {
    #header-desktop-layout {
        display: grid;
        grid-template-columns: 1fr 5fr 1fr;
        align-items: center;
        gap: 16px; /* Optional: adds some spacing between grid columns */
    }

    #header-desktop-logo {
        grid-column: 1; /* First column */
        justify-self: start;
    }

    #header-desktop-navigation {
        grid-column: 2; /* Middle column */
        justify-self: start;
    }

    #header-desktop-actions {
        grid-column: 3; /* Last column */
        justify-self: end;
    }
}

/* Responsive adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
    #header-desktop-layout {
        gap: 30px; /* Smaller gap on tablet */
    }
}

@media (min-width: 1024px) {
    #header-desktop-layout {
        gap: 10px; /* Standard gap on laptop */
    }
}

/* Responsive Header Action Buttons for Tablets and Laptops */
@media (min-width: 768px) and (max-width: 1023px) {
    #header-desktop-action-buttons {
        gap: 9px; /* Smaller gap between icons on tablets */
    }

    #header-desktop-action-buttons .header-action-buttons button {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #header-desktop-action-buttons .header-action-buttons button svg {
        width: 18px;   /* Slightly larger icon size for tablets */
        height: 18px;
    }

    /* Specific gap between search and cart icons */
    #header-desktop-action-buttons .header-action-buttons button:first-child {
        margin-right: 8px; /* Add space between search and cart icons */
    }
}

@media (min-width: 1024px) {
    #header-desktop-action-buttons {
        gap: 16px; /* Wider gap between icons on laptops */
    }

    #header-desktop-action-buttons .header-action-buttons button {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #header-desktop-action-buttons .header-action-buttons button svg {
        width: 20px;   /* Controlled icon size for laptops */
        height: 20px;/* look at me ::  increase both width and height to increase the size of the icon */
    }
    /* Size control for Language and Currency Switchers */
#header-desktop-action-buttons .header-switchers svg {
    width: 17px;   /* Controlled icon size for language and currency switchers*/
    height: 17px;
}

    /* Specific gap between search and cart icons */
    #header-desktop-action-buttons .header-action-buttons button:first-child {
        margin-right: 16px; /* Add space between search and cart icons */
    }
}



