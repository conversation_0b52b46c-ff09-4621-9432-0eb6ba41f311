import mongoose from 'mongoose';

// Define schema for order items
const OrderItemSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  imageUrl: {
    type: String
  }
});

// Define schema for customer information
const CustomerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true
  },
  phone: {
    type: String
  }
});

// Define schema for shipping address
const ShippingAddressSchema = new mongoose.Schema({
  street: {
    type: String,
    required: true
  },
  city: {
    type: String,
    required: true
  },
  state: {
    type: String,
    required: true
  },
  postalCode: {
    type: String,
    required: true
  },
  country: {
    type: String,
    required: true
  }
});

// Define reward applied schema
const RewardAppliedSchema = new mongoose.Schema({
  id: {
    type: Number,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true
  }
});

// Define main order schema
const OrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  customer: {
    type: CustomerSchema,
    required: true
  },
  items: {
    type: [OrderItemSchema],
    required: true,
    validate: {
      validator: function(items: any[]) {
        return items.length > 0;
      },
      message: 'Order must contain at least one item'
    }
  },
  subtotal: {
    type: Number,
    required: true
  },
  shipping: {
    type: Number,
    required: true
  },
  tax: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['paid', 'unpaid', 'refunded'],
    default: 'unpaid'
  },
  paymentMethod: {
    type: String,
    required: true
  },
  shippingAddress: {
    type: ShippingAddressSchema,
    required: true
  },
  rewardApplied: {
    type: RewardAppliedSchema,
    default: null
  },
  trackingNumber: {
    type: String,
    default: ''
  },
  carrier: {
    type: String,
    default: ''
  },
  estimatedDelivery: {
    type: String
  },
  notes: {
    type: String
  }
}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

// Create a function to generate order numbers
OrderSchema.statics.generateOrderNumber = async function() {
  const count = await this.countDocuments();
  const orderNumber = `ORD-${String(count + 1).padStart(3, '0')}`;
  return orderNumber;
};

// Check if model exists before creating
const Order = mongoose.models.Order || mongoose.model('Order', OrderSchema);

export default Order; 