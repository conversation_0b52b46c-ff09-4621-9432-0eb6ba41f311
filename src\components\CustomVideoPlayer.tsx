'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, Volume1, VolumeX, Maximize } from 'lucide-react';
import styles from './CustomVideoPlayer.module.css';

interface CustomVideoPlayerProps {
  src: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  playsInline?: boolean;
}

const CustomVideoPlayer: React.FC<CustomVideoPlayerProps> = ({
  src,
  className = '',
  autoPlay = false,
  muted = true,
  loop = true,
  playsInline = true,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [isMuted, setIsMuted] = useState(muted);
  const [isDragging, setIsDragging] = useState(false);
  const [wasPlayingBeforeDrag, setWasPlayingBeforeDrag] = useState(false);
  const [volume, setVolume] = useState(1);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateTime = () => {
      // Only update from video timeupdate if not currently dragging
      if (!isDragging) {
        setCurrentTime(video.currentTime);
        setProgress((video.currentTime / video.duration) * 100);
      }
    };

    const updateDuration = () => {
      setDuration(video.duration);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    // Initialize volume state
    setVolume(video.volume);

    video.addEventListener('timeupdate', updateTime);
    video.addEventListener('loadedmetadata', updateDuration);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('timeupdate', updateTime);
      video.removeEventListener('loadedmetadata', updateDuration);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [isDragging]);

  // Separate useEffect for handling global mouse/touch events during dragging
  useEffect(() => {
    const handleGlobalEnd = () => {
      if (isDragging) {
        const video = videoRef.current;
        if (!video) return;

        setIsDragging(false);
        
        if (wasPlayingBeforeDrag) {
          video.play();
        }
      }
    };

    if (isDragging) {
      document.addEventListener('mouseup', handleGlobalEnd);
      document.addEventListener('touchend', handleGlobalEnd);
      document.addEventListener('mouseleave', handleGlobalEnd);
    }

    return () => {
      document.removeEventListener('mouseup', handleGlobalEnd);
      document.removeEventListener('touchend', handleGlobalEnd);
      document.removeEventListener('mouseleave', handleGlobalEnd);
    };
  }, [isDragging, wasPlayingBeforeDrag]);

  // Separate useEffect for handling volume slider clicks outside
  useEffect(() => {
    if (!showVolumeSlider) return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const volumeContainer = document.querySelector(`.${styles.volumeContainer}`);
      if (volumeContainer && !volumeContainer.contains(event.target as Node)) {
        setShowVolumeSlider(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showVolumeSlider]);

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  const handleProgressInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newProgress = parseFloat(e.target.value);
    const newTime = (newProgress / 100) * duration;
    
    // Immediate seeking for ultra-fast response
    if (video.fastSeek) {
      video.fastSeek(newTime);
    } else {
      video.currentTime = newTime;
    }
    
    // Update progress immediately without waiting for video timeupdate
    setProgress(newProgress);
    setCurrentTime(newTime);
  };

  const handleProgressTouchMove = (e: React.TouchEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    const input = e.currentTarget;
    if (!video || !input) return;

    // Calculate progress from touch position
    const rect = input.getBoundingClientRect();
    const touchX = e.touches[0].clientX;
    const relativeX = Math.max(0, Math.min(1, (touchX - rect.left) / rect.width));
    const newProgress = relativeX * 100;
    const newTime = (newProgress / 100) * duration;
    
    // Ultra-fast mobile seeking
    if (video.fastSeek) {
      video.fastSeek(newTime);
    } else {
      video.currentTime = newTime;
    }
    
    // Update states immediately for smooth mobile scrubbing
    setProgress(newProgress);
    setCurrentTime(newTime);
    
    // Update input value for visual feedback
    input.value = newProgress.toString();
  };

  const handleProgressTouchStart = (e: React.TouchEvent) => {
    e.preventDefault(); // Prevent mobile scrolling
    handleDragStart();
  };

  const handleProgressTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    // Touch end is handled by the global event listener
  };

  const handleDragStart = () => {
    const video = videoRef.current;
    if (!video) return;

    setIsDragging(true);
    setWasPlayingBeforeDrag(isPlaying);
    
    if (isPlaying) {
      video.pause();
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    const newMutedState = !video.muted;
    video.muted = newMutedState;
    setIsMuted(newMutedState);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
    
    // If changing from zero or to zero, handle mute state
    if (video.muted && newVolume > 0) {
      video.muted = false;
      setIsMuted(false);
    } else if (newVolume === 0) {
      video.muted = true;
      setIsMuted(true);
    }
  };

  const handleVolumeTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleVolumeTouchMove = (e: React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      video.requestFullscreen();
    }
  };

  const showControlsTemporarily = () => {
    setShowControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, 3000);
  };

  const handleMouseMove = () => {
    showControlsTemporarily();
  };

  const handleVideoClick = () => {
    togglePlayPause();
    showControlsTemporarily();
  };

  const handleVolumeToggle = () => {
    setShowVolumeSlider(!showVolumeSlider);
  };

  return (
    <div className={`relative ${className}`}>
      <div 
        className="relative group"
        onMouseMove={handleMouseMove}
        onMouseLeave={() => {
          if (isPlaying) {
            setShowControls(false);
          }
        }}
      >
        <video
          ref={videoRef}
          src={src}
          className="w-full h-full object-cover cursor-pointer rounded-t-lg"
          autoPlay={autoPlay}
          muted={isMuted}
          loop={loop}
          playsInline={playsInline}
          crossOrigin="anonymous"
          onClick={handleVideoClick}
        />
        
        {/* Center Play Button Overlay (when paused) */}
        {!isPlaying && !isDragging && (
          <div 
            className="absolute inset-0 flex items-center justify-center cursor-pointer"
            onClick={togglePlayPause}
          >
            <div className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center hover:bg-black/70 transition-colors">
              <Play className="w-8 h-8 text-white ml-1" />
            </div>
          </div>
        )}
      </div>
      
      {/* Custom Controls Bar - Always visible at bottom */}
      <div className={styles.controlsContainer}>
        {/* Play/Pause Button */}
        <button 
          onClick={togglePlayPause}
          className={styles.controlButton}
        >
          {isPlaying ? (
            <Pause className="w-6 h-6" />
          ) : (
            <Play className="w-6 h-6" />
          )}
        </button>

        {/* Progress Bar */}
        <div className={styles.progressContainer}>
          <input 
            type="range" 
            min="0" 
            max="100" 
            value={progress} 
            onChange={handleProgressInput}
            onInput={handleProgressInput}
            className={styles.customRange}
            style={{
              background: `linear-gradient(to right, #4b5563 0%, #4b5563 ${progress}%, #d1d5db ${progress}%, #d1d5db 100%)`
            }}
            onMouseDown={handleDragStart}
            onTouchStart={handleProgressTouchStart}
            onTouchMove={handleProgressTouchMove}
            onTouchEnd={handleProgressTouchEnd}
          />
        </div>

        {/* Volume Button and Slider */}
        <div 
          className={styles.volumeContainer}
          onMouseEnter={() => setShowVolumeSlider(true)}
          onMouseLeave={() => setShowVolumeSlider(false)}
        >
          <button 
            onClick={toggleMute}
            onTouchStart={handleVolumeToggle}
            className={styles.controlButton}
            aria-label={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? (
              <VolumeX className="w-6 h-6" />
            ) : volume < 0.5 ? (
              <Volume1 className="w-6 h-6" />
            ) : (
              <Volume2 className="w-6 h-6" />
            )}
            <span className={styles.volumeTooltip}>{isMuted ? "Unmute" : "Adjust volume"}</span>
          </button>

          {/* Vertical Volume Slider */}
          <div className={`${styles.volumeSlider} ${showVolumeSlider ? styles.visible : ''}`}>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01"
              value={volume} 
              onChange={handleVolumeChange}
              className={styles.volumeRange}
              style={{
                background: `linear-gradient(to right, #4b5563 0%, #4b5563 ${volume * 100}%, #d1d5db ${volume * 100}%, #d1d5db 100%)`
              }}
              onTouchStart={handleVolumeTouchStart}
              onTouchMove={handleVolumeTouchMove}
            />
          </div>
        </div>

        {/* Fullscreen Button */}
        <button 
          onClick={toggleFullscreen}
          className={styles.controlButton}
        >
          <Maximize className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default CustomVideoPlayer; 