import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Review } from '@/models/Review';
import { Product } from '@/models/Product';

// Get all reviews with optional filters for status and productId
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const productId = searchParams.get('productId');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (productId) query.productId = productId;
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Get reviews with product details
    const reviews = await Review.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .lean();
    
    // Get product details for each review
    const reviewsWithProductDetails = await Promise.all(
      reviews.map(async (review) => {
        const product = await Product.findById(review.productId).lean();
        return {
          ...review,
          productName: product ? product.name : 'Unknown Product',
          productImageUrl: product ? product.imageUrl : null,
        };
      })
    );
    
    return NextResponse.json({
      success: true,
      reviews: reviewsWithProductDetails
    });
  } catch (error: any) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch reviews'
    }, { status: 500 });
  }
}

// Create a new review
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { productId, customerName, customerEmail, rating, reviewText } = body;
    
    if (!productId || !customerName || !customerEmail || !rating || !reviewText) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }
    
    // Validate rating range
    if (rating < 1 || rating > 5) {
      return NextResponse.json({
        success: false,
        error: 'Rating must be between 1 and 5'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Check if product exists
    const product = await Product.findById(productId);
    
    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }
    
    // Create review
    const newReview = new Review({
      productId,
      customerName,
      customerEmail,
      rating,
      reviewText,
      status: 'pending',
    });
    
    await newReview.save();
    
    return NextResponse.json({
      success: true,
      message: 'Review submitted successfully and pending approval',
      review: newReview
    });
  } catch (error: any) {
    console.error('Error creating review:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create review'
    }, { status: 500 });
  }
} 