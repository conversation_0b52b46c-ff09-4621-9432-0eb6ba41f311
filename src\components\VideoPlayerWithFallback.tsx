'use client';

import React, { useRef, useState, useEffect } from 'react';
import { PlayIcon } from 'lucide-react';

interface VideoPlayerWithFallbackProps {
  src: string;
  className?: string;
  muted?: boolean;
  playsInline?: boolean;
  loop?: boolean;
  crossOrigin?: 'anonymous' | 'use-credentials' | '';
  onPlay?: () => void;
  onPause?: () => void;
  id?: string;
}

const VideoPlayerWithFallback: React.FC<VideoPlayerWithFallbackProps> = ({
  src,
  className = '',
  muted = true,
  playsInline = true,
  loop = false,
  crossOrigin = 'anonymous',
  onPlay,
  onPause,
  id
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check if browser supports the video format
  const isWebM = src.toLowerCase().includes('.webm');
  const isMP4 = src.toLowerCase().includes('.mp4');

  const handlePlay = () => {
    if (videoRef.current) {
      videoRef.current.play().catch(error => {
        console.error('Error playing video:', error);
        setHasError(true);
      });
      setIsPlaying(true);
      onPlay?.();
    }
  };

  const handlePause = () => {
    if (videoRef.current) {
      videoRef.current.pause();
      setIsPlaying(false);
      onPause?.();
    }
  };

  const togglePlay = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isPlaying) {
      handlePause();
    } else {
      handlePlay();
    }
  };

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement>) => {
    console.error('Video error:', e);
    setHasError(true);
    setIsLoading(false);
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleVideoPlay = () => {
    setIsPlaying(true);
  };

  const handleVideoPause = () => {
    setIsPlaying(false);
  };

  // Check browser support for WebM
  useEffect(() => {
    if (isWebM) {
      const video = document.createElement('video');
      const canPlayWebM = video.canPlayType('video/webm') !== '';
      
      if (!canPlayWebM) {
        console.warn(`WebM format may not be supported in this browser for: ${src}`);
        setHasError(true);
      }
    }
  }, [src, isWebM]);

  return (
    <div className={`relative ${className}`}>
      {!hasError ? (
        <>
          <video
            ref={videoRef}
            id={id}
            className="w-full h-full object-cover"
            muted={muted}
            playsInline={playsInline}
            loop={loop}
            crossOrigin={crossOrigin}
            onError={handleVideoError}
            onLoadedData={handleVideoLoad}
            onPlay={handleVideoPlay}
            onPause={handleVideoPause}
            preload="metadata"
          >
            <source src={src} type={isWebM ? 'video/webm' : 'video/mp4'} />
            <p>Your browser does not support the video format.</p>
          </video>

          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
            </div>
          )}

          {/* Play button overlay */}
          {!isPlaying && !isLoading && (
            <div 
              className="absolute inset-0 flex items-center justify-center cursor-pointer bg-black/20 hover:bg-black/30 transition-colors"
              onClick={togglePlay}
            >
              <div className="w-[15%] aspect-square relative bg-black/30 rounded-full flex items-center justify-center hover:bg-black/50 transition-colors">
                <PlayIcon 
                  className="text-white ml-1" 
                  size={24}
                />
              </div>
            </div>
          )}
        </>
      ) : (
        /* Fallback for unsupported formats */
        <div className="w-full h-full bg-gray-200 flex flex-col items-center justify-center p-4">
          <div className="text-center">
            <PlayIcon size={48} className="text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-2">Video format not supported</p>
            <p className="text-xs text-gray-500">
              {isWebM ? 'WebM format requires a modern browser' : 'Video cannot be loaded'}
            </p>
            {/* Fallback: Try to load as MP4 if it was WebM */}
            {isWebM && (
              <button 
                onClick={() => window.open(src, '_blank')}
                className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
              >
                Open Video
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPlayerWithFallback; 