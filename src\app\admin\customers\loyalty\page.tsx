'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, Award, Gift, Settings, RefreshCw, Plus, Edit, Trash, Tag, Star, DollarSign } from 'lucide-react';

interface Customer {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  totalSpent: number;
  orderCount: number;
  lastOrder: string;
  currentRewards: Array<{
    id: number;
    name: string;
    issuedDate: string;
  }>;
  spentRewards: Array<{
    id: number;
    name: string;
    spentDate: string;
    amount: number;
  }>;
}

export default function LoyaltyRewards() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [simulatedAmount, setSimulatedAmount] = useState<number>(0);
  const [issuingRewardFor, setIssuingRewardFor] = useState<string | null>(null);
  const router = useRouter();

  // Sample reward options - keeping this for reward functionality
  const [rewardOptions, setRewardOptions] = useState([
    {
      id: 6,
      name: '$100 Reward',
      spendRequired: 2000,
      description: '$100 off your purchase - Premium reward for spending $2000',
      isActive: true
    },
    {
      id: 7,
      name: '$200 Reward',
      spendRequired: 4000,
      description: '$200 off your purchase - Premium reward for spending $4000',
      isActive: true
    },
    {
      id: 8,
      name: '$350 Reward',
      spendRequired: 6000,
      description: '$350 off your purchase - Elite reward for spending $6000+',
      isActive: true
    }
  ]);

  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      const filtered = customers.filter(customer => 
        customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (customer.phone && customer.phone.includes(searchQuery))
      );
      setFilteredCustomers(filtered);
    } else {
      setFilteredCustomers(customers);
    }
  }, [searchQuery, customers]);

  const fetchCustomers = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch real data from API
      const response = await fetch('/api/customers');
      
      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }
      
      const data = await response.json();
      console.log('Raw customer data structure:', JSON.stringify(data[0])); // Log complete first customer structure
      
      // Now fetch all orders to calculate accurate total spent amounts
      const ordersResponse = await fetch('/api/orders');
      let orders = [];
      
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json();
        orders = ordersData.orders || [];
        console.log('Sample order:', orders.length > 0 ? orders[0] : 'No orders');
      }
      
      // Transform the data to match our interface if needed
      const customersWithRewards = data.map((customer: any) => {
        // Calculate total spent from orders
        let totalSpent = 0;
        
        if (orders.length > 0) {
          // Sum up all orders for this customer
          const customerOrders = orders.filter(
            (order: any) => order.customer.email === customer.email
          );
          
          totalSpent = customerOrders.reduce(
            (sum: number, order: any) => sum + (parseFloat(order.total) || 0), 
            0
          );
          
          console.log(`Customer ${customer.email} total orders:`, customerOrders.length, 'Total spent:', totalSpent);
        }
        
        // If no orders found, try existing fields
        if (totalSpent === 0) {
          if (typeof customer.totalSpent === 'number') {
            totalSpent = customer.totalSpent;
            console.log(`Using existing numeric totalSpent for ${customer.email}: ${totalSpent}`);
          } else if (typeof customer.totalSpent === 'string') {
            totalSpent = parseFloat(customer.totalSpent) || 0;
            console.log(`Using existing string totalSpent for ${customer.email}: ${totalSpent}`);
          } else if (customer.total_spent) {
            totalSpent = typeof customer.total_spent === 'number' ? 
              customer.total_spent : parseFloat(customer.total_spent) || 0;
            console.log(`Using total_spent field for ${customer.email}: ${totalSpent}`);
          } else {
            console.log(`Warning: No order or total spent data for ${customer.email}`);
          }
        }
        
        return {
          ...customer,
          currentRewards: customer.currentRewards || [],
          spentRewards: customer.spentRewards || [], // Initialize spent rewards if not present
          totalSpent: totalSpent,
          orderCount: customer.orderCount || (customer.orders ? customer.orders.length : 0)
        };
      });
      
      console.log('Processed customer data:', customersWithRewards[0]); // Log processed data
      setCustomers(customersWithRewards);
      setFilteredCustomers(customersWithRewards);
    } catch (err) {
      setError('Error loading customer loyalty data. Please try again.');
      console.error('Error fetching customer loyalty data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewCustomer = (customerId: string) => {
    router.push(`/admin/customers/details/${customerId}`);
  };

  // Check if a customer is eligible for a specific reward
  const isEligibleForReward = (totalSpent: number, rewardSpendRequired: number): boolean => {
    // Calculate what multiple of 2000 the customer has reached
    const multiplier = Math.floor(totalSpent / 2000);
    const currentTier = multiplier * 2000;
    
    // Customer is eligible if their spending exactly matches a required tier
    return currentTier === rewardSpendRequired;
  };

  // Issue reward to a customer
  const handleIssueReward = async (customerId: string, rewardId: number, rewardName: string) => {
    try {
      // Set loading state for this specific customer
      setIssuingRewardFor(customerId);
      
      console.log(`Attempting to issue reward: ${rewardName} (ID: ${rewardId}) to customer: ${customerId}`);
      
      // Find the customer
      const customer = customers.find(c => c._id === customerId);
      if (!customer) {
        throw new Error("Customer not found");
      }
      
      // Extract the reward amount from the reward name
      const rewardMatch = rewardName.match(/\$(\d+)/);
      if (!rewardMatch || !rewardMatch[1]) {
        throw new Error("Invalid reward format");
      }
      
      const newRewardAmount = parseInt(rewardMatch[1], 10);
      
      // Calculate existing reward amount
      let currentRewardAmount = 0;
      if (customer.currentRewards && customer.currentRewards.length > 0) {
        const existingRewardMatch = customer.currentRewards[0].name.match(/\$(\d+)/);
        if (existingRewardMatch && existingRewardMatch[1]) {
          currentRewardAmount = parseInt(existingRewardMatch[1], 10);
        }
      }
      
      // Calculate combined reward amount
      const combinedRewardAmount = currentRewardAmount + newRewardAmount;
      const combinedRewardName = `$${combinedRewardAmount} Reward`;
      
      console.log(`Adding ${newRewardAmount} to existing ${currentRewardAmount} for a total of ${combinedRewardAmount}`);
      
      // Make API call to save the combined reward to the database
      const response = await fetch(`/api/customers/${customerId}/rewards`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rewardId: Date.now(), // Generate a new unique ID
          rewardName: combinedRewardName,
          issuedDate: new Date().toISOString().split('T')[0],
          replaceAllRewards: true // Replace the existing reward with the combined one
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        console.error('API returned error:', data);
        console.error('Response status:', response.status);
        console.error('Response body:', data);
        throw new Error(data.error || 'Failed to save reward');
      }

      console.log('Reward issued successfully:', data);

      // Update local state only after successful API call
      setCustomers(prevCustomers => 
        prevCustomers.map(customer => {
          if (customer._id === customerId) {
            // Replace with the combined reward
            return {
              ...customer,
              currentRewards: [{
                id: Date.now(), 
                name: combinedRewardName, 
                issuedDate: new Date().toISOString().split('T')[0]
              }]
            };
          }
          return customer;
        })
      );

      // Update filtered customers as well
      setFilteredCustomers(prevCustomers => 
        prevCustomers.map(customer => {
          if (customer._id === customerId) {
            // Replace with the combined reward
            return {
              ...customer,
              currentRewards: [{
                id: Date.now(), 
                name: combinedRewardName, 
                issuedDate: new Date().toISOString().split('T')[0]
              }]
            };
          }
          return customer;
        })
      );
    } catch (err: any) {
      console.error('Error issuing reward:', err);
      alert(`Failed to issue reward: ${err.message || 'Unknown error'}. Please try again.`);
    } finally {
      // Clear loading state
      setIssuingRewardFor(null);
    }
  };

  // Get eligible rewards that are not already issued
  const getEligibleRewards = (customer: Customer) => {
    try {
      // Handle totalSpent properly - ensure we have a number and round down to nearest multiple of 2000
      const totalSpentValue = typeof customer.totalSpent === 'string' 
        ? parseFloat(customer.totalSpent) 
        : customer.totalSpent;
        
      // Calculate what multiple of 2000 the customer has reached
      const multiplier = Math.floor(totalSpentValue / 2000);
      
      console.log(`Customer ${customer.email} has spent $${totalSpentValue}, which is ${multiplier} multiples of $2000`);
      
      // If the total spent is not at least 2000, no rewards
      if (multiplier < 1) {
        return [];
      }
      
      // Calculate the total reward amount the customer should have earned based on spending
      const totalEarnedAmount = multiplier * 100;
      console.log(`Customer ${customer.email} should have total earned: $${totalEarnedAmount}`);
      
      // Calculate current rewards amount
      let currentRewardAmount = 0;
      if (customer.currentRewards && customer.currentRewards.length > 0) {
        // Extract the dollar amount from the reward name (e.g., "$100 Reward" → 100)
        const match = customer.currentRewards[0].name.match(/\$(\d+)/);
        if (match && match[1]) {
          currentRewardAmount = parseInt(match[1], 10);
        }
      }
      console.log(`Customer ${customer.email} current reward amount: $${currentRewardAmount}`);
      
      // Calculate spent rewards amount
      let spentRewardAmount = 0;
      if (customer.spentRewards && customer.spentRewards.length > 0) {
        spentRewardAmount = customer.spentRewards.reduce((total, reward) => total + reward.amount, 0);
      }
      console.log(`Customer ${customer.email} spent reward amount: $${spentRewardAmount}`);
      
      // Calculate what should be available (total earned minus what's current and what's spent)
      const availableRewardAmount = totalEarnedAmount - currentRewardAmount - spentRewardAmount;
      console.log(`Customer ${customer.email} available for additional: $${availableRewardAmount}`);
      
      // If there's nothing available, no new rewards
      if (availableRewardAmount <= 0) {
        console.log(`Customer ${customer.email} has no additional rewards available`);
        return [];
      }
      
      const rewardName = `$${availableRewardAmount} Reward`;
      
      // Create a reward option for this amount
      const rewardId = Date.now(); // Generate a unique ID
      
      const additionalReward = {
        id: rewardId,
        name: rewardName,
        spendRequired: totalSpentValue,
        description: `Additional $${availableRewardAmount} reward based on your spending`,
        isActive: true
      };
      
      console.log(`Offering reward: ${rewardName} to ${customer.email}`);
      return [additionalReward];
    } catch (error) {
      console.error("Error in getEligibleRewards:", error);
      return [];
    }
  };

  // Handle delete reward
  const handleDeleteReward = async (rewardId: number) => {
    try {
      // Make API call to remove the reward
      const response = await fetch(`/api/rewards/${rewardId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete reward');
      }

      // Only update UI after successful API call
      // Remove the reward from the options
      setRewardOptions(prevRewards => prevRewards.filter(reward => reward.id !== rewardId));
      
      // Also update customers to remove any issued rewards with this ID
      setCustomers(prevCustomers => 
        prevCustomers.map(customer => ({
          ...customer,
          currentRewards: customer.currentRewards.filter(reward => reward.id !== rewardId)
        }))
      );
      
      // Update filtered customers as well
      setFilteredCustomers(prevCustomers => 
        prevCustomers.map(customer => ({
          ...customer,
          currentRewards: customer.currentRewards.filter(reward => reward.id !== rewardId)
        }))
      );
    } catch (err) {
      console.error('Error deleting reward:', err);
      alert('Failed to delete reward. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Loyalty & Rewards Program</h1>
        <button 
          onClick={fetchCustomers}
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          <RefreshCw size={18} className="mr-2" />
          Refresh
        </button>
      </div>

      {/* Search bar */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search size={18} className="text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search customers by email, name or phone..."
          className="pl-10 pr-4 py-2 w-full border rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-10 text-red-500">{error}</div>
      ) : filteredCustomers.length === 0 ? (
        <div className="text-center py-10 text-gray-500">
          {searchQuery ? 'No customers match your search criteria.' : 'No customers found.'}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border rounded-lg overflow-hidden">
            <thead className="bg-gray-100 text-gray-700">
              <tr>
                <th className="py-3 px-4 text-left">Customer</th>
                <th className="py-3 px-4 text-left">Email</th>
                <th className="py-3 px-4 text-left">Orders</th>
                <th className="py-3 px-4 text-left">Total Spent</th>
                <th className="py-3 px-4 text-left">Current Rewards</th>
                <th className="py-3 px-4 text-left">Spent Rewards</th>
                <th className="py-3 px-4 text-left">Eligible to Reward</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCustomers.map((customer) => (
                <tr key={customer._id}>
                  <td className="py-3 px-4">
                    {customer.firstName} {customer.lastName}
                  </td>
                  <td className="py-3 px-4">{customer.email}</td>
                  <td className="py-3 px-4">{customer.orderCount}</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      <DollarSign size={16} className="text-green-500 mr-1" />
                      <span>${(customer.totalSpent || 0).toFixed(2)}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    {customer.currentRewards && customer.currentRewards.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        <span className="inline-block px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                          {customer.currentRewards[0].name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">No active rewards</span>
                    )}
                  </td>
                  <td className="py-3 px-4">
                    {customer.spentRewards && customer.spentRewards.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        <span className="inline-block px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                          ${customer.spentRewards.reduce((total, reward) => total + reward.amount, 0)} Used
                        </span>
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">No spent rewards</span>
                    )}
                  </td>
                  <td className="py-3 px-4">
                    {getEligibleRewards(customer).length > 0 ? (
                      <button
                        onClick={() => {
                          const reward = getEligibleRewards(customer)[0];
                          console.log('Issuing reward to customer:', {
                            customerId: customer._id,
                            customerEmail: customer.email,
                            rewardId: reward.id,
                            rewardName: reward.name
                          });
                          handleIssueReward(customer._id, reward.id, reward.name);
                        }}
                        disabled={issuingRewardFor === customer._id}
                        className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm flex items-center"
                      >
                        {issuingRewardFor === customer._id ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <Award size={14} className="mr-1"/> 
                            Issue {getEligibleRewards(customer)[0].name}
                          </>
                        )}
                      </button>
                    ) : (
                      <span className="text-xs text-gray-500">No eligible rewards</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
} 