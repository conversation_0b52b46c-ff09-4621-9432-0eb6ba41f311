import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';

interface Params {
  params: {
    id: string;
  };
}

// GET /api/admin/users/[id] - Get a specific user
export async function GET(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const user = await User.findById(id)
      .select('-password')
      .populate('role')
      .lean();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users/[id] - Update a user
export async function PUT(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const updates = await request.json();
    
    // If email is being updated, check if it already exists
    if (updates.email) {
      const existingUser = await User.findOne({ 
        email: updates.email.toLowerCase(),
        _id: { $ne: id } // not equal to the current user ID
      });
      
      if (existingUser) {
        return NextResponse.json(
          { error: 'A user with this email already exists' },
          { status: 409 }
        );
      }
    }
    
    // If password is provided, let mongoose middleware handle hashing
    // Otherwise, exclude password field from updates
    if (!updates.password) {
      delete updates.password;
    }
    
    // Update the user
    const updatedUser = await User.findByIdAndUpdate(
      id,
      updates,
      { new: true, runValidators: true }
    )
      .select('-password')
      .populate('role')
      .lean();
    
    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete a user
export async function DELETE(request: Request, { params }: Params) {
  try {
    await dbConnect();
    
    const { id } = params;
    const deletedUser = await User.findByIdAndDelete(id).lean();
    
    if (!deletedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
} 