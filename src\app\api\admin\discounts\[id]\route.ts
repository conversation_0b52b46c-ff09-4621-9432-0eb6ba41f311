import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Discount from '@/models/Discount';
import { withAuth } from '@/lib/auth';

// GET - retrieve a specific discount
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const discount = await Discount.findById(params.id);
    
    if (!discount) {
      return NextResponse.json({ error: 'Discount not found' }, { status: 404 });
    }
    
    return NextResponse.json(discount);
  } catch (error) {
    console.error('Error fetching discount:', error);
    return NextResponse.json({ error: 'Failed to fetch discount' }, { status: 500 });
  }
}

// PUT - update a discount
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.code || !data.type || data.value === undefined || !data.startDate || !data.endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if code already exists (excluding this discount)
    const existingDiscount = await Discount.findOne({ 
      code: data.code.toUpperCase(),
      _id: { $ne: params.id }
    });
    
    if (existingDiscount) {
      return NextResponse.json(
        { error: 'Discount code already exists' },
        { status: 400 }
      );
    }
    
    // Update the discount
    const updatedDiscount = await Discount.findByIdAndUpdate(
      params.id,
      {
        ...data,
        code: data.code.toUpperCase()
      },
      { new: true, runValidators: true }
    );
    
    if (!updatedDiscount) {
      return NextResponse.json({ error: 'Discount not found' }, { status: 404 });
    }
    
    return NextResponse.json(updatedDiscount);
  } catch (error) {
    console.error('Error updating discount:', error);
    return NextResponse.json({ error: 'Failed to update discount' }, { status: 500 });
  }
}

// DELETE - delete a discount
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const discount = await Discount.findByIdAndDelete(params.id);
    
    if (!discount) {
      return NextResponse.json({ error: 'Discount not found' }, { status: 404 });
    }
    
    return NextResponse.json({ message: 'Discount deleted successfully' });
  } catch (error) {
    console.error('Error deleting discount:', error);
    return NextResponse.json({ error: 'Failed to delete discount' }, { status: 500 });
  }
} 