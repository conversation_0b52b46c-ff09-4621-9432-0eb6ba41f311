// Simple test script to check API response
const fetch = require('node-fetch');

async function testAPI() {
  try {
    console.log('Testing /api/products endpoint...');
    const response = await fetch('http://localhost:3000/api/products');
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response success:', data.success);
    console.log('Number of products:', data.products ? data.products.length : 0);
    
    if (data.products && data.products.length > 0) {
      console.log('\nFirst product sample:');
      const firstProduct = data.products[0];
      console.log('Name:', firstProduct.name);
      console.log('Category:', firstProduct.category);
      console.log('Subcategory:', firstProduct.subcategory);
      console.log('Subcategory type:', typeof firstProduct.subcategory);
      
      // Find a product with subcategory
      const productWithSubcategory = data.products.find(p => p.subcategory);
      if (productWithSubcategory) {
        console.log('\nProduct with subcategory:');
        console.log('Name:', productWithSubcategory.name);
        console.log('Category:', productWithSubcategory.category);
        console.log('Subcategory:', productWithSubcategory.subcategory);
        console.log('Subcategory type:', typeof productWithSubcategory.subcategory);
      } else {
        console.log('\nNo products found with subcategory!');
        console.log('All products subcategory status:');
        data.products.forEach((p, i) => {
          console.log(`${i + 1}. ${p.name}: subcategory = ${p.subcategory || 'undefined'}`);
        });
      }
    }
    
    console.log('\nTesting /api/categories endpoint...');
    const categoriesResponse = await fetch('http://localhost:3000/api/categories');
    const categoriesData = await categoriesResponse.json();
    
    console.log('Categories count:', categoriesData.length);
    if (categoriesData.length > 0) {
      const firstCategory = categoriesData[0];
      console.log('First category:', firstCategory.name);
      console.log('Subcategories count:', firstCategory.subcategories ? firstCategory.subcategories.length : 0);
      if (firstCategory.subcategories && firstCategory.subcategories.length > 0) {
        console.log('First subcategory:', firstCategory.subcategories[0]);
      }
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();
