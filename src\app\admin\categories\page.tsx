'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Edit, Plus, Trash, Save, X } from 'lucide-react';

type Subcategory = {
  _id: string;
  name: string;
};

type Category = {
  _id: string;
  name: string;
  subcategories: Subcategory[];
};

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingSubcategory, setEditingSubcategory] = useState<string | null>(null);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [addingCategory, setAddingCategory] = useState(false);
  const [addingSubcategory, setAddingSubcategory] = useState<string | null>(null);

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Initialize expandedCategories when categories are loaded
  // All categories will be collapsed by default
  useEffect(() => {
    const expanded: Record<string, boolean> = {};
    categories.forEach(category => {
      expanded[category._id] = false;
    });
    setExpandedCategories(expanded);
  }, [categories]);

  // Fetch categories from the API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/categories');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const startEditingCategory = (categoryId: string, currentName: string) => {
    setEditingCategory(categoryId);
    setNewCategoryName(currentName);
  };

  const startEditingSubcategory = (subcategoryId: string, currentName: string) => {
    setEditingSubcategory(subcategoryId);
    setNewSubcategoryName(currentName);
  };

  const saveCategory = async (categoryId: string) => {
    if (newCategoryName.trim() === '') return;
    
    try {
      setError(null);
      
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newCategoryName }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => prev.map(category => 
        category._id === categoryId ? updatedCategory : category
      ));
      
      setEditingCategory(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error updating category:', err);
    }
  };

  const saveSubcategory = async (categoryId: string, subcategoryId: string) => {
    if (newSubcategoryName.trim() === '') return;
    
    try {
      setError(null);
      
      const response = await fetch(`/api/categories/${categoryId}/subcategories/${subcategoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newSubcategoryName }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update subcategory');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => prev.map(category => 
        category._id === categoryId ? updatedCategory : category
      ));
      
      setEditingSubcategory(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error updating subcategory:', err);
    }
  };

  const deleteCategory = async (categoryId: string) => {
    try {
      setError(null);
      
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete category');
      }
      
      // Update local state
      setCategories(prev => prev.filter(category => category._id !== categoryId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error deleting category:', err);
    }
  };

  const deleteSubcategory = async (categoryId: string, subcategoryId: string) => {
    try {
      setError(null);
      
      const response = await fetch(`/api/categories/${categoryId}/subcategories/${subcategoryId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete subcategory');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => prev.map(category => 
        category._id === categoryId ? updatedCategory : category
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error deleting subcategory:', err);
    }
  };

  const startAddingCategory = () => {
    setAddingCategory(true);
    setNewCategoryName('');
  };

  const startAddingSubcategory = (categoryId: string) => {
    setAddingSubcategory(categoryId);
    setNewSubcategoryName('');
  };

  const saveNewCategory = async () => {
    if (newCategoryName.trim() === '') return;
    
    try {
      setError(null);
      
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newCategoryName }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }
      
      const newCategory = await response.json();
      
      // Update local state
      setCategories(prev => [...prev, newCategory]);
      
      setAddingCategory(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error creating category:', err);
    }
  };

  const saveNewSubcategory = async (categoryId: string) => {
    if (newSubcategoryName.trim() === '') return;
    
    try {
      setError(null);
      
      const response = await fetch(`/api/categories/${categoryId}/subcategories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newSubcategoryName }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create subcategory');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => prev.map(category => 
        category._id === categoryId ? updatedCategory : category
      ));
      
      setAddingSubcategory(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error creating subcategory:', err);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Categories Management</h1>
        <button 
          onClick={startAddingCategory}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus size={16} className="mr-2" />
          Add Category
        </button>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-100 rounded-md text-red-600">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
        </div>
      ) : (
        <>
          {/* Adding new category */}
          {addingCategory && (
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-md">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Add New Category</h3>
                <button onClick={() => setAddingCategory(false)} className="text-gray-500 hover:text-gray-700">
                  <X size={18} />
                </button>
              </div>
              <div className="mt-2 flex gap-2">
                <input
                  type="text"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="Category name"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button 
                  onClick={saveNewCategory}
                  className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
                >
                  <Save size={16} className="mr-2" />
                  Save
                </button>
              </div>
            </div>
          )}

          {categories.length === 0 ? (
            <div className="py-20 text-center">
              <p className="text-gray-500">No categories found. Click "Add Category" to create one.</p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="divide-y divide-gray-200">
                {categories.map(category => (
                  <div key={category._id} className="p-0">
                    <div 
                      className="p-4 bg-gray-50 flex justify-between items-center cursor-pointer hover:bg-gray-100"
                      onClick={(e) => {
                        // Prevent toggling when clicking on edit/delete buttons or input fields
                        if (
                          e.target instanceof HTMLButtonElement ||
                          e.target instanceof HTMLInputElement ||
                          (e.target instanceof Element && 
                           (e.target.closest('button') || e.target.closest('input')))
                        ) {
                          return;
                        }
                        toggleCategory(category._id);
                      }}
                    >
                      <div className="flex items-center">
                        <span className="mr-2 text-gray-500">
                          {expandedCategories[category._id] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                        </span>
                        
                        {editingCategory === category._id ? (
                          <input
                            type="text"
                            value={newCategoryName}
                            onChange={(e) => setNewCategoryName(e.target.value)}
                            className="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                          />
                        ) : (
                          <h3 className="font-medium">{category.name}</h3>
                        )}
                      </div>
                      
                      <div onClick={(e) => e.stopPropagation()} className="flex space-x-2">
                        {editingCategory === category._id ? (
                          <>
                            <button 
                              onClick={() => saveCategory(category._id)}
                              className="p-1 text-green-600 hover:text-green-800"
                            >
                              <Save size={18} />
                            </button>
                            <button 
                              onClick={() => setEditingCategory(null)}
                              className="p-1 text-gray-600 hover:text-gray-800"
                            >
                              <X size={18} />
                            </button>
                          </>
                        ) : (
                          <>
                            <button 
                              onClick={() => startEditingCategory(category._id, category.name)}
                              className="p-1 text-blue-600 hover:text-blue-800"
                            >
                              <Edit size={18} />
                            </button>
                            <button 
                              onClick={() => deleteCategory(category._id)}
                              className="p-1 text-red-600 hover:text-red-800"
                            >
                              <Trash size={18} />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {expandedCategories[category._id] && (
                      <div className="pl-8 pr-4 py-2 space-y-2">
                        {/* Subcategories list */}
                        {category.subcategories.length === 0 ? (
                          <p className="text-gray-500 italic text-sm p-2">No subcategories</p>
                        ) : (
                          category.subcategories.map(subcategory => (
                            <div key={subcategory._id} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded-md">
                              {editingSubcategory === subcategory._id ? (
                                <input
                                  type="text"
                                  value={newSubcategoryName}
                                  onChange={(e) => setNewSubcategoryName(e.target.value)}
                                  className="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  autoFocus
                                />
                              ) : (
                                <span>{subcategory.name}</span>
                              )}
                              
                              <div className="flex space-x-2">
                                {editingSubcategory === subcategory._id ? (
                                  <>
                                    <button 
                                      onClick={() => saveSubcategory(category._id, subcategory._id)}
                                      className="p-1 text-green-600 hover:text-green-800"
                                    >
                                      <Save size={16} />
                                    </button>
                                    <button 
                                      onClick={() => setEditingSubcategory(null)}
                                      className="p-1 text-gray-600 hover:text-gray-800"
                                    >
                                      <X size={16} />
                                    </button>
                                  </>
                                ) : (
                                  <>
                                    <button 
                                      onClick={() => startEditingSubcategory(subcategory._id, subcategory.name)}
                                      className="p-1 text-blue-600 hover:text-blue-800"
                                    >
                                      <Edit size={16} />
                                    </button>
                                    <button 
                                      onClick={() => deleteSubcategory(category._id, subcategory._id)}
                                      className="p-1 text-red-600 hover:text-red-800"
                                    >
                                      <Trash size={16} />
                                    </button>
                                  </>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                        
                        {/* Add new subcategory */}
                        {addingSubcategory === category._id ? (
                          <div className="flex justify-between items-center p-2 bg-blue-50 rounded-md">
                            <input
                              type="text"
                              value={newSubcategoryName}
                              onChange={(e) => setNewSubcategoryName(e.target.value)}
                              placeholder="Subcategory name"
                              className="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              autoFocus
                            />
                            
                            <div className="flex space-x-2">
                              <button 
                                onClick={() => saveNewSubcategory(category._id)}
                                className="p-1 text-green-600 hover:text-green-800"
                              >
                                <Save size={16} />
                              </button>
                              <button 
                                onClick={() => setAddingSubcategory(null)}
                                className="p-1 text-gray-600 hover:text-gray-800"
                              >
                                <X size={16} />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <button 
                            onClick={() => startAddingSubcategory(category._id)}
                            className="flex items-center text-blue-600 hover:text-blue-800 p-2"
                          >
                            <Plus size={16} className="mr-1" />
                            Add Subcategory
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
} 