import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function GET() {
  try {
    await dbConnect();
    
    // Get the first customer
    const customer = await Customer.findOne();
    
    if (!customer) {
      return NextResponse.json({ error: 'No customers found' }, { status: 404 });
    }
    
    // Add a test reward
    const testReward = {
      id: 999,
      name: 'Test Reward',
      issuedDate: new Date().toISOString().split('T')[0]
    };
    
    // Check if reward schema field exists
    if (!customer.currentRewards) {
      // Create the array if it doesn't exist
      customer.currentRewards = [];
    }
    
    // Add the reward
    customer.currentRewards.push(testReward);
    
    // Save the customer
    await customer.save();
    
    return NextResponse.json({
      success: true,
      message: 'Test reward added successfully',
      customerId: customer._id,
      customerEmail: customer.email,
      reward: testReward
    });
  } catch (error: any) {
    console.error('Error adding test reward:', error);
    return NextResponse.json(
      { error: 'Failed to add test reward', details: error.message },
      { status: 500 }
    );
  }
} 