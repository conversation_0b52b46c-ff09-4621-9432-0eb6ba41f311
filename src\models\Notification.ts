import mongoose, { Schema } from 'mongoose';

// Define the Notification schema
const notificationSchema = new Schema({
  type: {
    type: String,
    enum: ['order', 'promotional', 'user_activity'],
    required: true,
  },
  subType: {
    type: String,
    required: true,
    // Examples: order_placed, order_shipped, new_product, etc.
  },
  title: {
    type: String,
    required: true,
  },
  body: {
    type: String,
    required: true,
  },
  data: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {},
    // Additional data to be sent with the notification
  },
  icon: {
    type: String,
    default: '/logo.png',
  },
  image: {
    type: String,
    // Optional image for rich notifications
  },
  clickAction: {
    type: String,
    default: '/',
    // URL to open when notification is clicked
  },
  recipients: {
    type: [{
      userId: String,      // User ID
      fcmToken: String,    // Firebase Cloud Messaging token
      status: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed'],
        default: 'pending',
      },
      sentAt: Date,
      deliveredAt: Date,
      openedAt: Date,
      error: String,
    }],
    default: [],
  },
  scheduledAt: {
    type: Date,
    // When the notification is scheduled to be sent
  },
  status: {
    type: String,
    enum: ['scheduled', 'sending', 'sent', 'failed', 'cancelled'],
    default: 'scheduled',
  },
  stats: {
    total: { type: Number, default: 0 },
    sent: { type: Number, default: 0 },
    delivered: { type: Number, default: 0 },
    opened: { type: Number, default: 0 },
    failed: { type: Number, default: 0 },
  },
  ttl: {
    type: Number,
    default: 2419200, // 4 weeks in seconds
    // Time to live for the notification in seconds
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add pre-save hook to update timestamps
notificationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create or get the Notification model
const Notification = mongoose.models.Notification || mongoose.model('Notification', notificationSchema);

export default Notification; 