import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

/**
 * API route that checks the MongoDB connection status
 * Returns a JSON response with connection information
 * 
 * GET /api/check-connection
 */
export async function GET() {
  try {
    // Attempt to connect to the database
    const dbConnection = await connectToDatabase();
    
    // If connection was successful, return success status
    if (dbConnection.status === 'connected') {
      return NextResponse.json({
        status: 'connected',
        error: null
      });
    } 
    
    // If connection failed, return error details
    return NextResponse.json({
      status: 'error',
      error: dbConnection.error
    });
  } catch (error: any) {
    // Handle any unexpected errors
    console.error('Unexpected error when checking connection:', error);
    
    return NextResponse.json({
      status: 'error',
      error: {
        message: error.message || 'An unexpected error occurred',
        name: error.name || 'Error',
        explanation: 'There was an unexpected error when trying to check the database connection.'
      }
    }, { status: 500 });
  }
} 