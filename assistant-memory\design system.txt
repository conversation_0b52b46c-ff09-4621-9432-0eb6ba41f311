DESIGN SYSTEM

1. TYPOGRAPHY
   Base Font: System default sans-serif
   Sizes:
   - xs: 0.75rem (12px)  [Used in prices, small text]
   - sm: 0.875rem (14px) [Used in buttons, secondary text]
   - base: 1rem (16px)   [Default body text]
   - lg: 1.125rem (18px) [Used in product names]
   Font Weights:
   - Regular: 400
   - Medium: 500 [Used in prices, buttons]
   - Semibold: 600 [Used in product names]

2. SPACING
   Base unit: 0.25rem (4px)
   Common spacing values:
   - 1.5: 0.375rem (6px)  [Minimal gap between elements]
   - 2: 0.5rem (8px)      [Small component padding]
   - 2.5: 0.625rem (10px) [Medium component padding]
   - 3: 0.75rem (12px)    [Standard padding]
   - 4: 1rem (16px)       [Large padding]
   
   Vertical Spacing:
   - Between name & price: mt-1.5 (sm), mt-2 (md)
   - Between price & button: mt-2.5 (sm), mt-3 (md), mt-4 (lg)

3. COLORS
   Primary:
   - Gold gradient: from-[#E6C374] to-white
   - Black: text-black
   Text:
   - Body: text-gray-500
   - Prices: text-black
   Background:
   - Card: bg-white
   Border:
   - Card: border-gray-100

4. COMPONENTS
   Cards:
   - Border radius: rounded-lg
   - Shadow: shadow-md
   - Hover: shadow-lg
   - Transitions: transition-all duration-300

   Buttons:
   - Border radius: rounded-full
   - Padding: py-1.5 (sm), py-2 (md)
   - Hover effects: scale-[1.02]
   - Active effects: scale-[0.98]

5. RESPONSIVE BREAKPOINTS
   - Default: Mobile first design
   - sm: 640px
   - md: 768px
   - lg: 1024px

6. IMAGES
   - Aspect ratio: 4/3 (mobile), 1/1 (sm+)
   - Quality: 85
   - Hover: scale-105
   - Transition: duration-300

7. CONTAINER LAYOUT
   - Display: flex
   - Direction: flex-col
   - Height: h-full
   - Overflow: overflow-hidden

8. GRID LAYOUT
   - Featured sections: grid grid-cols-2 (mobile), grid-cols-4 (md+)
   - Featured large item: col-span-2
   - Section grid gap: gap-4 (mobile), gap-6 (md+)
   - Two-column layout: grid-cols-1 (mobile), grid-cols-2 (md+)
   - Content alignment: items-center justify-between
   - Responsive padding: px-4 py-8 (mobile), px-6 py-12 (md), px-8 py-16 (lg)

9. COMMON LAYOUT PATTERNS
   Page Structure:
   - Max width container: max-w-7xl mx-auto px-4 sm:px-6 lg:px-8
   - Section spacing: py-8 sm:py-12 lg:py-16
   - Narrow content: max-w-3xl mx-auto

   Component Patterns:
   - Card grid: grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6
   - Content with sidebar: grid grid-cols-1 lg:grid-cols-3 gap-8 (content: lg:col-span-2)
   - Hero section: relative h-[50vh] sm:h-[60vh] lg:h-[70vh]
   - Two-column feature: grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12 items-center
   - Three-column highlight: grid grid-cols-1 sm:grid-cols-3 gap-8
   
   Stacking Context:
   - Navbar: z-50
   - Modals/overlays: z-40
   - Dropdowns: z-30
   - Sticky elements: z-20
   - Default content: z-10

   Responsive Behavior:
   - Hiding elements: hidden sm:block (hide on mobile)
   - Alternative layouts: flex-col md:flex-row (stack on mobile, row on desktop)
   - Column reverse: flex-col-reverse md:flex-row (reverse on mobile)
   - Responsive spacing: space-y-4 md:space-y-0 md:space-x-6

This design system follows modern web standards and Tailwind CSS best practices while maintaining consistency with the existing implementation.





