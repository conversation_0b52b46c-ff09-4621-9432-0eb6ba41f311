import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const { customerId } = await req.json();
    
    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID is required' },
        { status: 400 }
      );
    }
    
    // Check if customer exists
    const customer = await Customer.findById(customerId).lean();
    
    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    // Customer exists, return success response
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error verifying customer:', error);
    return NextResponse.json(
      { error: 'Authentication verification failed' },
      { status: 500 }
    );
  }
} 