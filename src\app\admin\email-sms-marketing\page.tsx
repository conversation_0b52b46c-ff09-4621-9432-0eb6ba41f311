'use client';

import { useState } from 'react';
import { Send, Mail, MessageSquare, Calendar, Clock, Users, AlertTriangle, History } from 'lucide-react';
import Link from 'next/link';

export default function EmailSmsMarketingPage() {
  const [campaignType, setCampaignType] = useState<'email' | 'sms'>('email');
  const [formData, setFormData] = useState({
    subject: '',
    content: '',
    recipientGroup: 'all',
    manualRecipients: '',
    manualPhoneNumbers: '',
    scheduledDate: '',
    scheduledTime: '',
    sendNow: true,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));

    // If sendNow is checked, clear the scheduled date/time fields
    if (name === 'sendNow' && checked) {
      setFormData(prev => ({
        ...prev,
        scheduledDate: '',
        scheduledTime: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate form
      if (!formData.subject && campaignType === 'email') {
        throw new Error('Subject is required for email campaigns');
      }
      if (!formData.content) {
        throw new Error('Content is required');
      }
      
      // Validate recipients based on campaign type
      if (campaignType === 'email') {
        if (formData.recipientGroup === 'manual' && !formData.manualRecipients) {
          throw new Error('Please enter at least one email address for the manual recipient list');
        }
      } else { // SMS campaign
        if (formData.recipientGroup === 'manual' && !formData.manualPhoneNumbers) {
          throw new Error('Please enter at least one phone number for the manual recipient list');
        }
      }
      
      if (!formData.sendNow && (!formData.scheduledDate || !formData.scheduledTime)) {
        throw new Error('Scheduled date and time are required when not sending immediately');
      }

      // Prepare data for the API
      const apiData = {
        ...formData,
        campaignType,
        // Use the appropriate manual recipients field based on campaign type
        manualRecipients: campaignType === 'email' ? formData.manualRecipients : formData.manualPhoneNumbers
      };

      // Call the API to send or schedule the campaign
      const response = await fetch('/api/email-campaign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send campaign');
      }

      // Show success message
      setSuccess(data.message || `Your ${campaignType} campaign has been ${formData.sendNow ? 'sent' : 'scheduled'} successfully!`);
      
      // Reset form
      setFormData({
        subject: '',
        content: '',
        recipientGroup: 'all',
        manualRecipients: '',
        manualPhoneNumbers: '',
        scheduledDate: '',
        scheduledTime: '',
        sendNow: true,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while sending the campaign');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Email & SMS Marketing</h1>
          <p className="text-gray-600 mt-1">Create and send promotional campaigns to your customers</p>
        </div>
        <Link
          href="/admin/email-sms-marketing/history"
          className="flex items-center bg-blue-50 text-blue-600 px-4 py-2 rounded-md hover:bg-blue-100 transition"
        >
          <History size={18} className="mr-2" />
          View Campaign History
        </Link>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md flex items-start">
          <AlertTriangle className="mr-2 h-5 w-5 flex-shrink-0" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-100 text-green-700 rounded-md">
          <p>{success}</p>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex space-x-4 mb-6">
          <button
            type="button"
            className={`flex items-center px-4 py-2 rounded-md ${
              campaignType === 'email'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setCampaignType('email')}
          >
            <Mail className="mr-2 h-5 w-5" />
            Email Campaign
          </button>
          <button
            type="button"
            className={`flex items-center px-4 py-2 rounded-md ${
              campaignType === 'sms'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setCampaignType('sms')}
          >
            <MessageSquare className="mr-2 h-5 w-5" />
            SMS Campaign
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Campaign subject - only for email */}
          {campaignType === 'email' && (
            <div className="mb-4">
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                Email Subject
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter email subject"
                required
              />
            </div>
          )}

          {/* Campaign content */}
          <div className="mb-4">
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              {campaignType === 'email' ? 'Email Content' : 'SMS Message'}
            </label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              rows={campaignType === 'email' ? 6 : 3}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder={campaignType === 'email' ? 'Enter email content' : 'Enter SMS message (160 characters max)'}
              maxLength={campaignType === 'sms' ? 160 : undefined}
              required
            />
            {campaignType === 'sms' && (
              <p className="text-xs text-gray-500 mt-1">
                Characters: {formData.content.length}/160
              </p>
            )}
          </div>

          {/* Recipient selection */}
          <div className="mb-4">
            <label htmlFor="recipientGroup" className="block text-sm font-medium text-gray-700 mb-1">
              Recipients
            </label>
            <div className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-gray-400" />
              <select
                id="recipientGroup"
                name="recipientGroup"
                value={formData.recipientGroup}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Customers</option>
                <option value="new">New Customers (last 30 days)</option>
                <option value="active">Active Customers (purchased in last 90 days)</option>
                <option value="inactive">Inactive Customers (no purchase in 90+ days)</option>
                <option value="vip">VIP Customers (spent over $1000)</option>
                <option value="manual">Manual List (for testing)</option>
              </select>
            </div>
          </div>

          {/* Manual recipient list - only shown when manual option is selected for EMAIL */}
          {formData.recipientGroup === 'manual' && campaignType === 'email' && (
            <div className="mb-4 mt-2">
              <label htmlFor="manualRecipients" className="block text-sm font-medium text-gray-700 mb-1">
                Email Addresses
              </label>
              <textarea
                id="manualRecipients"
                name="manualRecipients"
                value={formData.manualRecipients}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter email addresses separated by commas (e.g., <EMAIL>, <EMAIL>)"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter email addresses separated by commas. This is for testing purposes only.
              </p>
            </div>
          )}

          {/* Manual phone numbers list - only shown when manual option is selected for SMS */}
          {formData.recipientGroup === 'manual' && campaignType === 'sms' && (
            <div className="mb-4 mt-2">
              <label htmlFor="manualPhoneNumbers" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Numbers
              </label>
              <textarea
                id="manualPhoneNumbers"
                name="manualPhoneNumbers"
                value={formData.manualPhoneNumbers}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter phone numbers in E.164 format separated by commas (e.g., +12025551234, +447911123456)"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter phone numbers in E.164 format (with country code) separated by commas. Example: +1XXXYYYZZZZ
              </p>
            </div>
          )}

          {/* Send timing */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <input
                type="checkbox"
                id="sendNow"
                name="sendNow"
                checked={formData.sendNow}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="sendNow" className="ml-2 block text-sm text-gray-700">
                Send immediately
              </label>
            </div>

            {!formData.sendNow && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Schedule Date
                  </label>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-5 w-5 text-gray-400" />
                    <input
                      type="date"
                      id="scheduledDate"
                      name="scheduledDate"
                      value={formData.scheduledDate}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700 mb-1">
                    Schedule Time
                  </label>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-5 w-5 text-gray-400" />
                    <input
                      type="time"
                      id="scheduledTime"
                      name="scheduledTime"
                      value={formData.scheduledTime}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Submit button */}
          <button
            type="submit"
            disabled={loading}
            className="flex items-center justify-center w-full md:w-auto px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition disabled:bg-blue-300"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <Send className="mr-2 h-5 w-5" />
                {formData.sendNow ? 'Send Campaign Now' : 'Schedule Campaign'}
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
} 