# AWS MediaConvert Setup for Video Chunking

## Overview
This guide explains how to set up AWS MediaConvert to chunk hero videos into 75 parts (0.5 seconds each) for fast streaming.

## Prerequisites
- AWS Account with MediaConvert access
- S3 bucket for videos (`videosbucket2025`)
- Proper IAM permissions

## 1. Create IAM Service Role for MediaConvert

### Step 1: Create Service Role
1. Go to AWS Console → IAM → Roles
2. Click "Create role"
3. Select "AWS service" → "MediaConvert"
4. Click "Next: Permissions"

### Step 2: Attach Policies
Attach these policies:
- `AmazonS3FullAccess` (or custom S3 policy below)
- `AmazonAPIGatewayInvokeFullAccess`

### Custom S3 Policy (Recommended):
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::videosbucket2025",
                "arn:aws:s3:::videosbucket2025/*"
            ]
        }
    ]
}
```

### Step 3: Name the Role
- Role name: `MediaConvertServiceRole`
- Copy the ARN: `arn:aws:iam::YOUR-ACCOUNT-ID:role/MediaConvertServiceRole`

## 2. Get MediaConvert Endpoint

### Find Your Regional Endpoint:
1. Go to AWS Console → MediaConvert
2. Copy the endpoint URL (usually: `https://mediaconvert.{region}.amazonaws.com`)
3. For eu-north-1: `https://mediaconvert.eu-north-1.amazonaws.com`

## 3. Environment Variables

Add these to your `.env.local` file:

```env
# MediaConvert Configuration
AWS_MEDIACONVERT_ENDPOINT=https://mediaconvert.eu-north-1.amazonaws.com
AWS_MEDIACONVERT_ROLE_ARN=arn:aws:iam::YOUR-ACCOUNT-ID:role/MediaConvertServiceRole
S3_CHUNKED_VIDEOS_BUCKET=videosbucket2025

# Existing AWS Configuration (make sure these are set)
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_VIDEOS_BUCKET=videosbucket2025
```

## 4. S3 Bucket Structure

Your bucket will have this structure after chunking:
```
videosbucket2025/
├── hero-videos/                    # Original uploaded videos
│   ├── mobile-hero-timestamp-file.mp4
│   └── desktop-hero-timestamp-file.mp4
├── chunked-videos/                 # HLS chunked videos
│   ├── mobile-jobid/
│   │   ├── index.m3u8             # HLS playlist
│   │   ├── segment001.ts          # 0.5s video chunk
│   │   ├── segment002.ts
│   │   └── ... (up to 75 segments)
│   └── desktop-jobid/
│       ├── index.m3u8
│       └── segments...
```

## 5. Testing

1. Upload a hero video in the admin panel
2. Click "Chunk Video (75 parts)" button
3. Monitor progress in the chunking status area
4. Verify HLS files are created in S3

## 6. Pricing Estimate

MediaConvert pricing (approximate):
- $0.0075 per minute of video processed
- For a 30-second video: ~$0.004 per conversion
- Storage: Standard S3 pricing for chunked files

## 7. Troubleshooting

### Common Issues:
1. **403 Forbidden**: Check IAM role permissions
2. **Invalid Role ARN**: Verify the role ARN is correct
3. **No such bucket**: Ensure S3 bucket exists and is accessible
4. **Job fails**: Check video format compatibility

### MediaConvert Job Status:
- `SUBMITTED`: Job is queued
- `PROGRESSING`: Job is processing
- `COMPLETE`: Job finished successfully
- `ERROR`: Job failed (check AWS console for details)

## 8. Performance Benefits

After chunking:
- **Faster loading**: Videos start playing immediately
- **Adaptive streaming**: Quality adjusts based on connection
- **Better UX**: No waiting for full video download
- **Mobile optimization**: Smaller chunks for mobile devices 