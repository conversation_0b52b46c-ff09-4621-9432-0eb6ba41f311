'use client';

import React from 'react';
import localFont from 'next/font/local';
import { useLanguage } from '@/contexts/LanguageContext';

// Load Dosis font from downloaded fonts
const dosisFont = localFont({
  src: '../../../downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf',
  variable: '--font-dosis',
  display: 'swap',
  weight: '400 700'
});

export default function PrivacyPolicyPage() {
  const { translations } = useLanguage();

  return (
    <div className={`min-h-screen ${dosisFont.variable}`} style={{ backgroundColor: '#f8f8f8' }}>
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8 sm:py-12 md:py-16">
        <h1 className="text-3xl sm:text-4xl font-bold mb-6 sm:mb-8 text-center" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_policy_page_title}</h1>
        <div className="p-6 sm:p-8" style={{ backgroundColor: '#f8f8f8' }}>
          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_introduction}</h2>
          <p className="mb-4 text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_introduction_text}
          </p>

          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_information_collect}</h2>
          <p className="mb-4 text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_information_collect_text}
          </p>

          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_information_use}</h2>
          <p className="mb-4 text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_information_use_text}
          </p>

          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_security}</h2>
          <p className="mb-4 text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_security_text}
          </p>

          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_changes}</h2>
          <p className="mb-4 text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_changes_text}
          </p>

          <h2 className="text-2xl font-semibold mb-4" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px'}}>{translations.privacy_contact}</h2>
          <p className="text-gray-700" style={{fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px', lineHeight: '1.8'}}>
            {translations.privacy_contact_text}
          </p>
          {/* Add more detailed sections as needed */}
        </div>
      </div>
    </div>
  );
} 