<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section with Floating Coins Animation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        /* Basic reset */
        body {
            margin: 0;
            overflow: hidden;
        }
        
        /* Container for the Three.js canvas */
        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1; /* Place canvas behind content */
        }
        
        /* Style the canvas element */
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        /* Hero section styling */
        .hero-section {
            position: relative;
            height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Hero section with animation background -->
    <section class="hero-section">
        <!-- Canvas container for the animation -->
        <div id="canvas-container">
            <canvas id="bg-canvas"></canvas>
        </div>
        
        <!-- Your hero content goes here -->
    </section>

    <script>
        // Ensure Three.js is loaded before proceeding
        if (typeof THREE === 'undefined') {
            console.error("THREE.js failed to load.");
        } else {
            // --- Three.js Setup ---

            // Create a new scene
            const scene = new THREE.Scene();

            // Create a perspective camera
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5; // Position the camera back to see the coins

            // Create a WebGL renderer and attach it to the canvas
            const canvas = document.getElementById('bg-canvas');
            const renderer = new THREE.WebGLRenderer({
                canvas: canvas,
                alpha: true, // Enable transparency for the canvas background
                antialias: true // Enable antialiasing for smoother edges
            });
            // Set the renderer size to match the window dimensions
            renderer.setSize(window.innerWidth, window.innerHeight);
            // Set the pixel ratio for high-DPI screens
            renderer.setPixelRatio(window.devicePixelRatio);

            // --- Lighting Setup ---

            // Add ambient light (lights all objects equally)
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            // Add a directional light (like sunlight)
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            // Position the directional light
            directionalLight.position.set(5, 10, 7.5);
            scene.add(directionalLight);

            // --- Coin Creation ---

            // Define the geometry for the coins (a cylinder)
            const coinGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.1, 32);

            // Define the material for the coins (using MeshPhysicalMaterial for translucency)
            const coinMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xffc0cb, // Base color (pinkish)
                metalness: 0.1,   // Low metalness for a less metallic look
                roughness: 0.1,   // Smooth surface
                transparent: true, // Enable transparency
                opacity: 0.5, // Set a lower opacity for translucency
                emissive: 0x8a2be2, // Blue-violet emissive color for the glow
                emissiveIntensity: 1.5 // Increase emissive intensity for a stronger glow
            });

            // Array to hold all the coin meshes
            const coins = [];
            const numCoins = 30; // Number of coins to create
            const spread = 15; // Defines the area where coins are initially scattered

            // Create and position multiple coins
            for (let i = 0; i < numCoins; i++) {
                // Create a mesh (geometry + material) for each coin
                const coin = new THREE.Mesh(coinGeometry, coinMaterial);

                // Set random initial position within the defined spread
                coin.position.set(
                    (Math.random() - 0.5) * spread, // X position
                    (Math.random() - 0.5) * spread, // Y position
                    (Math.random() - 0.5) * spread - 5 // Z position (offset back)
                );

                // Set random initial rotation
                coin.rotation.set(
                    Math.random() * Math.PI * 2, // Rotate around X axis
                    Math.random() * Math.PI * 2, // Rotate around Y axis
                    Math.random() * Math.PI * 2 // Rotate around Z axis
                );

                // Store unique animation properties for each coin in userData
                coin.userData = {
                    rotationSpeedX: (Math.random() - 0.5) * 0.02, // Random X rotation speed
                    rotationSpeedY: (Math.random() - 0.5) * 0.02, // Random Y rotation speed
                    floatSpeed: 0.005 + Math.random() * 0.005, // Varying float speed
                    floatOffset: Math.random() * Math.PI * 2 // Varying start point in float cycle
                };

                // Add the coin to the scene and the coins array
                scene.add(coin);
                coins.push(coin);
            }

            // --- Animation Loop ---

            // Clock to track time for smooth animation
            const clock = new THREE.Clock();

            // The main animation loop function
            function animate() {
                // Request the next frame from the browser
                requestAnimationFrame(animate);

                const elapsedTime = clock.getElapsedTime();

                // Animate each coin
                coins.forEach(coin => {
                    // Apply rotation based on stored speeds
                    coin.rotation.x += coin.userData.rotationSpeedX;
                    coin.rotation.y += coin.userData.rotationSpeedY;

                    // Apply floating movement using a sine wave for smooth oscillation
                    coin.position.y += Math.sin(elapsedTime * coin.userData.floatSpeed + coin.userData.floatOffset) * 0.01;
                });

                // Render the scene with the camera
                renderer.render(scene, camera);
            }

            // --- Event Handlers ---

            // Handle window resizing to maintain correct aspect ratio and size
            function onWindowResize() {
                // Update camera aspect ratio
                camera.aspect = window.innerWidth / window.innerHeight;
                // Update the camera's projection matrix
                camera.updateProjectionMatrix();

                // Update renderer size
                renderer.setSize(window.innerWidth, window.innerHeight);
                // Update pixel ratio on resize
                renderer.setPixelRatio(window.devicePixelRatio);
            }

            // Add event listener for window resize
            window.addEventListener('resize', onWindowResize, false);

            // --- Initialization ---

            // Start animation and set initial size only after the window is fully loaded
            window.onload = function() {
                // Call resize handler once initially to set correct size
                onWindowResize();
                // Start the animation loop
                animate();
            }
        }
    </script>
</body>
</html> 