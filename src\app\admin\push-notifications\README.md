# Push Notification System

This document explains the implementation of the push notification system for Afghan Gems.

## Overview

The push notification system allows the admin to send notifications to users about order updates, promotional offers, and user activity updates. It uses Firebase Cloud Messaging (FCM) to deliver notifications to users across web and mobile platforms, even when they're not actively using the application.

## Key Features

- **Multi-platform support**: Notifications work on web browsers, Android, and iOS (when properly configured)
- **Customizable templates**: Ready-made templates for common notifications
- **User preferences**: Users can set which notifications they want to receive
- **Scheduled notifications**: Notifications can be sent immediately or scheduled for later
- **Notification history**: Track sent notifications and their delivery status

## Components

The system consists of the following components:

1. **Client-side**:
   - Firebase Cloud Messaging integration for receiving notifications
   - Service worker for handling background notifications
   - Notification permission management
   - User preference settings

2. **Admin Panel**:
   - Push notification composer
   - Notification templates
   - Notification settings
   - Notification history

3. **Server-side**:
   - Firebase Admin SDK for sending notifications
   - API routes for handling notification requests
   - Notification scheduler for scheduled notifications
   - Database models for storing notification data

## Implementation Details

### Firebase Configuration

1. Create a Firebase project at [firebase.google.com](https://firebase.google.com)
2. Enable Firebase Cloud Messaging
3. Generate Web Push certificates (VAPID keys)
4. Create a service account for the Firebase Admin SDK
5. Add Firebase configuration variables to `.env.local`

### Database Models

1. **Notification**: Stores notification data, recipients, and delivery status
2. **UserFcmToken**: Stores user FCM tokens and notification preferences

### Client Setup

1. Initialize Firebase in the client
2. Register the service worker
3. Request notification permission
4. Generate and store FCM tokens
5. Set up foreground message handlers

### Sending Notifications

1. Compose notification in the admin panel
2. Select notification type and recipients
3. Choose to send immediately or schedule
4. API processes the request and sends via Firebase Admin SDK
5. Update notification status and track delivery

### Notification Scheduler

1. Run the scheduler script periodically (e.g., via cron job)
2. Script finds scheduled notifications ready to be sent
3. Sends notifications via Firebase Admin SDK
4. Updates notification status in the database

## Usage

### Sending Notifications

1. Go to the admin panel -> Push Notifications -> Send Notification
2. Select notification type and event type
3. Enter notification title and content (or use default templates)
4. Select recipient group
5. Choose to send immediately or schedule for later
6. Click Send/Schedule button

### Monitoring Notifications

1. Go to the admin panel -> Push Notifications -> Send Notification
2. Scroll down to the Notification History section
3. View sent notifications, their status, and recipient counts

### Running the Notification Scheduler

Run the scheduler periodically to process scheduled notifications:

```bash
npm run notifications
```

For production, set up a cron job to run this script every few minutes.

## Troubleshooting

- **Notifications not showing**: Ensure notification permission is granted in the browser
- **FCM tokens not generating**: Check Firebase configuration and VAPID keys
- **Service worker not registering**: Ensure the service worker file is accessible
- **Scheduled notifications not sent**: Make sure the scheduler script is running properly 