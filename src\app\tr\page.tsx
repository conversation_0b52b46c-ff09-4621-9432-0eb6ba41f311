'use client';

import { useState, useRef } from 'react';

export default function TestPage() {
  // State to hold test results
  const [mongoDbResult, setMongoDbResult] = useState<any>(null);
  const [s3ImageResult, setS3ImageResult] = useState<any>(null);
  const [s3VideoResult, setS3VideoResult] = useState<any>(null);
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({
    mongoCreate: false,
    mongoGet: false,
    s3ImageUpload: false,
    s3ImageGet: false,
    s3VideoUpload: false,
    s3VideoGet: false,
  });

  // File input refs
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const videoFileInputRef = useRef<HTMLInputElement>(null);

  // Helper function for API calls
  const apiCall = async (endpoint: string, method: 'GET' | 'POST', body?: any) => {
    try {
      const response = await fetch(`/api/${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });
      return await response.json();
    } catch (error) {
      console.error(`Error in ${method} ${endpoint}:`, error);
      return { success: false, error: 'Failed to fetch' };
    }
  };

  // Helper function for file uploads
  const uploadFile = async (endpoint: string, file: File, loadingKey: string, setResult: (data: any) => void) => {
    try {
      setLoading(prev => ({ ...prev, [loadingKey]: true }));
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch(`/api/${endpoint}/upload`, {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      setResult(result);
    } catch (error) {
      console.error(`Error uploading file to ${endpoint}:`, error);
      setResult({ success: false, error: 'Failed to upload file' });
    } finally {
      setLoading(prev => ({ ...prev, [loadingKey]: false }));
    }
  };

  // MongoDB handlers
  const handleCreateMongoRecord = async () => {
    setLoading({ ...loading, mongoCreate: true });
    const result = await apiCall('test-mongo', 'POST', { name: `Test Record ${new Date().toISOString()}` });
    setMongoDbResult(result);
    setLoading({ ...loading, mongoCreate: false });
  };

  const handleGetMongoRecords = async () => {
    setLoading({ ...loading, mongoGet: true });
    const result = await apiCall('test-mongo', 'GET');
    setMongoDbResult(result);
    setLoading({ ...loading, mongoGet: false });
  };

  // S3 Image handlers
  const handleUploadS3Image = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!imageFileInputRef.current?.files?.[0]) {
      setS3ImageResult({ success: false, error: 'Please select a file to upload' });
      return;
    }
    
    await uploadFile('test-s3-image', imageFileInputRef.current.files[0], 's3ImageUpload', setS3ImageResult);
  };

  const handleGetS3Images = async () => {
    setLoading({ ...loading, s3ImageGet: true });
    const result = await apiCall('test-s3-image', 'GET');
    setS3ImageResult(result);
    setLoading({ ...loading, s3ImageGet: false });
  };

  // S3 Video handlers
  const handleUploadS3Video = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!videoFileInputRef.current?.files?.[0]) {
      setS3VideoResult({ success: false, error: 'Please select a file to upload' });
      return;
    }
    
    await uploadFile('test-s3-video', videoFileInputRef.current.files[0], 's3VideoUpload', setS3VideoResult);
  };

  const handleGetS3Videos = async () => {
    setLoading({ ...loading, s3VideoGet: true });
    const result = await apiCall('test-s3-video', 'GET');
    setS3VideoResult(result);
    setLoading({ ...loading, s3VideoGet: false });
  };

  // Helper to render result
  const renderResult = (result: any) => {
    if (!result) return null;
    
    return (
      <div className="mt-4 p-4 bg-gray-100 rounded overflow-auto max-h-80">
        <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Database Connection Tests</h1>

      {/* MongoDB Test Section */}
      <div className="mb-10 p-6 border rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">MongoDB Test</h2>
        <div className="flex gap-4">
          <button
            onClick={handleCreateMongoRecord}
            disabled={loading.mongoCreate}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading.mongoCreate ? 'Creating...' : 'Create MongoDB Record'}
          </button>
          <button
            onClick={handleGetMongoRecords}
            disabled={loading.mongoGet}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading.mongoGet ? 'Loading...' : 'Retrieve MongoDB Records'}
          </button>
        </div>
        {renderResult(mongoDbResult)}
      </div>

      {/* S3 Image Test Section */}
      <div className="mb-10 p-6 border rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">S3 Image Bucket Test</h2>
        <form onSubmit={handleUploadS3Image} className="mb-4">
          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select image file to upload:</label>
              <input 
                type="file" 
                ref={imageFileInputRef} 
                className="border p-2 w-full rounded"
                accept="image/*"
              />
            </div>
            <div className="flex gap-4">
              <button
                type="submit"
                disabled={loading.s3ImageUpload}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {loading.s3ImageUpload ? 'Uploading...' : 'Upload to S3 Images'}
              </button>
              <button
                type="button"
                onClick={handleGetS3Images}
                disabled={loading.s3ImageGet}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
              >
                {loading.s3ImageGet ? 'Loading...' : 'List S3 Images'}
              </button>
            </div>
          </div>
        </form>
        {renderResult(s3ImageResult)}
      </div>

      {/* S3 Video Test Section */}
      <div className="mb-10 p-6 border rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">S3 Video Bucket Test</h2>
        <form onSubmit={handleUploadS3Video} className="mb-4">
          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select video file to upload:</label>
              <input 
                type="file" 
                ref={videoFileInputRef} 
                className="border p-2 w-full rounded"
                accept="video/*"
              />
            </div>
            <div className="flex gap-4">
              <button
                type="submit"
                disabled={loading.s3VideoUpload}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {loading.s3VideoUpload ? 'Uploading...' : 'Upload to S3 Videos'}
              </button>
              <button
                type="button"
                onClick={handleGetS3Videos}
                disabled={loading.s3VideoGet}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
              >
                {loading.s3VideoGet ? 'Loading...' : 'List S3 Videos'}
              </button>
            </div>
          </div>
        </form>
        {renderResult(s3VideoResult)}
      </div>
    </div>
  );
} 