import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

// Define a simple test schema
const TestSchema = new mongoose.Schema({
  name: { type: String, required: true },
  createdAt: { type: Date, default: Date.now }
});

// Get or create the model (prevents multiple model compilation errors)
const getTestModel = () => {
  // Check if the model is already defined
  return mongoose.models.TestRecord || mongoose.model('TestRecord', TestSchema);
};

/**
 * Create a test record in MongoDB
 * 
 * POST /api/test-mongo
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const { name } = await request.json();
    
    // Validate input
    if (!name || typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'Please provide a valid name'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Create the model and save a new record
    const TestModel = getTestModel();
    const newRecord = new TestModel({ name });
    await newRecord.save();
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Test record created successfully',
      record: newRecord
    });
  } catch (error: any) {
    console.error('Error creating test record:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create test record'
    }, { status: 500 });
  }
}

/**
 * Get all test records from MongoDB
 * 
 * GET /api/test-mongo
 */
export async function GET() {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Get all records
    const TestModel = getTestModel();
    const records = await TestModel.find().sort({ createdAt: -1 }).limit(10); // Get latest 10 records
    
    // Return success response
    return NextResponse.json({
      success: true,
      count: records.length,
      records
    });
  } catch (error: any) {
    console.error('Error retrieving test records:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to retrieve test records'
    }, { status: 500 });
  }
} 