// Lightweight CloudFront utilities for client-side use
// No AWS SDK imports - keeps bundle size small

// CloudFront domains from environment variables
const imagesCloudfrontDomain = process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN || '';

/**
 * Clean CloudFront domain by removing https:// prefix if present
 */
function cleanCloudFrontDomain(domain: string): string {
  return domain.replace(/^https?:\/\//, '');
}

/**
 * Generate a CloudFront URL for an image - Client-side optimized
 * @param key The S3 object key (filename)
 * @returns The full CloudFront URL for the image
 */
export function getCloudFrontImageUrl(key: string): string {
  if (!imagesCloudfrontDomain) {
    console.warn('Images CloudFront domain not configured. Falling back to direct S3 access.');
    return `/api/test-s3-image/${key}`;
  }
  return `https://${cleanCloudFrontDomain(imagesCloudfrontDomain)}/${key}`;
}

/**
 * Alias for getCloudFrontImageUrl for backward compatibility
 * @param key The S3 object key (filename)
 * @returns The full CloudFront URL for the image
 */
export function getImageUrl(key: string): string {
  return getCloudFrontImageUrl(key);
}

/**
 * Direct CloudFront URLs for hero videos - No AWS SDK needed
 * @returns Object with mobile and desktop CloudFront URLs
 */
export function getDirectHeroVideoUrls(): { mobile: string; desktop: string } {
  // Direct CloudFront URLs for ultra-fast video loading
  const directMobileUrl = 'https://d39l10jdryt7ww.cloudfront.net/mobile+hero.mp4';
  const directDesktopUrl = 'https://d39l10jdryt7ww.cloudfront.net/desktop+hero.mp4';
  
  return {
    mobile: directMobileUrl,
    desktop: directDesktopUrl
  };
} 