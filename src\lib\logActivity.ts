import { SystemLogDocument } from '@/models/SystemLog';

/**
 * Log an admin activity to the system logs - DEPRECATED
 * 
 * This function is no longer used as we now log directly in the API endpoints.
 * Keeping this as a reference but it should not be used in new code.
 */
export async function logAdminActivity(logData: Partial<SystemLogDocument>): Promise<void> {
  console.warn('logAdminActivity is deprecated - use direct database logging instead');
}

/**
 * DEPRECATED - Use direct SystemLog.create() calls in API routes instead
 */
export const logActions = {
  create: async () => {
    console.warn('logActions.create is deprecated - use direct database logging instead');
  },
  update: async () => {
    console.warn('logActions.update is deprecated - use direct database logging instead');
  },
  delete: async () => {
    console.warn('logActions.delete is deprecated - use direct database logging instead');
  },
  view: async () => {
    console.warn('logActions.view is deprecated - use direct database logging instead');
  },
  login: async () => {
    console.warn('logActions.login is deprecated - use direct database logging instead');
  },
  logout: async () => {
    console.warn('logActions.logout is deprecated - use direct database logging instead');
  },
  failure: async () => {
    console.warn('logActions.failure is deprecated - use direct database logging instead');
  }
}; 