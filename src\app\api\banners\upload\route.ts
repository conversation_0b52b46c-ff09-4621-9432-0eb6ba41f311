import { NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Image bucket name from environment variables
const imagesBucketName = process.env.S3_IMAGES_BUCKET || 'imagesbucket2025';

/**
 * Handle banner image upload to S3 banners folder
 * 
 * POST /api/banners/upload
 */
export async function POST(request: Request) {
  try {
    // Get form data from request
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    
    // Validate file
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided'
      }, { status: 400 });
    }
    
    // Get file buffer
    const fileBuffer = await file.arrayBuffer();
    
    // Generate a key (filename) for S3, with banners/ prefix
    const fileKey = `banners/${Date.now()}-${file.name}`;
    
    // Create S3 client
    const client = getS3Client();
    
    // Upload file to S3
    const uploadCommand = new PutObjectCommand({
      Bucket: imagesBucketName,
      Key: fileKey,
      Body: Buffer.from(fileBuffer),
      ContentType: file.type || 'application/octet-stream'
    });
    
    await client.send(uploadCommand);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Successfully uploaded banner image to S3 image bucket: ${imagesBucketName}`,
      bucketName: imagesBucketName,
      fileKey: fileKey,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });
  } catch (error: any) {
    console.error('Error uploading banner image to S3:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to upload banner image to S3',
      code: error.Code || error.code,
      bucketName: imagesBucketName
    }, { status: 500 });
  }
} 