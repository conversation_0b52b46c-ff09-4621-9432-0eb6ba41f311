'use client';

import { motion } from 'framer-motion';
import { useRouter, usePathname } from 'next/navigation';
import { ReactNode, useEffect, useCallback, useState } from 'react';

interface PageTransitionProps {
  children: ReactNode;
}

export default function PageTransition({ children }: PageTransitionProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [prevPathname, setPrevPathname] = useState(pathname);

  // Function to handle route transition animations
  const handleRouteChange = useCallback(() => {
    const element = document.getElementById('page-content');
    if (element) {
      element.style.transition = 'opacity 0s ease';
      element.style.opacity = '0';
    }
  }, []);

  // Track pathname changes to detect programmatic navigation
  useEffect(() => {
    if (prevPathname !== pathname) {
      setPrevPathname(pathname);
      
      // Scroll to top instantly when page changes
      window.scrollTo({
        top: 0,
        left: 0
      });
      
      // Handle fade in for new page
      const element = document.getElementById('page-content');
      if (element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0s ease';
        // Force a reflow to ensure the initial opacity is applied
        element.offsetHeight;
        element.style.opacity = '1';
      }
    }
  }, [pathname, prevPathname]);

  useEffect(() => {
    // Handle fade in on mount
    const element = document.getElementById('page-content');
    if (element) {
      element.style.opacity = '0';
      element.style.transition = 'opacity 0s ease';
      // Force a reflow to ensure the initial opacity is applied
      element.offsetHeight;
      element.style.opacity = '1';
    }

    // Listen for clicks on all anchor tags and Next.js Link components
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const anchor = target.closest('a');

      if (anchor && anchor.href && !anchor.href.startsWith('#') && anchor.href !== window.location.href) {
        // Check if this is just a query parameter change on the same route
        const currentUrl = new URL(window.location.href);
        const targetUrl = new URL(anchor.href);

        // If the pathname is the same, only query parameters are changing
        // Skip animation for query parameter changes on the same route
        if (currentUrl.pathname === targetUrl.pathname) {
          // Let the normal navigation happen without animation
          return;
        }

        // Also skip animation for shop category navigation to avoid delay
        if (targetUrl.pathname === '/shop' && targetUrl.searchParams.has('category')) {
          // Let the normal navigation happen without animation for shop categories
          return;
        }

        // Prevent both default anchor behavior and Next.js Link navigation
        e.preventDefault();
        e.stopPropagation();
        handleRouteChange();

        // Wait for fade out animation to complete before navigation
        setTimeout(() => {
          router.push(anchor.href);
        }, 0);
      }
    };

    // Create a patched version of the router.push method
    const originalPush = router.push;
    const patchedPush = (href: string) => {
      // Only apply transition if it's not an anchor click (which is handled separately)
      if (!document.activeElement || document.activeElement.tagName !== 'A') {
        handleRouteChange();
        setTimeout(() => {
          originalPush(href);
        }, 0);
      } else {
        originalPush(href);
      }
    };

    // Patch the router.push method
    // @ts-ignore - We're monkey patching here
    router.push = patchedPush;

    document.addEventListener('click', handleClick, true);
    return () => {
      document.removeEventListener('click', handleClick, true);
      // Restore original push method
      // @ts-ignore - Restoring the monkey patch
      router.push = originalPush;
    };
  }, [handleRouteChange, router, pathname]);

  return (
    <div id="page-content" style={{ opacity: 0 }}>
      {children}
    </div>
  );
} 