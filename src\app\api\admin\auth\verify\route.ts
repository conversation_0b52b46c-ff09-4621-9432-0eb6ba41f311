import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const { userId } = await req.json();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Check if user exists and is active
    const user = await User.findOne({ 
      _id: userId,
      isActive: true 
    })
      .populate('role')
      .lean();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found or inactive' },
        { status: 404 }
      );
    }
    
    // User exists and is active, return success response
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error verifying admin user:', error);
    return NextResponse.json(
      { error: 'Authentication verification failed' },
      { status: 500 }
    );
  }
} 