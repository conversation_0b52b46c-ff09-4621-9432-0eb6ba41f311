'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Edit, Trash2, Save, X, Check, AlertCircle, UserPlus } from 'lucide-react';
import Link from 'next/link';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import PermissionGuard from '@/components/admin/PermissionGuard';

interface Permission {
  resource: string;
  actions: string[];
}

interface Role {
  _id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

export default function UserRolesPage() {
  const router = useRouter();
  
  return (
    <PermissionGuard 
      resource="users" 
      action="view" 
      redirectTo="/admin"
      fallback={<div className="p-6">You do not have permission to access this page.</div>}
    >
      <UserRolesContent />
    </PermissionGuard>
  );
}

function UserRolesContent() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingRole, setEditingRole] = useState<Partial<Role> | null>(null);
  const [showModal, setShowModal] = useState(false);
  
  // Available resources and actions
  const resources = [
    'dashboard', 'products', 'categories', 'orders', 'customers', 
    'inventory', 'reviews', 'analytics', 'returns', 'users'
  ];
  
  const actions = ['view', 'create', 'update', 'delete'];
  
  const router = useRouter();
  
  // Fetch roles on component mount
  useEffect(() => {
    fetchRoles();
  }, []);
  
  // Fetch all roles
  const fetchRoles = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/roles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      
      const data = await response.json();
      setRoles(data);
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError('Failed to load roles. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Open modal with empty role for creation
  const handleCreateRole = () => {
    setEditingRole({
      name: '',
      description: '',
      permissions: []
    });
    setShowModal(true);
  };
  
  // Open modal with existing role for editing
  const handleEditRole = (role: Role) => {
    setEditingRole({ ...role });
    setShowModal(true);
  };
  
  // Handle permission checkbox change
  const handlePermissionChange = (resource: string, action: string, checked: boolean) => {
    if (!editingRole) return;
    
    setEditingRole(prev => {
      if (!prev) return prev;
      
      const updatedPermissions = [...(prev.permissions || [])];
      const permissionIndex = updatedPermissions.findIndex(p => p.resource === resource);
      
      if (permissionIndex === -1 && checked) {
        // Add new permission
        updatedPermissions.push({
          resource,
          actions: [action]
        });
      } else if (permissionIndex !== -1) {
        // Update existing permission
        const actions = [...updatedPermissions[permissionIndex].actions];
        
        if (checked && !actions.includes(action)) {
          actions.push(action);
        } else if (!checked && actions.includes(action)) {
          const actionIndex = actions.indexOf(action);
          actions.splice(actionIndex, 1);
        }
        
        if (actions.length === 0) {
          // Remove permission if no actions left
          updatedPermissions.splice(permissionIndex, 1);
        } else {
          updatedPermissions[permissionIndex].actions = actions;
        }
      }
      
      return {
        ...prev,
        permissions: updatedPermissions
      };
    });
  };
  
  // Check if a permission is selected
  const isPermissionSelected = (resource: string, action: string): boolean => {
    if (!editingRole || !editingRole.permissions) return false;
    
    const permission = editingRole.permissions.find(p => p.resource === resource);
    return permission ? permission.actions.includes(action) : false;
  };
  
  // Save role (create or update)
  const handleSaveRole = async () => {
    if (!editingRole || !editingRole.name) return;
    
    try {
      const isNewRole = !editingRole._id;
      const url = isNewRole 
        ? '/api/admin/roles' 
        : `/api/admin/roles/${editingRole._id}`;
      
      const response = await fetch(url, {
        method: isNewRole ? 'POST' : 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingRole)
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save role');
      }
      
      // Refresh roles list
      await fetchRoles();
      
      // Close modal
      setShowModal(false);
      setEditingRole(null);
    } catch (error: any) {
      console.error('Error saving role:', error);
      setError(error.message || 'Failed to save role');
    }
  };
  
  // Delete role
  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/roles/${roleId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete role');
      }
      
      // Refresh roles list
      await fetchRoles();
    } catch (error) {
      console.error('Error deleting role:', error);
      setError('Failed to delete role. Please try again.');
    }
  };
  
  if (isLoading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">User Roles & Permissions</h1>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Roles & Permissions</h1>
        <div className="flex space-x-2">
          <Link
            href="/admin/user-roles/users"
            className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md flex items-center"
          >
            <UserPlus size={16} className="mr-2" />
            Manage Users
          </Link>
          <button
            onClick={handleCreateRole}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Plus size={16} className="mr-2" />
            Create New Role
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-start">
          <AlertCircle size={20} className="mr-2 flex-shrink-0 mt-0.5" />
          <span>{error}</span>
        </div>
      )}
      
      {roles.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-6 text-center">
          <p className="text-gray-500">No roles have been created yet. Create your first role to get started.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {roles.map(role => (
            <div key={role._id} className="bg-white border rounded-md shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
                <h2 className="font-semibold text-lg">{role.name}</h2>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => handleEditRole(role)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    onClick={() => handleDeleteRole(role._id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
              <div className="p-4">
                {role.description && (
                  <p className="text-gray-600 mb-3">{role.description}</p>
                )}
                <h3 className="font-medium mb-2">Permissions:</h3>
                <div className="space-y-2">
                  {role.permissions && role.permissions.length > 0 ? (
                    role.permissions.map(permission => (
                      <div key={permission.resource} className="bg-gray-50 rounded-md p-2">
                        <span className="font-medium capitalize">{permission.resource}</span>
                        <span className="text-sm text-gray-500 ml-2">
                          {permission.actions.join(', ')}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No permissions assigned</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Role Edit Modal */}
      {showModal && editingRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl overflow-hidden">
            <div className="flex justify-between items-center px-6 py-4 border-b">
              <h2 className="text-xl font-semibold">
                {editingRole._id ? 'Edit Role' : 'Create New Role'}
              </h2>
              <button 
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>
            
            <div className="px-6 py-4 max-h-[calc(100vh-200px)] overflow-y-auto">
              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={editingRole.name || ''}
                    onChange={(e) => setEditingRole({...editingRole, name: e.target.value})}
                    className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g. Admin, Editor, Viewer"
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={editingRole.description || ''}
                    onChange={(e) => setEditingRole({...editingRole, description: e.target.value})}
                    className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    placeholder="Describe what this role is for"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Permissions
                  </label>
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                            Resource
                          </th>
                          {actions.map(action => (
                            <th key={action} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {action}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {resources.map(resource => (
                          <tr key={resource} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className="font-medium capitalize">{resource}</span>
                            </td>
                            {actions.map(action => (
                              <td key={`${resource}-${action}`} className="px-4 py-3 whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  id={`${resource}-${action}`}
                                  checked={isPermissionSelected(resource, action)}
                                  onChange={(e) => handlePermissionChange(resource, action, e.target.checked)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t flex justify-end space-x-3">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveRole}
                disabled={!editingRole.name}
                className={`px-4 py-2 rounded-md text-white flex items-center ${
                  editingRole.name ? 'bg-blue-500 hover:bg-blue-600' : 'bg-blue-300 cursor-not-allowed'
                }`}
              >
                <Save size={16} className="mr-2" />
                Save Role
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}