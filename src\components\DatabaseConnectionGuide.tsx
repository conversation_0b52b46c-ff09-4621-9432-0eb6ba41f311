'use client';

import { useState } from 'react';

/**
 * A component that provides a user guide for connecting to different databases in MongoDB Atlas
 */
export default function DatabaseConnectionGuide() {
  const [selectedDatabase, setSelectedDatabase] = useState<string>('default');
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 mt-6">
      <h2 className="text-xl font-bold mb-4">Database Connection Guide</h2>
      
      <div className="mb-4">
        <label htmlFor="database-select" className="block text-sm font-medium text-gray-700 mb-1">
          I want to connect to:
        </label>
        <select 
          id="database-select"
          value={selectedDatabase}
          onChange={(e) => setSelectedDatabase(e.target.value)}
          className="block w-full rounded-md border border-gray-300 py-2 px-3 text-gray-700 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
        >
          <option value="default">Select a database type...</option>
          <option value="mongodb-atlas">MongoDB Atlas (Cloud)</option>
          <option value="mongodb-local">MongoDB (Local Installation)</option>
          <option value="custom">Custom Database</option>
        </select>
      </div>
      
      {selectedDatabase === 'mongodb-atlas' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-indigo-700">Connecting to MongoDB Atlas</h3>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 1: Create or access your MongoDB Atlas account</h4>
            <p className="text-gray-600 mb-2">Visit <a href="https://www.mongodb.com/cloud/atlas" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800">MongoDB Atlas</a> and sign in or create a new account.</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 2: Create a new cluster or use an existing one</h4>
            <p className="text-gray-600 mb-2">From your MongoDB Atlas dashboard, create a new cluster or select an existing one.</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 3: Set up database access</h4>
            <ol className="list-decimal pl-5 text-gray-600 space-y-1">
              <li>Go to the "Database Access" section in the security menu</li>
              <li>Click "Add New Database User"</li>
              <li>Create a username and password</li>
              <li>Set appropriate privileges (at least "Read and Write")</li>
              <li>Save the user</li>
            </ol>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 4: Set up network access</h4>
            <ol className="list-decimal pl-5 text-gray-600 space-y-1">
              <li>Go to the "Network Access" section in the security menu</li>
              <li>Click "Add IP Address"</li>
              <li>Add your current IP address or use "0.0.0.0/0" for all IPs (not recommended for production)</li>
              <li>Save the changes</li>
            </ol>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 5: Get your connection string</h4>
            <ol className="list-decimal pl-5 text-gray-600 space-y-1">
              <li>Go to your cluster and click "Connect"</li>
              <li>Select "Connect your application"</li>
              <li>Copy the provided connection string</li>
              <li>Replace "&lt;password&gt;" with your database user's password</li>
              <li>Replace "&lt;dbname&gt;" with your desired database name (e.g., "gemstone_store")</li>
            </ol>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 6: Update your .env.local file</h4>
            <p className="text-gray-600 mb-2">Update your .env.local file with the new connection string:</p>
            <div className="bg-gray-800 text-white p-3 rounded-md font-mono text-sm">
              <p>MONGODB_URI=mongodb+srv://username:<EMAIL>/gemstone_store?retryWrites=true&w=majority</p>
            </div>
          </div>
          
          <div className="bg-indigo-50 p-4 rounded-md border border-indigo-200">
            <h4 className="font-medium text-indigo-800 mb-2">Example Connection String Format</h4>
            <p className="text-gray-700">Your connection string should look similar to this:</p>
            <div className="bg-white p-3 rounded-md font-mono text-sm text-gray-700 mt-2 border border-gray-300">
              mongodb+srv://yourusername:<EMAIL>/gemstone_store?retryWrites=true&w=majority
            </div>
            <p className="text-sm text-gray-600 mt-2">
              <span className="font-medium">Important:</span> Replace "yourusername", "yourpassword", and the cluster 
              information with your actual MongoDB Atlas credentials.
            </p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-md border border-green-200">
            <h4 className="font-medium text-green-800 mb-2">Restart Your Application</h4>
            <p className="text-gray-600">After updating your .env.local file, restart your application for the changes to take effect.</p>
          </div>
        </div>
      )}
      
      {selectedDatabase === 'mongodb-local' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-indigo-700">Connecting to Local MongoDB</h3>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 1: Install MongoDB locally</h4>
            <p className="text-gray-600 mb-2">Download and install MongoDB Community Edition from the <a href="https://www.mongodb.com/try/download/community" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800">official MongoDB website</a>.</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 2: Start MongoDB service</h4>
            <p className="text-gray-600 mb-2">Ensure the MongoDB service is running on your machine.</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 3: Update your .env.local file</h4>
            <p className="text-gray-600 mb-2">Set your connection string to use the local MongoDB instance:</p>
            <div className="bg-gray-800 text-white p-3 rounded-md font-mono text-sm">
              <p>MONGODB_URI=mongodb://localhost:27017/gemstone_store</p>
            </div>
            <p className="text-sm text-gray-600 mt-2">This connects to a database named "gemstone_store" on your local MongoDB server.</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-md border border-green-200">
            <h4 className="font-medium text-green-800 mb-2">Restart Your Application</h4>
            <p className="text-gray-600">After updating your .env.local file, restart your application for the changes to take effect.</p>
          </div>
        </div>
      )}
      
      {selectedDatabase === 'custom' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-indigo-700">Connecting to Custom Database</h3>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 1: Get your connection information</h4>
            <p className="text-gray-600">Obtain the following information from your database administrator:</p>
            <ul className="list-disc pl-5 text-gray-600 space-y-1 mt-2">
              <li>Database host (e.g., db.example.com or IP address)</li>
              <li>Port number (default for MongoDB is 27017)</li>
              <li>Database name</li>
              <li>Username and password with appropriate permissions</li>
              <li>Any additional connection options or authentication requirements</li>
            </ul>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 2: Format your connection string</h4>
            <p className="text-gray-600 mb-2">Format the connection string based on your database type:</p>
            <div className="bg-gray-800 text-white p-3 rounded-md font-mono text-sm mb-2">
              <p>Standard format: ********************************:port/database</p>
              <p className="mt-1">SRV format: *********************************************</p>
            </div>
            <p className="text-sm text-gray-600">The SRV format is typically used for MongoDB Atlas and other cloud providers.</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Step 3: Update your .env.local file</h4>
            <p className="text-gray-600 mb-2">Add your connection string to the .env.local file:</p>
            <div className="bg-gray-800 text-white p-3 rounded-md font-mono text-sm">
              <p>MONGODB_URI=your_connection_string_here</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
            <h4 className="font-medium text-yellow-800 mb-2">Security Considerations</h4>
            <ul className="list-disc pl-5 text-gray-600 space-y-1">
              <li>Never commit .env files with credentials to version control</li>
              <li>Use environment variables for all sensitive information</li>
              <li>Consider using a secrets manager for production environments</li>
              <li>Use strong, unique passwords for database access</li>
              <li>Restrict database user permissions to only what's needed</li>
            </ul>
          </div>
          
          <div className="bg-green-50 p-4 rounded-md border border-green-200">
            <h4 className="font-medium text-green-800 mb-2">Testing Your Connection</h4>
            <p className="text-gray-600">After setting up your connection string:</p>
            <ol className="list-decimal pl-5 text-gray-600 space-y-1 mt-2">
              <li>Restart your application</li>
              <li>Check the connection status on the home page</li>
              <li>If there are errors, review the detailed error messages</li>
            </ol>
          </div>
        </div>
      )}
      
      {selectedDatabase === 'default' && (
        <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
          <p className="text-blue-700">
            Please select a database type from the dropdown to view connection instructions.
          </p>
        </div>
      )}
    </div>
  );
} 