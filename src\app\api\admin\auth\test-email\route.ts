import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';

export async function GET(req: Request) {
  try {
    const testEmail = process.env.GMAIL_USER;
    
    if (!testEmail) {
      return NextResponse.json({ 
        error: 'No test email address configured. Please set GMAIL_USER in .env file.' 
      }, { status: 500 });
    }
    
    const emailContent = {
      to: testEmail,
      subject: 'Test Email from Afghan Int\'l Gems',
      html: `
        <h1>Email System Test</h1>
        <p>This is a test email to verify that the email system is working correctly.</p>
        <p>If you're receiving this, it means the Gmail SMTP configuration is working!</p>
        <p>Time sent: ${new Date().toLocaleString()}</p>
      `
    };
    
    const success = await sendEmail(emailContent);
    
    if (success) {
      return NextResponse.json({ 
        success: true,
        message: `Test email sent successfully to ${testEmail}` 
      });
    } else {
      return NextResponse.json({ 
        error: 'Failed to send test email' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json({ 
      error: 'An error occurred while sending test email' 
    }, { status: 500 });
  }
} 