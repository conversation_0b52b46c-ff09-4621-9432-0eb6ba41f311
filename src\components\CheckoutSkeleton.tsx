'use client';

const CheckoutSkeleton = () => {
  return (
    <div className="container mx-auto px-4 py-8 bg-[#f8f8f8]">
      <div className="max-w-5xl mx-auto">
        {/* Progress Bar Skeleton */}
        <div className="w-full bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6">
          <div className="flex items-center justify-between max-w-full mx-auto">
            {/* Step 1 */}
            <div className="flex items-center flex-1">
              <div className="flex items-center flex-shrink-0">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="ml-2 sm:ml-3 min-w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                </div>
              </div>
              {/* Progress Line */}
              <div className="mx-2 sm:mx-4 md:mx-8 flex-1 max-w-[3rem] sm:max-w-[6rem] md:max-w-[8rem]">
                <div className="h-0.5 bg-gray-200 animate-pulse"></div>
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="flex items-center flex-1">
              <div className="flex items-center flex-shrink-0">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="ml-2 sm:ml-3 min-w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back button and title skeleton */}
        <div className="flex items-center mb-6">
          <div className="w-10 h-10 bg-gray-200 rounded-full mr-2 animate-pulse"></div>
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>

        {/* Main content layout */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left column - Customer Info Form Skeleton */}
          <div className="lg:w-2/3">
            <div className="bg-white rounded-lg shadow-md p-6 mb-4">
              {/* Form title */}
              <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
              
              {/* Form fields grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {/* Email field */}
                <div>
                  <div className="h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                </div>
                
                {/* Phone field */}
                <div>
                  <div className="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                </div>
                
                {/* Full name field - spans 2 columns */}
                <div className="md:col-span-2">
                  <div className="h-4 bg-gray-200 rounded w-16 mb-1 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
              
              {/* Shipping address section */}
              <div className="h-5 bg-gray-200 rounded w-32 mb-3 animate-pulse"></div>
              
              {/* Address textarea */}
              <div>
                <div className="h-4 bg-gray-200 rounded w-28 mb-1 animate-pulse"></div>
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
              
              {/* Continue button */}
              <div className="mt-6">
                <div className="w-full h-12 bg-gray-200 rounded-md animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Right column - Order Summary Skeleton */}
          <div className="lg:w-1/3">
            <div className="bg-white rounded-lg shadow-md p-6 mb-4">
              {/* Order summary title */}
              <div className="h-6 bg-gray-200 rounded w-32 mb-4 animate-pulse"></div>
              
              {/* Cart items */}
              <div className="space-y-4 mb-6">
                {/* Cart item 1 */}
                <div className="flex items-center space-x-3">
                  <div className="w-16 h-16 bg-gray-200 rounded animate-pulse"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-1 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                </div>
                
                {/* Cart item 2 */}
                <div className="flex items-center space-x-3">
                  <div className="w-16 h-16 bg-gray-200 rounded animate-pulse"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-2/3 mb-1 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                </div>
              </div>
              
              {/* Summary totals */}
              <div className="space-y-3 border-t border-gray-200 pt-4">
                {/* Subtotal */}
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                </div>
                
                {/* Shipping */}
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-14 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                </div>
                
                {/* Total */}
                <div className="flex justify-between pt-3 border-t border-gray-200">
                  <div className="h-5 bg-gray-200 rounded w-12 animate-pulse"></div>
                  <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products Section Skeleton */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            {/* Related products title */}
            <div className="flex flex-col items-center mb-6">
              <div className="h-6 bg-gray-200 rounded w-40 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-56 mb-2 animate-pulse"></div>
              <div className="w-16 h-px bg-gray-200 animate-pulse"></div>
            </div>
            
            {/* Related products grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Array.from({ length: 4 }, (_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="aspect-square bg-gray-200 rounded-lg mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSkeleton; 