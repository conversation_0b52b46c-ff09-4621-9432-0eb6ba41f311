import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';
import Order from '@/models/Order';
import { Product } from '@/models/Product';

interface Params {
  params: {
    id: string;
  };
}

export async function GET(req: Request, { params }: Params) {
  try {
    const { id } = params;
    
    await dbConnect();
    
    // Get customer details
    const customer = await Customer.findById(id).lean();
    
    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    // Get customer's orders
    const orders = await Order.find({ 
      'customer.email': customer.email 
    }).sort({ createdAt: -1 }).lean();
    
    // Get customer's wishlist products
    const wishlistItems = await Product.find({
      _id: { $in: customer.wishlist || [] }
    }).lean();
    
    // Combine data
    const customerWithRelations = {
      ...customer,
      orders: orders || [],
      wishlist: wishlistItems || []
    };
    
    return NextResponse.json(customerWithRelations);
  } catch (error) {
    console.error('Error fetching customer details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer details' },
      { status: 500 }
    );
  }
} 