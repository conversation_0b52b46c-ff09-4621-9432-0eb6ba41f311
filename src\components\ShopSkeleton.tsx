'use client';

import ProductCardSkeleton from './ProductCardSkeleton';

const ShopSkeleton = () => {
  return (
    <div className="min-h-screen bg-gradient-to-r from-[#f8f8f8] via-[#f8f8f8] to-[#f8f8f8] ">
      <div className="container mx-auto px-4">
        {/* Mobile filter toggle skeleton (hidden but matching structure) */}
        <div className="hidden">
          <div className="w-full h-12 bg-gray-200 rounded-md animate-pulse mb-4"></div>
        </div>
        
        <div className="flex flex-col md:flex-row gap-6 sm:gap-8">
          {/* NewSidebar Skeleton - Hidden on mobile, visible on desktop */}
          <div className="hidden md:block md:w-1/4 lg:w-1/5">
            <div className="bg-white rounded-lg p-6">
              {/* Search skeleton */}
              <div className="h-10 bg-gray-200 rounded-md mb-6 animate-pulse"></div>
              
              {/* Categories skeleton */}
              <div className="space-y-4">
                {Array.from({ length: 3 }, (_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded mb-2"></div>
                    <div className="pl-4 space-y-2">
                      {Array.from({ length: 2 }, (_, j) => (
                        <div key={j} className="h-4 bg-gray-200 rounded w-3/4"></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Price range skeleton */}
              <div className="mt-6 space-y-3">
                <div className="h-5 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
              
              {/* Reset filters button skeleton */}
              <div className="mt-6">
                <div className="h-10 bg-gray-200 rounded-md animate-pulse"></div>
              </div>
            </div>
          </div>
          
          {/* Main Content Skeleton */}
          <div className="md:w-3/4 lg:w-4/5">
            {/* Sort and Results Info Bar Skeleton */}
            <div className="flex justify-between items-center mb-6 border-b border-t h-[51px] px-[13px]">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-gray-200 rounded animate-pulse mr-2"></div>
                <div className="h-5 bg-gray-200 rounded w-24 animate-pulse"></div>
              </div>
              <div className="flex items-center">
                <div className="h-5 bg-gray-200 rounded w-8 animate-pulse mr-1"></div>
                <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
              </div>
            </div>
            
            {/* Active Filters Skeleton */}
            <div className="mb-6 flex flex-wrap gap-2 items-center animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded-full w-24"></div>
              <div className="h-8 bg-gray-200 rounded-full w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-16 ml-auto"></div>
            </div>
            
            {/* Products Heading Skeleton */}
            <div className="flex flex-col items-center mb-6 sm:mb-8 md:mb-10">
              <div className="h-7 bg-gray-200 rounded w-48 mb-2 sm:mb-3 animate-pulse"></div>
            </div>
            
            {/* Product Grid Skeleton - Updated to match new grid system */}
            <div className="grid grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 gap-4 sm:gap-5 md:gap-6">
              {Array.from({ length: 12 }, (_, index) => (
                <div key={index} className="col-span-2">
                  <ProductCardSkeleton />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShopSkeleton; 