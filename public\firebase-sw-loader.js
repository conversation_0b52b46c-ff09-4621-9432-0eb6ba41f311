// This script dynamically loads the Firebase config into the service worker
(function() {
  // Wait until the DOM is fully loaded
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeServiceWorker, 100); // Small delay to be safe
  } else {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(initializeServiceWorker, 100); // Small delay to be safe
    });
  }
  
  function initializeServiceWorker() {
    // Wait until the DOM is ready to ensure the window variables are set
    if (!('serviceWorker' in navigator)) {
      console.warn('Service workers are not supported in this browser');
      return;
    }
    
    try {
      // Check if the window ENV variables are set
      if (!window.ENV_FIREBASE_API_KEY && !window.ENV_FIREBASE_PROJECT_ID) {
        console.warn('Firebase configuration is missing or not yet loaded. Retrying in 500ms...');
        setTimeout(initializeServiceWorker, 500);
        return;
      }
      
      // Get Firebase config values from the window
      const firebaseConfig = {
        apiKey: window.ENV_FIREBASE_API_KEY,
        authDomain: window.ENV_FIREBASE_AUTH_DOMAIN,
        projectId: window.ENV_FIREBASE_PROJECT_ID,
        storageBucket: window.ENV_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: window.ENV_FIREBASE_MESSAGING_SENDER_ID,
        appId: window.ENV_FIREBASE_APP_ID,
        vapidKey: window.ENV_FIREBASE_VAPID_KEY
      };
      
      // Validate that we have the required config values
      if (!firebaseConfig.apiKey || !firebaseConfig.projectId || !firebaseConfig.messagingSenderId) {
        console.warn('Firebase configuration is incomplete. Push notifications may not work properly.');
      }

      // Check for existing service worker registrations
      navigator.serviceWorker.getRegistrations().then(registrations => {
        const fcmServiceWorker = registrations.find(reg => 
          reg.scope.includes(window.location.origin) && 
          reg.active && 
          reg.active.scriptURL.includes('firebase-messaging-sw.js')
        );
        
        if (fcmServiceWorker) {
          console.log('Firebase Messaging service worker already registered. Updating configuration...');
          sendConfigToServiceWorker(fcmServiceWorker, firebaseConfig);
        } else {
          // Register the service worker if not already registered
          registerServiceWorker(firebaseConfig);
        }
      }).catch(error => {
        console.error('Error checking service worker registrations:', error);
        // Fallback to registration
        registerServiceWorker(firebaseConfig);
      });
    } catch (error) {
      console.error('Error setting up service worker:', error);
    }
  }
  
  function registerServiceWorker(firebaseConfig) {
    navigator.serviceWorker.register('/firebase-messaging-sw.js')
      .then(registration => {
        console.log('Service Worker registered with scope:', registration.scope);
        sendConfigToServiceWorker(registration, firebaseConfig);
      })
      .catch(error => {
        console.error('Service Worker registration failed:', error);
      });
  }
  
  function sendConfigToServiceWorker(registration, firebaseConfig) {
    // Pass Firebase config to the service worker
    if (registration.active) {
      console.log('Sending config to active service worker');
      try {
        registration.active.postMessage({
          type: 'FIREBASE_CONFIG',
          config: firebaseConfig
        });
        console.log('Configuration sent to service worker successfully');
      } catch (e) {
        console.error('Error sending configuration to service worker:', e);
      }
    } else if (registration.installing) {
      console.log('Service worker is installing, waiting for it to be ready');
      registration.installing.addEventListener('statechange', function(event) {
        if (this.state === 'activated') {
          console.log('Service worker now active, sending config');
          try {
            registration.active.postMessage({
              type: 'FIREBASE_CONFIG',
              config: firebaseConfig
            });
            console.log('Configuration sent to service worker successfully');
          } catch (e) {
            console.error('Error sending configuration to service worker:', e);
          }
        }
      });
    } else {
      console.warn('Service worker registered but neither active nor installing');
    }
  }
})(); 