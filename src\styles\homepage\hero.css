.hero-container {
    width: 100%;
    height: 89vh;
    position: relative;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.8);
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-video,
.hero-image {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.16);
}



.hero-content {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    z-index: 20;
    inset: 0px;
}

.hero-text-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    margin-bottom: 1.25rem; /* 20px converted to rem */
    margin-left: 1.625rem; /* 26px converted to rem */
}

.hero-title {
    color: white;
    font-family: var(--font-dosis), sans-serif;
    font-size: 1.875rem; /* text-3xl */
    font-weight: 400; /* font-medium */
    text-transform: capitalize;
    line-height: 1.25; /* leading-tight */
    letter-spacing: 1px;
    margin-bottom: 0.5rem; /* mb-2 */
    filter: drop-shadow(2px 0.5px 3px rgba(0,0,0,1));
}

.hero-subtitle {
    color: white;
    font-family: var(--font-dosis), sans-serif;
    font-size: 1rem; /* text-base */
    font-weight: 400; /* font-medium */
    text-transform: capitalize;
    line-height: 1.25; /* leading-tight */
    letter-spacing: 1px;
    filter: drop-shadow(0px 2px 2px rgba(0,0,0,1));
}

.hero-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 2rem;
    margin-left: 1.375rem; /* 22px converted to rem (16px = 1rem) */
    margin-bottom: 1.5rem; /* 24px converted to rem */
}

.hero-button-left {
    z-index: 20;
}

.hero-button-right {
    z-index: 20;
}

/* Living Gem Button Animations */
@keyframes orbital-stars {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 var(--glow-size) var(--glow-color-1); }
    50% { box-shadow: 0 0 calc(var(--glow-size) * 1.5) var(--glow-color-2); }
}

@keyframes aurora-flow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes stars-twinkle {
    0% { opacity: 0.2; }
    50% { opacity: 1; }
    100% { opacity: 0.2; }
}

@keyframes core-flare {
    0% { transform: scale(0); opacity: 0.8; }
    80% { transform: scale(1.5); opacity: 0.2; }
    100% { transform: scale(2); opacity: 0; }
}

@keyframes button-vibrate {
    0% { transform: translate(0); }
    10% { transform: translate(-1px, -1px); }
    20% { transform: translate(1px, -1px); }
    30% { transform: translate(-1px, 1px); }
    40% { transform: translate(1px, 1px); }
    50% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, -1px); }
    70% { transform: translate(-1px, 1px); }
    80% { transform: translate(1px, 1px); }
    90% { transform: translate(-1px, -1px); }
    100% { transform: translate(0); }
}

.hero-button-wrapper {
    perspective: 250px;
    position: relative;
    overflow: visible;
}

.hero-button {
    position: relative;
    width: 10rem; /* w-40 */
    height: 2.75rem; /* h-11 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 0.875rem; /* text-sm */
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    cursor: pointer;

    border: 1px solid #ffd700;
    border-radius: 50px;
    background: var(--bg-gradient);

    --glow-size: 25px;
    --glow-color-1: rgba(255, 223, 0, 0.8);
    --glow-color-2: rgba(255, 215, 0, 1);

    animation: pulse-glow 4s infinite ease-in-out;
    transition: box-shadow 0.4s ease, transform 0.3s ease-out;
    transform-style: preserve-3d;
    overflow: hidden;
    letter-spacing: 0.12em; /* increased tracking for premium feel */
}

/* Mouse tracking highlight */
.hero-button::before {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                                rgba(255, 255, 255, 0.4),
                                transparent 40%);
    border-radius: 50px;
    opacity: 1;
    transition: opacity 0.4s;
    z-index: 2;
}

/* Core flare effect during unstable events */
.hero-button.is-unstable::before {
    animation: core-flare 0.4s ease-out forwards;
}

/* Orbital Stars Effect - Using wrapper for proper outside positioning */
.hero-button-wrapper::before {
    content: '';
    position: absolute;
    top: 50%; left: 50%;
    width: 200px; height: 200px;
    margin-top: -100px; margin-left: -100px;
    border-radius: 50%;
    background:
        radial-gradient(circle at 50% 0%, #ffd700 3px, transparent 4px),
        radial-gradient(circle at 86.6% 25%, #ffdf00 2px, transparent 3px),
        radial-gradient(circle at 86.6% 75%, #ffd700 3px, transparent 4px),
        radial-gradient(circle at 50% 100%, #ffdf00 2px, transparent 3px),
        radial-gradient(circle at 13.4% 75%, #ffd700 3px, transparent 4px),
        radial-gradient(circle at 13.4% 25%, #ffdf00 2px, transparent 3px);
    animation: orbital-stars 8s infinite linear;
    z-index: 10;
    pointer-events: none;
}

/* Crack overlay for unstable events */
.crack-overlay {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="280" height="65" viewBox="0 0 280 65" xmlns="http://www.w3.org/2000/svg"><path d="M130 0 L140 25 L120 30 L150 65 M160 0 L155 30 L170 35 L160 65 M145 28 L165 37" stroke-width="1.5" stroke="white" fill="none" /></svg>');
    background-size: 100% 100%;
    z-index: 3;
    opacity: 0;
    transition: opacity 0.4s ease-out;
}

.hero-button.is-unstable .crack-overlay {
    opacity: 0.7;
    transition-duration: 0.1s;
}

/* Removed vibrate effect during unstable events - keeping other effects intact */
.hero-button.is-unstable {
    /* Vibration animation removed per user request */
}

.hero-button span {
    position: relative;
    z-index: 4;
}

/* Stars effect removed */

.hero-button:active {
    transform: scale(0.96) !important;
    transition-duration: 0.1s;
}

.hero-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Button style variants */
.dark-style {
    --bg-gradient: #000000;
    --aurora-gradient: linear-gradient(45deg, #888, #555, #fff, #555, #888);
}

.sapphire-style {
    --bg-gradient: #0066ff;
    --aurora-gradient: linear-gradient(45deg, #00d2ff, #3a7bd5, #ffffff, #3a7bd5, #00d2ff);
}

.hero-button-secondary {
    position: relative;
    width: 10rem; /* w-40 */
    height: 2.75rem; /* h-11 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 0.875rem; /* text-sm */
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    cursor: pointer;

    border: 1px solid #ffd700;
    border-radius: 50px;
    background: var(--bg-gradient);

    --glow-size: 25px;
    --glow-color-1: rgba(255, 223, 0, 0.8);
    --glow-color-2: rgba(255, 215, 0, 1);

    animation: pulse-glow 4s infinite ease-in-out;
    transition: box-shadow 0.4s ease, transform 0.3s ease-out;
    transform-style: preserve-3d;
    overflow: hidden;
    letter-spacing: 0.12em; /* increased tracking for premium feel */
}

/* Mouse tracking highlight for secondary button */
.hero-button-secondary::before {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                                rgba(255, 255, 255, 0.4),
                                transparent 40%);
    border-radius: 50px;
    opacity: 1;
    transition: opacity 0.4s;
    z-index: 2;
}

/* Core flare effect during unstable events for secondary button */
.hero-button-secondary.is-unstable::before {
    animation: core-flare 0.4s ease-out forwards;
}

/* Crack overlay for secondary button unstable events */
.hero-button-secondary.is-unstable .crack-overlay {
    opacity: 0.7;
    transition-duration: 0.1s;
}

/* Removed vibrate effect during unstable events for secondary button - keeping other effects intact */
.hero-button-secondary.is-unstable {
    /* Vibration animation removed per user request */
}

/* Remove secondary button after effect since we're using wrapper */

.hero-button-secondary span {
    position: relative;
    z-index: 4;
}

.hero-button-secondary:active {
    transform: scale(0.96) !important;
    transition-duration: 0.1s;
}

.hero-button-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Particle and effect styles */
.particle, .shockwave {
    position: fixed;
    border-radius: 50%;
    pointer-events: none;
}

.shockwave {
    z-index: 9999;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.particle {
    z-index: 9999;
    transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 1s ease-out;
}

/* Venting shard styles removed */



/* Media Queries */
/* Removed 640px breakpoint */

/* Tablet Breakpoint (768px) */
@media (min-width: 768px) {
    .hero-text-content {
        margin-left: 2rem; /* Slightly increased from mobile, merged from previous 640px styles */
        margin-bottom: 1.75rem; /* Increased from default, incorporating 640px margin-bottom */
    }

    .hero-title {
        font-size: 2.25rem; /* Kept from 640px breakpoint */
        margin-bottom: 0.75rem; /* Kept from 640px breakpoint */
    }

    .hero-subtitle {
        font-size: 1.25rem; /* Kept from 640px breakpoint */
    }

    .hero-buttons-container {
        margin-left: 2rem; /* Slightly increased from mobile */
        margin-bottom: 2rem; /* Slightly increased */
        gap: 1.5rem; /* Increased gap between buttons, merged from 640px */
        flex-wrap: wrap;
    }

    .hero-button,
    .hero-button-secondary {
        width: 11rem; /* Slightly wider than mobile */
        height: 2.9rem; /* Slightly taller than mobile */
        font-size: 0.95rem; /* Slightly larger font */
    }
} 

/* Laptop Breakpoint (1024px) */
@media (min-width: 1024px) {
    .hero-text-content {
        margin-left: 2.5rem; /* Further increased */
        margin-bottom: 2rem; /* Further increased */
    }

    .hero-buttons-container {
        margin-left: 2.5rem; /* Further increased */
        margin-bottom: 2.25rem; /* Further increased */
        gap: 1.5rem; /* Increased gap between buttons */
    }

    .hero-button,
    .hero-button-secondary {
        width: 12rem; /* Wider buttons */
        height: 3.1rem; /* Taller buttons */
        font-size: 1rem; /* Larger font */
    }
} 