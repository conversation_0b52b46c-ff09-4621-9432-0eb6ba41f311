'use client';

import { useState, FormEvent, useEffect } from 'react';
import { Send, Phone, Mail, MapPin, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';

export default function ContactPage() {
  const { customer, isAuthenticated, isLoading: authLoading } = useAuth();
  const { translations } = useLanguage();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    category: 'order',
    description: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Auto-fill form with customer information when logged in
  useEffect(() => {
    if (isAuthenticated && customer) {
      setFormData(prevData => ({
        ...prevData,
        name: `${customer.firstName} ${customer.lastName}`,
        email: customer.email
      }));
    }
  }, [isAuthenticated, customer]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Validate form
      if (!formData.name || !formData.email || !formData.description) {
        throw new Error(translations.required_fields_error);
      }
      
      // Prepare the data for submission
      const ticketData = {
        customer: {
          name: formData.name,
          email: formData.email
        },
        category: formData.category,
        description: formData.description
      };
      
      // Submit the form data to the API
      const response = await fetch('/api/support-tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || translations.submit_error);
      }
      
      // Handle success
      setSuccess(translations.success_message);
      setFormData({
        name: isAuthenticated && customer ? `${customer.firstName} ${customer.lastName}` : '',
        email: isAuthenticated && customer ? customer.email : '',
        category: 'order',
        description: ''
      });
      
    } catch (err: any) {
      setError(err.message || translations.unexpected_error);
      console.error('Error submitting form:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div id="contact-page-container" className="min-h-screen bg-[#f8f8f8]">
      <div id="contact-page-content" className="max-w-6xl mx-auto px-4 py-6">
        <h1 className="text-3xl font-bold text-center mb-10 font-serif">{translations.contact_us_heading}</h1>
        
        <div id="contact-main-grid" className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {/* Contact Information */}
          <section id="contact-info-section" className="bg-transparent  rounded-lg p-6">
            <div id="contact-details-list" className="space-y-6">
              <div id="address-item" className="flex items-start">
                <MapPin className="w-6 h-6 text-blue-600 mr-3 mt-1" />
                <div id="address-content">
                  <h3 className="font-medium">{translations.address_title}</h3>
                  <p className="text-gray-700 text-sm">{translations.address_value}</p>
                </div>
              </div>
              
              <div id="email-item" className="flex items-start">
                <Mail className="w-6 h-6 text-blue-600 mr-3 mt-1" />
                <div id="email-content">
                  <h3 className="font-medium">{translations.email_title}</h3>
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline text-sm">
                    <EMAIL>
                  </a>
                </div>
              </div>
              
              <div id="phone-item" className="flex items-start">
                <Phone className="w-6 h-6 text-blue-600 mr-3 mt-1" />
                <div id="phone-content">
                  <h3 className="font-medium">{translations.contact_title}</h3>
                  <p className="text-gray-700 text-sm">+39 3483594687</p>
                  <p className="text-gray-700 text-sm">+39 3452421202</p>
                </div>
              </div>
            </div>
            
            <section id="business-hours-section" className="mt-10">
              <h3 className="text-lg font-semibold mb-4">{translations.business_hours}</h3>
              <div id="business-hours-grid" className="grid grid-cols-2 gap-2 text-sm">
                <div id="monday-friday-label">{translations.monday_friday}</div>
                <div id="monday-friday-hours">{translations.monday_friday_hours}</div>
                <div id="saturday-label">{translations.saturday}</div>
                <div id="saturday-hours">{translations.saturday_hours}</div>
                <div id="sunday-label">{translations.sunday}</div>
                <div id="sunday-hours">{translations.sunday_hours}</div>
              </div>
            </section>
          </section>
          
          {/* Customer Complaint Form */}
          <section id="contact-form-section" className="rounded-lg p-6" style={{backgroundColor: '#f8f8f8'}}>
            <h2 className="text-2xl font-semibold mb-6">{translations.submit_complaint}</h2>
            
            {isAuthenticated && (
              <div id="logged-in-message" className="bg-blue-50 border border-blue-200 text-blue-800 rounded-md p-4 mb-6">
                {translations.logged_in_as} {customer?.firstName} {customer?.lastName}. {translations.info_prefilled}
              </div>
            )}
            
            {success && (
              <div id="success-message" className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4 mb-6">
                {success}
              </div>
            )}
            
            {error && (
              <div id="error-message" className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
                {error}
              </div>
            )}
            
            {authLoading ? (
              <div id="loading-container" className="flex justify-center items-center py-10">
                <Loader2 size={30} className="animate-spin text-blue-500" />
                <span className="ml-2">{translations.loading_info}</span>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div id="name-field">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    {translations.full_name} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  />
                </div>
                
                <div id="email-field">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {translations.email_address} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  />
                </div>
                

                
                <div id="category-field">
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                    {translations.inquiry_type} *
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="order">{translations.order_inquiry}</option>
                    <option value="product">{translations.product_information}</option>
                    <option value="shipping">{translations.shipping_delivery}</option>
                    <option value="return">{translations.returns_refunds}</option>
                    <option value="general">{translations.general_inquiry}</option>
                    <option value="complaint">{translations.complaint}</option>
                  </select>
                </div>


                
                <div id="description-field">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    {translations.message} *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  ></textarea>
                </div>
                
                <div id="submit-button-container">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full px-4 py-2 bg-gradient-to-tl from-[#51575F] to-[#1F2937] text-white border-none rounded-md font-medium flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] hover:from-[#6B7280] hover:to-[#374151] active:from-[#4B5563] active:to-[#111827] focus:outline-none focus:ring-2 focus:ring-[#a3003f]/50"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 size={20} className="animate-spin mr-2" />
                        {translations.submitting}
                      </>
                    ) : (
                      <>
                        <Send size={18} className="mr-2" />
                        {translations.send_message}
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </section>
        </div>
      </div>
    </div>
  );
} 