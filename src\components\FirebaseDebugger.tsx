'use client';

import { useState, useEffect } from 'react';

export default function FirebaseDebugger() {
  const [envVars, setEnvVars] = useState<Record<string, string | undefined>>({});
  const [windowVars, setWindowVars] = useState<Record<string, string | undefined>>({});
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<string>('Checking...');
  const [isMounted, setIsMounted] = useState(false);
  const [notificationSupported, setNotificationSupported] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<string | null>(null);
  
  // Set isMounted to true when component mounts (client-side only)
  useEffect(() => {
    setIsMounted(true);
    
    // Now it's safe to check window
    if (typeof window !== 'undefined') {
      // Check environment variables
      const env = {
        NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
        NEXT_PUBLIC_FIREBASE_VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      };
      setEnvVars(env);
      
      // Check window variables
      const win = {
        ENV_FIREBASE_API_KEY: window.ENV_FIREBASE_API_KEY,
        ENV_FIREBASE_AUTH_DOMAIN: window.ENV_FIREBASE_AUTH_DOMAIN,
        ENV_FIREBASE_PROJECT_ID: window.ENV_FIREBASE_PROJECT_ID,
        ENV_FIREBASE_STORAGE_BUCKET: window.ENV_FIREBASE_STORAGE_BUCKET,
        ENV_FIREBASE_MESSAGING_SENDER_ID: window.ENV_FIREBASE_MESSAGING_SENDER_ID,
        ENV_FIREBASE_APP_ID: window.ENV_FIREBASE_APP_ID,
        ENV_FIREBASE_VAPID_KEY: window.ENV_FIREBASE_VAPID_KEY,
      };
      setWindowVars(win);
      
      // Check Notification API support
      setNotificationSupported('Notification' in window);
      if ('Notification' in window) {
        setNotificationPermission(Notification.permission);
      }
      
      // Check service worker registration
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations()
          .then(registrations => {
            if (registrations.length === 0) {
              setServiceWorkerStatus('No service workers registered');
            } else {
              const swInfo = registrations.map(reg => {
                return `${reg.scope} (${reg.active ? 'active' : reg.installing ? 'installing' : 'waiting'})`;
              }).join(', ');
              setServiceWorkerStatus(`Registered: ${swInfo}`);
            }
          })
          .catch(err => {
            setServiceWorkerStatus(`Error checking service workers: ${err}`);
          });
      } else {
        setServiceWorkerStatus('Service workers not supported in this browser');
      }
    }
  }, []);
  
  // Don't render anything during server-side rendering
  if (!isMounted) {
    return (
      <div className="bg-white shadow-md rounded-lg p-4 mt-4">
        <h2 className="text-lg font-semibold mb-2">Firebase Configuration Debugger</h2>
        <p>Loading debugger...</p>
      </div>
    );
  }
  
  const anyMissingEnvVars = Object.values(envVars).some(val => !val);
  const anyMissingWindowVars = Object.values(windowVars).some(val => !val);
  
  const maskString = (str: string | undefined): string => {
    if (!str) return 'undefined';
    if (str.length <= 6) return '******';
    return str.substring(0, 3) + '...' + str.substring(str.length - 3);
  };
  
  return (
    <div className="bg-white shadow-md rounded-lg p-4 mt-4">
      <h2 className="text-lg font-semibold mb-2">Firebase Configuration Debugger</h2>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-1">Environment Variables</h3>
        <div className="bg-gray-50 p-2 rounded text-sm">
          {Object.entries(envVars).map(([key, value]) => (
            <div key={key} className="grid grid-cols-2 gap-2 mb-1">
              <span className="text-gray-700">{key}:</span>
              <span className={!value ? 'text-red-500' : 'text-green-600'}>
                {maskString(value)}
              </span>
            </div>
          ))}
          <div className="mt-2 text-xs">
            {anyMissingEnvVars ? (
              <span className="text-red-500">⚠️ Some environment variables are missing</span>
            ) : (
              <span className="text-green-600">✓ All environment variables are defined</span>
            )}
          </div>
        </div>
      </div>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-1">Window Variables</h3>
        <div className="bg-gray-50 p-2 rounded text-sm">
          {Object.entries(windowVars).map(([key, value]) => (
            <div key={key} className="grid grid-cols-2 gap-2 mb-1">
              <span className="text-gray-700">{key}:</span>
              <span className={!value ? 'text-red-500' : 'text-green-600'}>
                {maskString(value)}
              </span>
            </div>
          ))}
          <div className="mt-2 text-xs">
            {anyMissingWindowVars ? (
              <span className="text-red-500">⚠️ Some window variables are missing</span>
            ) : (
              <span className="text-green-600">✓ All window variables are defined</span>
            )}
          </div>
        </div>
      </div>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-1">Service Worker Status</h3>
        <div className="bg-gray-50 p-2 rounded text-sm">
          <span className={serviceWorkerStatus.includes('No') || serviceWorkerStatus.includes('Error') ? 'text-red-500' : 'text-green-600'}>
            {serviceWorkerStatus}
          </span>
        </div>
      </div>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-1">Notification API</h3>
        <div className="bg-gray-50 p-2 rounded text-sm">
          <p>
            Supported: <span className={notificationSupported ? 'text-green-600' : 'text-red-500'}>
              {notificationSupported ? 'Yes' : 'No'}
            </span>
          </p>
          {notificationSupported && notificationPermission && (
            <p>
              Current permission: <span className={
                notificationPermission === 'granted' ? 'text-green-600' :
                notificationPermission === 'denied' ? 'text-red-500' :
                'text-yellow-500'
              }>
                {notificationPermission}
              </span>
            </p>
          )}
        </div>
      </div>
      
      <div className="mt-4">
        <button 
          onClick={() => {
            // Force reload the page to refresh the debugger
            if (typeof window !== 'undefined') {
              window.location.reload();
            }
          }}
          className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm"
        >
          Refresh Debugger
        </button>
      </div>
    </div>
  );
} 