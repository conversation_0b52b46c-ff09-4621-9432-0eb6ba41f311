'use client';

import { useState, useEffect } from 'react';
import { requestNotificationPermission, getFCMToken } from '@/lib/firebase';
import FirebaseDebugger from '@/components/FirebaseDebugger';

export default function NotificationTestPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [serverTestLoading, setServerTestLoading] = useState(false);
  const [serverTestResult, setServerTestResult] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [configCheckResult, setConfigCheckResult] = useState<any>(null);
  const [configCheckLoading, setConfigCheckLoading] = useState(false);
  
  // Set isMounted when the component has mounted on the client
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  const handleRequestPermission = async () => {
    setLoading(true);
    try {
      const permissionResult = await requestNotificationPermission();
      console.log('Permission request result:', permissionResult);
      setResult(`Result: ${typeof permissionResult === 'string' ? permissionResult : JSON.stringify(permissionResult)}`);
      
      // If we got a token back, store it
      if (typeof permissionResult === 'string' && permissionResult.length > 20) {
        setFcmToken(permissionResult);
      }
    } catch (error) {
      console.error('Error requesting permission:', error);
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };
  
  const handleGetToken = async () => {
    setLoading(true);
    try {
      // Safely get the vapidKey
      let vapidKey = '';
      if (typeof window !== 'undefined') {
        vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || window.ENV_FIREBASE_VAPID_KEY || '';
      } else {
        vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || '';
      }
      
      if (!vapidKey) {
        throw new Error('VAPID key not found');
      }
      
      const token = await getFCMToken(vapidKey);
      console.log('FCM token:', token);
      
      if (token) {
        setFcmToken(token);
        setResult(`Token obtained: ${token.substring(0, 10)}...${token.substring(token.length - 10)}`);
      } else {
        setResult('Failed to get FCM token');
      }
    } catch (error) {
      console.error('Error getting token:', error);
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };
  
  const handleForceRefresh = async () => {
    setLoading(true);
    try {
      // First unsubscribe from any existing subscriptions
      if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        for (const registration of registrations) {
          const subscription = await registration.pushManager.getSubscription();
          if (subscription) {
            await subscription.unsubscribe();
            console.log('Unsubscribed from existing push subscription');
          }
        }
        
        setResult('Unsubscribed from all push subscriptions. Now generating a new token...');
        
        // Small delay to ensure unsubscribe takes effect
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Get a new token
        await handleGetToken();
      } else {
        setResult('Service worker not supported or not available');
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      setResult(`Error refreshing: ${error instanceof Error ? error.message : String(error)}`);
      setLoading(false);
    }
  };
  
  const handleServerTest = async () => {
    if (!fcmToken) {
      alert('No FCM token available. Please request permission first.');
      return;
    }
    
    setServerTestLoading(true);
    try {
      // Send test notification via server
      const response = await fetch('/api/push-notifications/test-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: fcmToken }),
      });
      
      const data = await response.json();
      console.log('Server test response:', data);
      
      if (data.success) {
        setServerTestResult(`Success! Message ID: ${data.messageId}`);
      } else {
        setServerTestResult(`Failed: ${data.error}`);
        if (data.details) {
          setServerTestResult(prev => `${prev}\n\nDetails: ${JSON.stringify(data.details, null, 2)}`);
        }
      }
    } catch (error) {
      console.error('Error sending server test:', error);
      setServerTestResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setServerTestLoading(false);
    }
  };

  const checkFirebaseConfig = async () => {
    setConfigCheckLoading(true);
    try {
      const response = await fetch('/api/push-notifications/check-config');
      const data = await response.json();
      setConfigCheckResult(data);
    } catch (error) {
      console.error('Error checking config:', error);
      setConfigCheckResult({ 
        success: false, 
        error: String(error) 
      });
    } finally {
      setConfigCheckLoading(false);
    }
  };
  
  // Add verifyToken function
  const verifyToken = async () => {
    if (!fcmToken) {
      alert('No FCM token available. Please get a token first.');
      return;
    }
    
    setServerTestLoading(true);
    try {
      // Verify token via server
      const response = await fetch('/api/push-notifications/verify-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: fcmToken }),
      });
      
      const data = await response.json();
      console.log('Token verification response:', data);
      
      if (data.success && data.isValid) {
        setServerTestResult(`Token is valid! Message ID: ${data.messageId}`);
      } else {
        setServerTestResult(`Token is NOT valid: ${data.error || 'Unknown error'}`);
        if (data.errorCode) {
          setServerTestResult(prev => `${prev}\nError code: ${data.errorCode}`);
        }
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      setServerTestResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setServerTestLoading(false);
    }
  };
  
  // Don't render anything during server-side rendering
  if (!isMounted) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Push Notification Test Page</h1>
        <p>Loading notification tests...</p>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Push Notification Test Page</h1>
      
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <h2 className="text-lg font-medium mb-3">Firebase Configuration Check</h2>
        <p className="mb-4 text-sm text-gray-600">
          Check if your Firebase client and admin configurations match. This is important for FCM to work properly.
        </p>
        
        <button
          onClick={checkFirebaseConfig}
          disabled={configCheckLoading}
          className="px-4 py-2 bg-yellow-600 text-white rounded-md"
        >
          {configCheckLoading ? 'Checking...' : 'Check Configuration'}
        </button>
        
        {configCheckResult && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <div className="flex items-center mb-2">
              <div className={`w-3 h-3 rounded-full mr-2 ${configCheckResult.success && configCheckResult.projectsMatch ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <p className="font-medium">{configCheckResult.message}</p>
            </div>
            
            <div className="mt-3">
              <p className="text-sm font-medium mb-1">Client Config (Browser)</p>
              <pre className="text-xs bg-gray-100 p-2 rounded">
                {JSON.stringify(configCheckResult.clientConfig, null, 2)}
              </pre>
            </div>
            
            <div className="mt-3">
              <p className="text-sm font-medium mb-1">Admin Config (Server)</p>
              <pre className="text-xs bg-gray-100 p-2 rounded">
                {JSON.stringify(configCheckResult.adminConfig, null, 2)}
              </pre>
            </div>
            
            <div className="mt-3">
              <p className="text-sm font-medium mb-1">Admin SDK Status</p>
              <p className={`text-sm ${configCheckResult.adminInitialized ? 'text-green-600' : 'text-red-600'}`}>
                {configCheckResult.adminInitialized ? 'Initialized ✓' : 'Not Initialized ✗'}
              </p>
              {configCheckResult.projectInfo && (
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
                  {JSON.stringify(configCheckResult.projectInfo, null, 2)}
                </pre>
              )}
            </div>
          </div>
        )}
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <h2 className="text-lg font-medium mb-3">Request Permission</h2>
        <p className="mb-4 text-sm text-gray-600">
          Click the button below to request notification permission. 
          This will also try to register your FCM token if permission is granted.
        </p>
        
        <div className="flex space-x-4">
          <button
            onClick={handleRequestPermission}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md"
          >
            {loading ? 'Requesting...' : 'Request Permission'}
          </button>
          
          <button
            onClick={handleGetToken}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 text-white rounded-md"
          >
            {loading ? 'Loading...' : 'Get FCM Token'}
          </button>
          
          <button
            onClick={handleForceRefresh}
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded-md"
          >
            {loading ? 'Refreshing...' : 'Force Refresh Token'}
          </button>
        </div>
        
        {result && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <h2 className="text-lg font-medium mb-3">Server Test Notification</h2>
        <p className="mb-4 text-sm text-gray-600">
          Send a test notification through Firebase Cloud Messaging. This uses the server
          to send a notification to your device.
        </p>
        
        <div className="flex space-x-4">
          <button
            onClick={handleServerTest}
            disabled={serverTestLoading || !fcmToken}
            className="px-4 py-2 bg-green-600 text-white rounded-md"
          >
            {serverTestLoading ? 'Sending...' : 'Send via FCM Server'}
          </button>
          
          <button
            onClick={verifyToken}
            disabled={serverTestLoading || !fcmToken}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md"
          >
            {serverTestLoading ? 'Checking...' : 'Verify Token'}
          </button>
        </div>
        
        {serverTestResult && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <pre className="text-sm whitespace-pre-wrap">{serverTestResult}</pre>
          </div>
        )}
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <h2 className="text-lg font-medium mb-3">Local Test Notification</h2>
        <p className="mb-4 text-sm text-gray-600">
          If permission is granted, you can test creating a local notification here.
          This doesn't use Firebase, just the browser's Notification API.
        </p>
        
        <button
          onClick={() => {
            // Safely check for Notification support
            if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
              new Notification('Test Notification', {
                body: 'This is a test notification created locally',
                icon: '/logo.png'
              });
            } else {
              alert('Notification permission not granted');
            }
          }}
          className="px-4 py-2 bg-purple-600 text-white rounded-md"
        >
          Send Local Notification
        </button>
      </div>
      
      {/* Only render FirebaseDebugger on the client side */}
      {isMounted && <FirebaseDebugger />}
    </div>
  );
} 