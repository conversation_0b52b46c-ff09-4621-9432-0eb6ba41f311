import { CloudFrontClient, CreateDistributionCommand } from '@aws-sdk/client-cloudfront';

// CloudFront configuration from environment variables
const cloudfrontConfig = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get CloudFront client
const getCloudFrontClient = () => {
  return new CloudFrontClient(cloudfrontConfig);
};

// CloudFront domains from environment variables
const videosCloudfrontDomain = process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN || '';
const imagesCloudfrontDomain = process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN || '';
const chunkedVideosCloudfrontDomain = process.env.NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN || 
  // Fallback to videos domain if chunked videos domain not specifically configured
  videosCloudfrontDomain || 
  // Further fallback to one of the configured CloudFront domains from next.config.js
  'd39l10jdryt7ww.cloudfront.net';

/**
 * Clean CloudFront domain by removing https:// prefix if present
 */
function cleanCloudFrontDomain(domain: string): string {
  return domain.replace(/^https?:\/\//, '');
}

/**
 * Generate a CloudFront URL for a video
 * @param key The S3 object key (filename)
 * @returns The full CloudFront URL for the video
 */
export function getCloudFrontVideoUrl(key: string): string {
  if (!videosCloudfrontDomain) {
    console.warn('Videos CloudFront domain not configured. Falling back to direct S3 access.');
    return `/api/test-s3-video/${key}`;
  }
  return `https://${cleanCloudFrontDomain(videosCloudfrontDomain)}/${key}`;
}

/**
 * Generate a CloudFront URL for an image
 * @param key The S3 object key (filename)
 * @returns The full CloudFront URL for the image
 */
export function getCloudFrontImageUrl(key: string): string {
  if (!imagesCloudfrontDomain) {
    console.warn('Images CloudFront domain not configured. Falling back to direct S3 access.');
    return `/api/test-s3-image/${key}`;
  }
  return `https://${cleanCloudFrontDomain(imagesCloudfrontDomain)}/${key}`;
}

/**
 * Alias for getCloudFrontImageUrl for backward compatibility
 * @param key The S3 object key (filename)
 * @returns The full CloudFront URL for the image
 */
export function getImageUrl(key: string): string {
  return getCloudFrontImageUrl(key);
}

/**
 * Generate a CloudFront URL for chunked videos (HLS segments)
 * @param key The S3 object key (filename) for the chunked video
 * @returns The full CloudFront URL for the chunked video or null if unavailable
 */
export function getCloudFrontChunkedVideoUrl(key: string): string | null {
  if (!chunkedVideosCloudfrontDomain) {
    console.warn('Chunked videos CloudFront domain not configured. Falling back to direct S3 access.');
    return null; // Return null to indicate no CloudFront URL available
  }
  
  const cleanDomain = cleanCloudFrontDomain(chunkedVideosCloudfrontDomain);
  const url = `https://${cleanDomain}/${key}`;
  
  console.log(`🚀 Generated CloudFront URL: ${url}`);
  return url;
}

/**
 * Generate CloudFront URLs for direct hero videos from S3 paths
 * Specifically for the user's S3 bucket: s3://videosbucket2025/mobile-chunk/ and desktop-chunk/
 * @returns Object with mobile and desktop CloudFront URLs
 */
export function getDirectHeroVideoUrls(): { mobile: string; desktop: string } {
  // Direct CloudFront URLs for ultra-fast video loading
  const directMobileUrl = 'https://d39l10jdryt7ww.cloudfront.net/mobile+hero.mp4';
  const directDesktopUrl = 'https://d39l10jdryt7ww.cloudfront.net/desktop+hero.mp4';
  
  console.log('🚀 Direct CloudFront Hero Video URLs Generated:');
  console.log('📱 Mobile Video:', directMobileUrl);
  console.log('🖥️ Desktop Video:', directDesktopUrl);
  console.log('⚡ Using CloudFront CDN for maximum performance');
  
  return {
    mobile: directMobileUrl,
    desktop: directDesktopUrl
  };
}

/**
 * Create a new CloudFront distribution for an S3 bucket
 * This function should be called once during setup
 */
export async function createCloudFrontDistribution(bucketName: string) {
  try {
    const client = getCloudFrontClient();
    
    const command = new CreateDistributionCommand({
      DistributionConfig: {
        CallerReference: Date.now().toString(), // Required unique identifier
        Origins: {
          Quantity: 1,
          Items: [
            {
              Id: 'S3Origin',
              DomainName: `${bucketName}.s3.${cloudfrontConfig.region}.amazonaws.com`,
              S3OriginConfig: {
                OriginAccessIdentity: ''
              }
            }
          ]
        },
        DefaultCacheBehavior: {
          TargetOriginId: 'S3Origin',
          ViewerProtocolPolicy: 'redirect-to-https',
          AllowedMethods: {
            Quantity: 2,
            Items: ['GET', 'HEAD'],
            CachedMethods: {
              Quantity: 2,
              Items: ['GET', 'HEAD']
            }
          },
          ForwardedValues: {
            QueryString: false,
            Cookies: {
              Forward: 'none'
            }
          },
          MinTTL: 0,
          DefaultTTL: 86400,
          MaxTTL: 31536000,
          Compress: true
        },
        Enabled: true,
        Comment: `CloudFront distribution for ${bucketName}`,
        PriceClass: 'PriceClass_100',
        DefaultRootObject: '',
        HttpVersion: 'http2',
        IsIPV6Enabled: true
      }
    });

    const response = await client.send(command);
    return response.Distribution;
  } catch (error: any) {
    console.error('Error creating CloudFront distribution:', error);
    throw error;
  }
} 