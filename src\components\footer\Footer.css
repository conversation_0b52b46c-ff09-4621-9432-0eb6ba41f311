/* Footer Subscribe Section */
.footer-subscribe-section {
  padding: 48px 16px;
  position: relative;
  background-color: #f8f8f8;
}

@media (min-width: 640px) {
  .footer-subscribe-section {
    padding: 64px 24px;
  }
}

@media (min-width: 1024px) {
  .footer-subscribe-section {
    padding: 80px 32px;
  }
}

.footer-subscribe-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  background-color: #f8f8f8;
}

.footer-subscribe-container {
  max-width: 768px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

.footer-subscribe-heading {
  font-size: 19.31px;
  font-weight: 500;
  color: black;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 0.04em;
  padding: 0 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-family: var(--font-dosis), sans-serif;
}

@media (min-width: 640px) {
  .footer-subscribe-heading {
    font-size: 21px;
    margin-bottom: 40px;
    padding: 0 24px;
  }
}

@media (min-width: 768px) {
  .footer-subscribe-heading {
    font-size: 23px;
    padding: 0 40px;
  }
}

@media (min-width: 1024px) {
  .footer-subscribe-heading {
    font-size: 25px;
  }
}

.footer-subscribe-form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 640px;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .footer-subscribe-form-container {
    gap: 24px;
  }
}

/* Footer Section Spacer */
.footer-section-spacer {
  height: 32px;
  background-color: #f8f8f8;
}

@media (min-width: 640px) {
  .footer-section-spacer {
    height: 40px;
  }
}

@media (min-width: 768px) {
  .footer-section-spacer {
    height: 48px;
  }
}

/* Footer Contact Section */
.footer-contact-section {
  padding: 24px 16px;
  background-color: #f8f8f8;
}

@media (min-width: 640px) {
  .footer-contact-section {
    padding: 40px 24px;
  }
}

.footer-contact-container {
  max-width: 1280px;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .footer-contact-container {
    text-align: center;
  }
}

.footer-contact-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .footer-contact-content {
    flex-direction: row;
    gap: 170px;
    padding-left: 20%;
  }
}

@media (min-width: 1024px) {
  .footer-contact-content {
    justify-content: center;
    gap: 350px;
    padding-left: 15%;
  }
}

.footer-contact-info {
  width: 100%;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .footer-contact-info {
    width: 50%;
    margin-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .footer-contact-info {
    width: auto;
    min-width: 300px;
    text-align: left;
  }
}

.footer-contact-heading {
  font-size: 16px;
  font-weight: bold;
  text-transform: capitalize;
  letter-spacing: 0.7px;
  color: black;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .footer-contact-heading {
    font-size: 17px;
    letter-spacing: 0.75px;
    margin-bottom: 18px;
  }
}

@media (min-width: 1024px) {
  .footer-contact-heading {
    font-size: 18px;
    letter-spacing: 0.8px;
    margin-bottom: 20px;
  }
}

.footer-contact-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-contact-item {
  display: flex;
  align-items: center;
}

.footer-contact-label {
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  margin-right: 8px;
}

.footer-contact-value {
  font-size: 12px;
  font-weight: normal;
  text-transform: capitalize;
  letter-spacing: 0.7px;
}

.footer-useful-links {
  width: 100%;
}

@media (min-width: 768px) {
  .footer-useful-links {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .footer-useful-links {
    width: auto;
    min-width: 300px;
    text-align: left;
  }
}

.footer-links-heading {
  font-size: 16px;
  font-weight: bold;
  text-transform: capitalize;
  letter-spacing: 0.7px;
  color: black;
  margin-bottom: 16px;
}

.footer-links-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.footer-link-item {
  display: inline-block;
  font-size: 14px;
  font-weight: normal;
  text-transform: capitalize;
  letter-spacing: 0.7px;
  color: black;
  transition: all 0.15s ease-in-out;
}

.footer-social-section {
  margin-bottom: 40px;
  width: 100%;
}

@media (min-width: 768px) {
  .footer-social-section {
    margin-bottom: 50px;
  }
}

@media (min-width: 1024px) {
  .footer-social-section {
    margin-bottom: 60px;
  }
}

.footer-social-heading-container {
  text-align: left;
  margin-bottom: 25px;
}

@media (min-width: 1024px) {
  .footer-social-heading-container {
    text-align: center;
  }
}

@media (min-width: 768px) {
  .footer-social-heading-container {
    text-align: center;
  }
}

.footer-social-heading {
  font-size: 16px;
  font-weight: 700;
  font-family: Dosis;
  color: black;
  text-transform: capitalize;
  line-height: 25.01px;
  letter-spacing: 0.16px;
}

@media (min-width: 768px) {
  .footer-social-heading {
    font-size: 18px;
    line-height: 27.5px;
    letter-spacing: 0.18px;
  }
}

@media (min-width: 1024px) {
  .footer-social-heading {
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0.2px;
  }
}

.footer-social-icons {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 40px;
}

.footer-social-icon {
  width: 26.45px;
  height: 26.45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 13.22px;
  color: white;
  position: relative;
}

@media (min-width: 768px) {
  .footer-social-icon {
    width: 30px;
    height: 30px;
    border-radius: 15px;
  }
}

@media (min-width: 1024px) {
  .footer-social-icon {
    width: 35px;
    height: 35px;
    border-radius: 17.5px;
  }
}

.footer-social-icon:hover {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.facebook-icon {
  background-color: #28158F;
}

.twitter-icon {
  background-color: black;
}

.instagram-icon {
  background-color: #A232A6;
}

.youtube-icon {
  background-color: #E11919;
}

.social-icon-img {
  color: white;
  width: 13.22px;
  height: 13.22px;
  position: absolute;
  left: 6.61px;
  top: 6.61px;
}

@media (min-width: 768px) {
  .social-icon-img {
    width: 15px;
    height: 15px;
    left: 7.5px;
    top: 7.5px;
  }
}

@media (min-width: 1024px) {
  .social-icon-img {
    width: 17.5px;
    height: 17.5px;
    left: 8.75px;
    top: 8.75px;
  }
}

.facebook-icon .social-icon-img {
  width: 7.08px !important;
  height: 13.22px !important;
  left: 9.68px !important;
  top: 6.61px !important;
}

@media (min-width: 768px) {
  .facebook-icon .social-icon-img {
    width: 8px !important;
    height: 15px !important;
    left: 11px !important;
    top: 7.5px !important;
  }
}

@media (min-width: 1024px) {
  .facebook-icon .social-icon-img {
    width: 9.5px !important;
    height: 17.5px !important;
    left: 12.75px !important;
    top: 8.75px !important;
  }
}

.youtube-icon .social-icon-img {
  width: 13.17px;
  height: 9.62px;
  top: 8.41px;
}

@media (min-width: 768px) {
  .youtube-icon .social-icon-img {
    width: 15px;
    height: 11px;
    top: 9.5px;
  }
}

@media (min-width: 1024px) {
  .youtube-icon .social-icon-img {
    width: 17.5px;
    height: 13px;
    top: 11px;
  }
}

/* Footer Main Section */
.footer-main-section {
  padding: 0px 16px 58.24px 16px;
  background-color: #f8f8f8;
  color: black;
}

@media (min-width: 640px) {
  .footer-main-section {
    padding: 24px 24px 58.24px 24px;
  }
}

@media (min-width: 1024px) {
  .footer-main-section {
    padding: 24px 32px 58.24px 32px;
  }
}

.footer-main-container {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.footer-payment-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  width: 100%;
  margin-bottom: 40px;
}

.footer-payment-icons img,
.footer-payment-icons svg {
  width: 50px;
  height: 30px;
  object-fit: contain;
}

@media (min-width: 768px) {
  .footer-payment-icons {
    gap: 20px;
    margin-bottom: 50px;
  }

  .footer-payment-icons img,
  .footer-payment-icons svg {
    width: 60px;
    height: 36px;
  }
}

@media (min-width: 1024px) {
  .footer-payment-icons {
    margin-bottom: 60px;
  }

  .footer-payment-icons img,
  .footer-payment-icons svg {
    width: 70px;
    height: 42px;
  }
}

.footer-copyright-container {
  text-align: center;
  width: 100%;
  padding: 10px 0;
}

@media (min-width: 768px) {
  .footer-copyright-container {
    padding: 15px 0;
  }
}

@media (min-width: 1024px) {
  .footer-copyright-container {
    padding: 20px 0;
  }
}

.footer-copyright-text {
  color: black;
  font-size: 14px;
  font-family: var(--font-dosis), sans-serif;
  font-weight: 500;
  line-height: 25px;
  letter-spacing: 0.70px;
  word-wrap: break-word;
}

@media (min-width: 640px) {
  .footer-copyright-text {
    font-size: 16px;
  }
}

@media (min-width: 768px) {
  .footer-copyright-text {
    font-size: 17px;
    line-height: 27px;
    letter-spacing: 0.75px;
  }
}

@media (min-width: 1024px) {
  .footer-copyright-text {
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0.80px;
  }
}

/* Currency and Language Switchers Container */
.footer-switchers-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  margin: 0 0 40px 0;
  padding: 0;
  border-top: none;
  border-bottom: none;
  gap: 40px;
  /* position: fixed;
  top: 50%;
  bottom: 0;
  left: 0;
  right: 0;
  z-index:1000; */
}

@media (min-width: 768px) {
  .footer-switchers-container {
    margin: 0 0 50px 0;
    padding: 0;
    gap: 60px;
  }
}

@media (min-width: 1024px) {
  .footer-switchers-container {
    margin: 0 0 60px 0;
  }
}

.footer-switchers-container::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

/* Currency Switcher */
.footer-currency-switcher {
  display: flex;
  justify-content: center;
  margin: 0;
  padding: 8px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-right: none;
}

@media (min-width: 640px) {
  .footer-currency-switcher {
    margin: 0;
    padding: 10px 0;
  }
}

.footer-language-switcher {
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  margin: 0;
  padding: 8px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
  .footer-language-switcher {
    margin: 0;
    padding: 10px 0;
  }
}

/* Styles for Switcher Text */
.switcher-text-style {
  color: #364153;
  font-size: 12.38px;
  font-family: var(--font-dosis), sans-serif;
  font-weight: 600;
  line-height: 18.57px;
  letter-spacing: 0.62px;
  word-wrap: break-word;
}

.custom-placeholder::placeholder {
  font-family: var(--font-dosis), sans-serif;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.04em;
  text-transform: capitalize;
}

.privacy-notice-text {
  font-family: var(--font-dosis), sans-serif;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.02em;
}

.subscribe-button {
  /* Exact match to .latest-articles-button */
  padding-left: 32px;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-radius: 9999px;
  background: linear-gradient(to top left, #51575F, #1F2937);
  color: white;
  font-family: var(--font-segoe-ui), sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 16.5px;
  letter-spacing: 1.83px;
  transition: all 0.3s;
  
  /* Additional properties for layout */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
}

.subscribe-button:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  background: linear-gradient(to top left, #6B7280, #374151);
}

.subscribe-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(to top left, #4B5563, #111827);
}

.subscribe-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.subscribe-button:disabled:hover {
  box-shadow: none;
  background: linear-gradient(to top left, #ff5100, #ffa100);
  transform: none;
}

/* Arrow animation */
.subscribe-button .arrow-icon {
  transition: transform 200ms ease-in-out;
}

.subscribe-button:hover .arrow-icon {
  transform: translateX(4px);
}

/* Loading icon */
.subscribe-button .loading-icon {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 

/* Responsive Font Sizes for Footer Contact Section */
@media (min-width: 768px) {
  .footer-contact-heading {
    font-size: 17px;
  }

  .footer-contact-label,
  .footer-contact-value,
  .footer-links-heading,
  .footer-link-item {
    font-size: 15px !important;
  }
}

@media (min-width: 1024px) {
  .footer-contact-heading {
    font-size: 18px;
  }

  .footer-contact-label,
  .footer-contact-value,
  .footer-links-heading,
  .footer-link-item {
    font-size: 16px !important;
  }
} 