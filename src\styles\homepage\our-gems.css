.our-gems-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem; /* equivalent to gap-5 */
    max-width: 100%; /* Default full width */
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 640px) {
    .our-gems-container {
        gap: 1.5rem; /* equivalent to sm:gap-6 */
        max-width: 90%; /* Slightly constrained on small screens */
    }
}

@media (min-width: 768px) {
    .our-gems-container {
        gap: 8rem; /* Extremely large gap for tablets */
        max-width: 80%; /* 80% width for tablets */
    }
}

@media (min-width: 1024px) {
    .our-gems-container {
        gap: 10rem; /* Extremely large gap for laptops */
        max-width: 70%; /* 70% width for laptops */
    }
}

.our-gems-card {
    background-color: #f8f8f8;
    border-radius: 0.75rem; /* rounded-xl */
    overflow: hidden;
    box-shadow: none;
    transition: box-shadow 0.3s ease-in-out;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.our-gems-card:hover {
    box-shadow: none;
}

.our-gems-image-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

.our-gems-image-wrapper {
    aspect-ratio: 4/3; /* Default mobile aspect ratio */
    width: 100%;
    position: relative;
}

@media (min-width: 640px) {
    .our-gems-image-wrapper {
        aspect-ratio: 1/1; /* Square on small screens and up */
    }
}

.our-gems-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease-in-out;
}

.our-gems-card:hover .our-gems-image {
    transform: scale(1.05); /* Slight zoom effect on hover, matching featured products */
}

.our-gems-text-container {
    background-color: #f8f8f8;
    padding: 0.75rem; /* py-3 */
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

@media (min-width: 640px) {
    .our-gems-text-container {
        padding: 1rem; /* sm:py-4 */
    }
}

.our-gems-title {
    font-family: var(--font-dosis), sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400; /* Regular */
    color: black;
    text-transform: capitalize;
    letter-spacing: 1px;
}

@media (min-width: 640px) {
    .our-gems-title {
        font-size: 16px; /* Keep consistent size */
        line-height: 24px;
    }
}

@media (min-width: 768px) {
    .our-gems-title {
        font-size: 16px; /* Keep consistent size */
        line-height: 24px;
    }
}

/* Our Gems Section Styles */
.our-gems-heading {
    font-family: var(--font-dosis), sans-serif;
    font-size: 25px;
    font-weight: 500;
    line-height: 36px;
    letter-spacing: 1.25px;
    color: rgb(0, 0, 0);
    text-align: center;
    margin-bottom: 0.75rem;
    text-transform: capitalize;
} 