import mongoose, { Schema, Document } from 'mongoose';

export interface IDiscount extends Document {
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minPurchase: number;
  maxUses: number;
  usedCount: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'inactive' | 'scheduled';
  products: string[];
  categories: string[];
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

const DiscountSchema: Schema = new Schema(
  {
    code: { 
      type: String, 
      required: true, 
      unique: true, 
      uppercase: true, 
      trim: true 
    },
    type: { 
      type: String, 
      enum: ['percentage', 'fixed'], 
      required: true 
    },
    value: { 
      type: Number, 
      required: true, 
      min: 0 
    },
    minPurchase: { 
      type: Number, 
      default: 0, 
      min: 0 
    },
    maxUses: { 
      type: Number, 
      default: 0 
    },
    usedCount: { 
      type: Number, 
      default: 0 
    },
    startDate: { 
      type: Date, 
      required: true 
    },
    endDate: { 
      type: Date, 
      required: true 
    },
    status: { 
      type: String, 
      enum: ['active', 'inactive', 'scheduled'], 
      default: 'active' 
    },
    products: [{ 
      type: Schema.Types.ObjectId, 
      ref: 'Product' 
    }],
    categories: [{ 
      type: String 
    }],
    description: { 
      type: String, 
      default: '' 
    }
  },
  {
    timestamps: true
  }
);

export default mongoose.models.Discount || mongoose.model<IDiscount>('Discount', DiscountSchema); 