import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(req: Request, { params }: Params) {
  try {
    const { id } = params;
    const { rewardId, rewardName, amount, spentDate, orderId } = await req.json();
    
    await dbConnect();
    
    // Validate input
    if (!rewardId || !rewardName || !amount || !spentDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Find customer
    const customer = await Customer.findById(id);
    
    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    // Initialize spentRewards array if it doesn't exist
    if (!customer.spentRewards) {
      customer.spentRewards = [];
    }
    
    // Initialize currentRewards array if it doesn't exist
    if (!customer.currentRewards) {
      customer.currentRewards = [];
    }
    
    // Add to spent rewards
    customer.spentRewards.push({
      id: Date.now(), // Use current timestamp as unique ID
      name: rewardName,
      spentDate: spentDate,
      amount: amount,
    });
    
    // Get current reward amount (if any)
    let currentRewardAmount = 0;
    if (customer.currentRewards.length > 0) {
      const match = customer.currentRewards[0].name.match(/\$(\d+)/);
      if (match && match[1]) {
        currentRewardAmount = parseInt(match[1], 10);
      }
    }
    
    // Calculate remaining reward amount
    const remainingAmount = currentRewardAmount - amount;
    
    // Update current rewards
    if (remainingAmount <= 0) {
      // If no rewards left, remove current rewards
      customer.currentRewards = [];
    } else {
      // Otherwise, update the reward with the remaining amount
      customer.currentRewards = [{
        id: Date.now(), // Generate a new ID
        name: `$${remainingAmount} Reward`,
        issuedDate: new Date().toISOString().split('T')[0]
      }];
    }
    
    // Save customer
    await customer.save();
    
    return NextResponse.json({
      success: true,
      message: 'Reward applied successfully',
      customer: customer
    });
  } catch (error: any) {
    console.error('Error applying customer reward:', error);
    return NextResponse.json(
      { 
        error: 'Failed to apply reward',
        details: error.message || 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 