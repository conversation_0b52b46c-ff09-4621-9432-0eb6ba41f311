import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Order from '@/models/Order';
import { Product } from '@/models/Product';

// GET - Fetch all orders with optional filters
export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    // Get query parameters for filtering
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const searchQuery = searchParams.get('q');
    
    // Build filter object
    const filter: any = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        // Add 1 day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        filter.createdAt.$lt = endDateObj;
      }
    }
    
    if (searchQuery) {
      filter.$or = [
        { orderNumber: { $regex: searchQuery, $options: 'i' } },
        { 'customer.name': { $regex: searchQuery, $options: 'i' } },
        { 'customer.email': { $regex: searchQuery, $options: 'i' } }
      ];
    }
    
    // Fetch orders
    const orders = await Order.find(filter)
      .sort({ createdAt: -1 })
      .lean();
    
    return NextResponse.json({ success: true, orders });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST - Create a new order
export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.customer || !data.items || !data.shippingAddress || !data.paymentMethod) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Ensure items is an array and not empty
    if (!Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Order must contain at least one item' },
        { status: 400 }
      );
    }
    
    // Generate order number
    const count = await Order.countDocuments();
    const timestamp = Date.now().toString().slice(-4);
    const orderNumber = `ORD-${String(count + 1).padStart(3, '0')}-${timestamp}`;
    
    // Create new order with reward information if provided
    const orderData = {
      ...data,
      orderNumber,
      // Include reward information if present
      rewardApplied: data.reward ? {
        id: data.reward.id,
        name: data.reward.name,
        amount: data.reward.amount
      } : null
    };
    
    // Remove the original reward field
    if (orderData.reward) {
      delete orderData.reward;
    }
    
    // Create and save the order
    const order = new Order(orderData);
    await order.save();
    
    // Update products inventory (if applicable)
    for (const item of data.items) {
      if (item.productId) {
        await Product.findByIdAndUpdate(
          item.productId,
          { $inc: { stock: -item.quantity } }
        );
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Order created successfully',
      order 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create order' },
      { status: 500 }
    );
  }
} 