import { NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';
import { getCloudFrontVideoUrl } from '@/lib/cloudfront';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Video bucket name from environment variables
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * Upload a hero video file
 * 
 * POST /api/hero-videos/upload
 */
export async function POST(request: Request) {
  try {
    // Get form data from request
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const type = formData.get('type') as 'mobile' | 'desktop' | null;
    
    // Validate file and type
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided'
      }, { status: 400 });
    }
    
    if (!type || !['mobile', 'desktop'].includes(type)) {
      return NextResponse.json({
        success: false,
        error: 'Valid type (mobile or desktop) is required'
      }, { status: 400 });
    }
    
    // Check if there's already an active video of this type
    await connectToDatabase();
    const existingVideo = await HeroVideo.findOne({ type, isActive: true });
    
    // Get file buffer
    const fileBuffer = await file.arrayBuffer();
    
    // Generate a prefix based on type
    const prefix = type === 'mobile' ? 'mobile-hero' : 'desktop-hero';
    
    // Generate a key (filename) for S3
    const fileKey = `hero-videos/${prefix}-${Date.now()}-${file.name.replace(/\s+/g, '-')}`;
    
    // Create S3 client
    const client = getS3Client();
    
    // Upload file to S3
    const uploadCommand = new PutObjectCommand({
      Bucket: videosBucketName,
      Key: fileKey,
      Body: Buffer.from(fileBuffer),
      ContentType: file.type || 'application/octet-stream'
    });
    
    await client.send(uploadCommand);
    
    // Get the CloudFront URL for the uploaded video
    const videoUrl = getCloudFrontVideoUrl(fileKey);
    
    // If there's an existing active video of this type, deactivate it
    if (existingVideo) {
      existingVideo.isActive = false;
      await existingVideo.save();
    }
    
    // Create new hero video entry in database
    const heroVideo = await HeroVideo.create({
      videoUrl,
      type,
      isActive: true,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });
    
    // Return success response
    return NextResponse.json({
      success: true,
      heroVideo,
      message: `Successfully uploaded ${type} hero video`
    });
  } catch (error: any) {
    console.error('Error uploading hero video:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to upload hero video',
      code: error.Code || error.code
    }, { status: 500 });
  }
} 