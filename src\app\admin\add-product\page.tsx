'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

type CategoryData = {
  _id: string;
  name: string;
  subcategories: Array<{_id: string; name: string}>;
};

export default function AddProductPage() {
  const router = useRouter();
  const { user } = useAdminAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [imageUploadProgress, setImageUploadProgress] = useState(0);
  const [videoUploadProgress, setVideoUploadProgress] = useState(0);
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [fetchingCategories, setFetchingCategories] = useState(true);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [productName, setProductName] = useState('');
  const [selectedShape, setSelectedShape] = useState<string | null>(null);
  const [featuredProduct, setFeaturedProduct] = useState(false);
  const [latestProduct, setLatestProduct] = useState(false);

  // Translation states
  const [descriptionEn, setDescriptionEn] = useState('');
  const [descriptionFr, setDescriptionFr] = useState('');
  const [descriptionIt, setDescriptionIt] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationTimeout, setTranslationTimeout] = useState<NodeJS.Timeout | null>(null);

  // Form refs
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const videoFileInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Available shapes
  const shapes = [
    { name: 'Oval', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><ellipse cx="12" cy="12" rx="8" ry="5" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Round', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><circle cx="12" cy="12" r="7" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Pear', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><path d="M12,5 C15,5 18,8 18,12 C18,16 15,19 12,19 C9,19 6,16 6,12 C6,10 7,7 12,5 Z" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Emerald', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="8" width="12" height="8" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Marquise', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><ellipse cx="12" cy="12" rx="8" ry="4" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Radiant', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="7" width="12" height="10" rx="2" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Square', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="7" y="7" width="10" height="10" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Heart', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><path d="M12,18 C12,18 5,14 5,9 C5,6.5 7,5 9,5 C10.5,5 12,6 12,7.5 C12,6 13.5,5 15,5 C17,5 19,6.5 19,9 C19,14 12,18 12,18 Z" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Cushion', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="6" width="12" height="12" rx="3" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Trilliant', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="12,5 19,19 5,19" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Octagonal', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="9,5 15,5 19,9 19,15 15,19 9,19 5,15 5,9" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
    { name: 'Triangular', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="12,5 19,19 5,19" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> }
  ];

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Fetch categories when component mounts
  useEffect(() => {
    fetchCategories();
  }, []);

  // Cleanup translation timeout on unmount
  useEffect(() => {
    return () => {
      if (translationTimeout) {
        clearTimeout(translationTimeout);
      }
    };
  }, [translationTimeout]);

  // Fetch categories from the API
  const fetchCategories = async () => {
    try {
      setFetchingCategories(true);
      const response = await fetch('/api/categories');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load categories. Please refresh the page.');
    } finally {
      setFetchingCategories(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      setError('You must be logged in to add a product');
      return;
    }
    
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get form data
      const formData = new FormData(e.target as HTMLFormElement);
      const productData = {
        name: formData.get('name'),
        description: {
          en: descriptionEn,
          fr: descriptionFr,
          it: descriptionIt,
        },
        price: formData.get('price'),
        category: formData.get('category'),
        weight: formData.get('weight'),
        shape: selectedShape,
        isFeatured: featuredProduct,
        isLatest: latestProduct,
      };

      // Upload image if selected
      let imageUrl = '';
      if (imageFileInputRef.current?.files?.[0]) {
        const imageFormData = new FormData();
        imageFormData.append('file', imageFileInputRef.current.files[0]);
        
        setImageUploadProgress(0);
        const imageResponse = await fetch('/api/test-s3-image/upload', {
          method: 'POST',
          body: imageFormData,
          onUploadProgress: (progressEvent) => {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            setImageUploadProgress(Math.round(progress));
          }
        });
        
        const imageResult = await imageResponse.json();
        if (!imageResult.success) {
          throw new Error('Failed to upload image');
        }
        imageUrl = imageResult.fileKey;
        setImageUploadProgress(100);
      }

      // Upload video if selected
      let videoUrl = '';
      if (videoFileInputRef.current?.files?.[0]) {
        const videoFormData = new FormData();
        videoFormData.append('file', videoFileInputRef.current.files[0]);
        
        setVideoUploadProgress(0);
        const videoResponse = await fetch('/api/test-s3-video/upload', {
          method: 'POST',
          body: videoFormData,
          onUploadProgress: (progressEvent) => {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            setVideoUploadProgress(Math.round(progress));
          }
        });
        
        const videoResult = await videoResponse.json();
        if (!videoResult.success) {
          throw new Error('Failed to upload video');
        }
        videoUrl = videoResult.fileKey;
        setVideoUploadProgress(100);
      }

      // Save product data to MongoDB
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...productData,
          imageUrl,
          videoUrl,
          userId: user._id,
          userName: `${user.firstName} ${user.lastName}`
        }),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to save product');
      }

      setSuccess(true);
      // Reset form
      (e.target as HTMLFormElement).reset();
      if (imageFileInputRef.current) imageFileInputRef.current.value = '';
      if (videoFileInputRef.current) videoFileInputRef.current.value = '';

      // Reset description states
      setDescriptionEn('');
      setDescriptionFr('');
      setDescriptionIt('');
      setSelectedCategory('');
      setSelectedShape(null);
      setProductName('');
      setImageUploadProgress(0);
      setVideoUploadProgress(0);
      
      // Redirect to products page after 2 seconds
      setTimeout(() => {
        router.push('/admin/products');
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the product');
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (subcategoryName: string, categoryId: string) => {
    setSelectedCategory(categoryId);
    setProductName(subcategoryName);
    setShowSuggestions(false);
  };

  const handleProductNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProductName(e.target.value);
    setShowSuggestions(true);
  };

  // Auto-translate function
  const translateDescription = async (englishText: string) => {
    if (!englishText.trim()) return;

    setIsTranslating(true);
    console.log('Starting translation for:', englishText.substring(0, 50) + '...');

    try {
      const response = await fetch('/api/test-translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: englishText.trim()
        }),
      });

      const data = await response.json();
      console.log('Translation response:', data);

      if (data.success && data.translations) {
        console.log('Setting translations:', {
          french: data.translations.french,
          italian: data.translations.italian
        });
        setDescriptionFr(data.translations.french || '');
        setDescriptionIt(data.translations.italian || '');
      } else {
        console.error('Translation failed:', data.error);
      }
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // Handle English description change with auto-translation
  const handleEnglishDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setDescriptionEn(value);

    // Clear existing timeout
    if (translationTimeout) {
      clearTimeout(translationTimeout);
    }

    if (value.trim()) {
      // Set new timeout for translation
      const newTimeout = setTimeout(() => {
        translateDescription(value);
      }, 1500); // 1.5 second delay
      setTranslationTimeout(newTimeout);
    } else {
      // Clear translations if English text is empty
      setDescriptionFr('');
      setDescriptionIt('');
      setTranslationTimeout(null);
    }
  };

  // Filter subcategories based on input text
  const filteredCategories = categories.map(category => ({
    ...category,
    subcategories: category.subcategories.filter(subcategory =>
      subcategory.name.toLowerCase().includes(productName.toLowerCase())
    )
  })).filter(category => category.subcategories.length > 0);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Add New Product</h1>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Product Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Product Information</h2>
            
            <div className="space-y-4">
              <div className="relative">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Product Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={productName}
                  onChange={handleProductNameChange}
                  onFocus={() => setShowSuggestions(true)}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3"
                />
                {showSuggestions && filteredCategories.length > 0 && (
                  <div 
                    ref={suggestionsRef}
                    className="absolute left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto"
                  >
                    {filteredCategories.map(category => (
                      <div key={category._id} className="p-2">
                        <div className="font-semibold text-gray-700">{category.name}</div>
                        <div className="ml-4">
                          {category.subcategories.map(subcategory => (
                            <div
                              key={subcategory._id}
                              className="py-1 px-2 hover:bg-gray-100 cursor-pointer text-gray-600"
                              onClick={() => handleCategorySelect(subcategory.name, category._id)}
                            >
                              {subcategory.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3"
                  disabled={fetchingCategories}
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {fetchingCategories && (
                  <p className="mt-1 text-sm text-gray-500">Loading categories...</p>
                )}
              </div>

              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                  Price ($)
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  required
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3"
                />
              </div>

              <div>
                <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
                  Weight (ct)
                </label>
                <input
                  type="number"
                  id="weight"
                  name="weight"
                  required
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Shape (Optional)
                </label>
                <div className="grid grid-cols-4 gap-3">
                  {shapes.map((shape) => (
                    <div 
                      key={shape.name}
                      onClick={() => setSelectedShape(selectedShape === shape.name ? null : shape.name)}
                      className={`border rounded-md p-3 flex flex-col items-center justify-center cursor-pointer transition-all ${
                        selectedShape === shape.name ? 'border-2 border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="mb-2 text-gray-700">{shape.svg}</div>
                      <div className="text-xs text-center font-medium">
                        {shape.name}
                      </div>
                    </div>
                  ))}
                </div>
                {selectedShape && (
                  <p className="mt-2 text-sm text-blue-600">
                    Selected shape: {selectedShape}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Tags for Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Tags for Section</h2>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featuredProduct"
                  name="featuredProduct"
                  checked={featuredProduct}
                  onChange={(e) => setFeaturedProduct(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="featuredProduct" className="ml-2 block text-sm text-gray-700">
                  Featured Products Section
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="latestProduct"
                  name="latestProduct"
                  checked={latestProduct}
                  onChange={(e) => setLatestProduct(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="latestProduct" className="ml-2 block text-sm text-gray-700">
                  Latest Products Section
                </label>
              </div>
            </div>
          </div>

          {/* Media Upload */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Media Upload</h2>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="image" className="block text-sm font-medium text-gray-700">
                  Product Image
                </label>
                <input
                  type="file"
                  id="image"
                  ref={imageFileInputRef}
                  accept="image/*"
                  className="mt-1 block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-semibold
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100"
                />
              </div>

              <div>
                <label htmlFor="video" className="block text-sm font-medium text-gray-700">
                  Product Video
                </label>
                <input
                  type="file"
                  id="video"
                  ref={videoFileInputRef}
                  accept="video/*"
                  className="mt-1 block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-semibold
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100"
                />
              </div>
            </div>
          </div>

          {/* Product Descriptions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Product Descriptions</h2>
            
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label htmlFor="description_en" className="block text-sm font-medium text-gray-700">
                    Description (English) {isTranslating && <span className="text-blue-500 text-xs">(Translating...)</span>}
                  </label>
                  <button
                    type="button"
                    onClick={() => descriptionEn.trim() && translateDescription(descriptionEn)}
                    disabled={!descriptionEn.trim() || isTranslating}
                    className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isTranslating ? 'Translating...' : 'Translate Now'}
                  </button>
                </div>
                <textarea
                  id="description_en"
                  name="description_en"
                  value={descriptionEn}
                  onChange={handleEnglishDescriptionChange}
                  required
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3"
                  placeholder="Enter English description here. French and Italian will auto-translate after 1.5 seconds..."
                />
              </div>

              <div>
                <label htmlFor="description_fr" className="block text-sm font-medium text-gray-700">
                  Description (French) <span className="text-gray-500 text-xs">(Auto-filled)</span>
                </label>
                <textarea
                  id="description_fr"
                  name="description_fr"
                  value={descriptionFr}
                  onChange={(e) => setDescriptionFr(e.target.value)}
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3 bg-blue-50"
                  placeholder="French translation will appear here automatically..."
                />
              </div>

              <div>
                <label htmlFor="description_it" className="block text-sm font-medium text-gray-700">
                  Description (Italian) <span className="text-gray-500 text-xs">(Auto-filled)</span>
                </label>
                <textarea
                  id="description_it"
                  name="description_it"
                  value={descriptionIt}
                  onChange={(e) => setDescriptionIt(e.target.value)}
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-3 bg-blue-50"
                  placeholder="Italian translation will appear here automatically..."
                />
              </div>
            </div>
          </div>

          {/* Upload Progress */}
          {(imageUploadProgress > 0 || videoUploadProgress > 0) && (
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Upload Progress</h2>
              <div className="space-y-4">
                {imageUploadProgress > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image Upload Progress
                    </label>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                        style={{ width: `${imageUploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-right">{imageUploadProgress}%</p>
                  </div>
                )}

                {videoUploadProgress > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Video Upload Progress
                    </label>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                        style={{ width: `${videoUploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-right">{videoUploadProgress}%</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.push('/admin')}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || fetchingCategories}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Saving...' : 'Save Product'}
            </button>
          </div>
        </form>

        {/* Status Messages */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mt-4 p-4 bg-green-50 text-green-700 rounded-md">
            Product saved successfully! Redirecting...
          </div>
        )}
      </div>
    </div>
  );
} 