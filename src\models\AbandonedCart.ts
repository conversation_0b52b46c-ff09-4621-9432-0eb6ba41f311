import mongoose from 'mongoose';

// Define schema for cart items
const CartItemSchema = new mongoose.Schema({
  productId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  imageUrl: {
    type: String
  }
});

// Define main abandoned cart schema
const AbandonedCartSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  customerInfo: {
    email: {
      type: String,
      sparse: true, // Allow null/undefined while maintaining index
      index: true
    },
    name: {
      type: String
    },
    phone: {
      type: String
    }
  },
  items: {
    type: [CartItemSchema],
    required: true,
    validate: {
      validator: function(items: any[]) {
        return items.length > 0;
      },
      message: 'Cart must contain at least one item'
    }
  },
  subtotal: {
    type: Number,
    required: true
  },
  lastActivity: {
    type: Date,
    default: Date.now,
    index: true
  },
  recoveryEmailSent: {
    type: Boolean,
    default: false
  },
  recoveryEmailDate: {
    type: Date
  },
  recovered: {
    type: Boolean,
    default: false
  },
  convertedToOrderId: {
    type: String
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  referrer: {
    type: String
  }
}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

// Index for finding recent abandoned carts
AbandonedCartSchema.index({ createdAt: -1 });
AbandonedCartSchema.index({ recovered: 1, createdAt: -1 });

// Check if model exists before creating
const AbandonedCart = mongoose.models.AbandonedCart || mongoose.model('AbandonedCart', AbandonedCartSchema);

export default AbandonedCart; 