import React, { useEffect } from 'react';

// Declare global THREE type to resolve TypeScript errors
declare global {
  interface Window {
    THREE: {
      Scene: new () => any;
      PerspectiveCamera: new (fov: number, aspect: number, near: number, far: number) => any;
      WebGLRenderer: new (options?: {
        canvas?: HTMLCanvasElement;
        antialias?: boolean;
        alpha?: boolean;
      }) => any;
      TorusKnotGeometry: new (
        radius?: number, 
        tube?: number, 
        tubularSegments?: number, 
        radialSegments?: number, 
        p?: number, 
        q?: number
      ) => any;
      MeshStandardMaterial: new (options?: {
        color?: number | string;
        metalness?: number;
        roughness?: number;
        emissive?: number;
        emissiveIntensity?: number;
        side?: number;
      }) => any;
      Mesh: new (geometry: any, material: any) => any;
      AmbientLight: new (color?: number, intensity?: number) => any;
      DirectionalLight: new (color?: number, intensity?: number) => any;
      PointLight: new (color?: number, intensity?: number, distance?: number) => any;
      Color: new (color?: number | string) => {
        clone(): any;
        getHSL(target: { h: number; s: number; l: number }): void;
        setHSL(h: number, s: number, l: number): any;
      };
      Vector3: new (x?: number, y?: number, z?: number) => {
        normalize(): any;
      };
      DoubleSide: number;
    };
  }
}

export function initFloatingCoins() {
  type CoinMesh = {
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    userData: {
      rotationSpeedX: number;
      rotationSpeedY: number;
      floatSpeed: number;
      floatOffset: number;
    };
  } & any; // This allows us to work with the Three.js mesh without knowing all properties

  const { THREE } = window;

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({ 
    canvas: document.getElementById('floating-coins-canvas') as HTMLCanvasElement, 
    antialias: true,
    alpha: true 
  });
  renderer.setSize(window.innerWidth, window.innerHeight);

  // Lights
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(5, 5, 5);
  scene.add(directionalLight);

  const coins: CoinMesh[] = [];

  function createCoin() {
    const geometry = new THREE.TorusKnotGeometry(0.3, 0.1, 64, 8, 2, 3);
    const material = new THREE.MeshStandardMaterial({ 
      color: 0xffd700, 
      metalness: 0.7, 
      roughness: 0.3,
      side: THREE.DoubleSide
    });
    const coin = new THREE.Mesh(geometry, material);

    // Random position
    coin.position.x = (Math.random() - 0.5) * 10;
    coin.position.y = (Math.random() - 0.5) * 10;
    coin.position.z = -5 - Math.random() * 5;

    // Custom user data for animation
    coin.userData = {
      rotationSpeedX: Math.random() * 0.02,
      rotationSpeedY: Math.random() * 0.02,
      floatSpeed: 0.01 + Math.random() * 0.03,
      floatOffset: Math.random() * Math.PI * 2
    };

    scene.add(coin);
    coins.push(coin as CoinMesh);
  }

  // Create multiple coins
  for (let i = 0; i < 10; i++) {
    createCoin();
  }

  camera.position.z = 5;

  function animate() {
    requestAnimationFrame(animate);

    coins.forEach((coin) => {
      // Rotation
      coin.rotation.x += coin.userData.rotationSpeedX;
      coin.rotation.y += coin.userData.rotationSpeedY;

      // Floating motion
      const time = performance.now() * 0.001;
      coin.position.y = Math.sin(time * coin.userData.floatSpeed + coin.userData.floatOffset) * 0.5;
    });

    renderer.render(scene, camera);
  }

  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }

  window.addEventListener('resize', onWindowResize);

  animate();

  // Cleanup function
  return () => {
    window.removeEventListener('resize', onWindowResize);
    scene.children.forEach((child) => {
      if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        (child.material as THREE.Material).dispose();
      }
    });
    renderer.dispose();
  };
} 