'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Mail, MessageSquare, Calendar, Clock, RefreshCcw, Search, AlertTriangle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Campaign {
  _id: string;
  type: 'email' | 'sms';
  subject?: string;
  content: string;
  recipientGroup: string;
  recipients: string[];
  scheduledAt?: string;
  sentAt?: string;
  status: 'scheduled' | 'sent' | 'failed' | 'cancelled';
  stats: {
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    complained: number;
  };
  createdAt: string;
  updatedAt: string;
}

export default function CampaignHistoryPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    fetchCampaigns();
  }, []);

  useEffect(() => {
    // Apply filters whenever they change
    applyFilters();
  }, [campaigns, searchTerm, filterType, filterStatus]);

  const fetchCampaigns = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/email-campaign/history');
      
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns');
      }
      
      const data = await response.json();
      setCampaigns(data);
    } catch (err) {
      console.error('Error fetching campaigns:', err);
      setError('Failed to load campaign history. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...campaigns];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(campaign => 
        (campaign.subject && campaign.subject.toLowerCase().includes(searchTerm.toLowerCase())) ||
        campaign.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.recipientGroup.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(campaign => campaign.type === filterType);
    }
    
    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(campaign => campaign.status === filterStatus);
    }
    
    setFilteredCampaigns(filtered);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Sent</span>;
      case 'scheduled':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Scheduled</span>;
      case 'failed':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Failed</span>;
      case 'cancelled':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Cancelled</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Unknown</span>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-5 w-5 text-blue-500" />;
      case 'sms':
        return <MessageSquare className="h-5 w-5 text-green-500" />;
      default:
        return null;
    }
  };

  const getRecipientGroupLabel = (group: string) => {
    switch (group) {
      case 'all':
        return 'All Customers';
      case 'new':
        return 'New Customers';
      case 'active':
        return 'Active Customers';
      case 'inactive':
        return 'Inactive Customers';
      case 'vip':
        return 'VIP Customers';
      case 'manual':
        return 'Manual List';
      default:
        return group;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return `${date.toLocaleDateString()} at ${date.toLocaleTimeString()}`;
  };

  const handleRefresh = () => {
    fetchCampaigns();
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/email-sms-marketing" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Campaign History</h1>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md flex items-start">
          <AlertTriangle className="mr-2 h-5 w-5 flex-shrink-0" />
          <p>{error}</p>
        </div>
      )}

      <div className="mb-6 flex flex-wrap gap-4 items-center">
        <div className="relative flex-1 min-w-[250px]">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search campaigns..."
            className="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex items-center">
          <label htmlFor="typeFilter" className="text-sm font-medium text-gray-700 mr-2">
            Type
          </label>
          <select
            id="typeFilter"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="border border-gray-300 rounded-md p-2 w-36"
          >
            <option value="all">All Types</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
          </select>
        </div>

        <div className="flex items-center">
          <label htmlFor="statusFilter" className="text-sm font-medium text-gray-700 mr-2">
            Status
          </label>
          <select
            id="statusFilter"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md p-2 w-36"
          >
            <option value="all">All Statuses</option>
            <option value="sent">Sent</option>
            <option value="scheduled">Scheduled</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <button
          onClick={handleRefresh}
          className="ml-auto flex items-center p-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100"
        >
          <RefreshCcw className="h-5 w-5 mr-2" />
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center my-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredCampaigns.length === 0 ? (
        <div className="text-center p-8 border rounded-lg bg-gray-50">
          <p className="text-gray-500">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all'
              ? 'No campaigns match your search criteria.'
              : 'No campaigns found. Create your first campaign to see it here.'}
          </p>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Campaign
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recipients
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stats
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCampaigns.map((campaign) => (
                  <tr key={campaign._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex flex-col">
                        <span className="font-medium text-gray-900">
                          {campaign.type === 'email' ? campaign.subject : 'SMS Campaign'}
                        </span>
                        <span className="text-sm text-gray-500 truncate max-w-xs">
                          {campaign.content.substring(0, 50)}
                          {campaign.content.length > 50 ? '...' : ''}
                        </span>
                        <span className="text-xs text-gray-400 mt-1">
                          Created {formatDistanceToNow(new Date(campaign.createdAt), { addSuffix: true })}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTypeIcon(campaign.type)}
                        <span className="ml-2 capitalize">{campaign.type}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span>{getRecipientGroupLabel(campaign.recipientGroup)}</span>
                        <span className="text-sm text-gray-500">{campaign.recipients.length} recipients</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(campaign.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        {campaign.status === 'scheduled' ? (
                          <>
                            <div className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                              <span>Scheduled for:</span>
                            </div>
                            <span className="text-sm">{formatDate(campaign.scheduledAt)}</span>
                          </>
                        ) : (
                          <>
                            <div className="flex items-center text-sm">
                              <Clock className="h-4 w-4 mr-1 text-gray-400" />
                              <span>Sent at:</span>
                            </div>
                            <span className="text-sm">{formatDate(campaign.sentAt)}</span>
                          </>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {campaign.status === 'sent' ? (
                        <div className="flex flex-col text-sm">
                          <span>Delivered: {campaign.stats.delivered}</span>
                          <span>Opened: {campaign.stats.opened}</span>
                          <span>Clicked: {campaign.stats.clicked}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">
                          {campaign.status === 'scheduled' ? 'Pending' : 'N/A'}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
} 