import mongoose, { Schema } from 'mongoose';

// Define message schema for ticket updates
const TicketMessageSchema = new Schema({
  sender: { 
    type: String, 
    required: true, 
    enum: ['customer', 'admin'] 
  },
  message: { 
    type: String, 
    required: true 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  },
  attachmentUrl: { 
    type: String 
  }
});

// Define the main Support Ticket schema
const SupportTicketSchema = new Schema(
  {
    ticketNumber: {
      type: String,
      required: true,
      unique: true
    },
    customer: {
      email: { type: String, required: true, lowercase: true },
      name: { type: String, required: true },
      phone: { type: String }
    },
    description: {
      type: String,
      required: true
    },
    category: { 
      type: String, 
      required: true,
      enum: ['order', 'product', 'shipping', 'payment', 'website', 'account', 'other']
    },
    priority: { 
      type: String, 
      required: true,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    },
    status: { 
      type: String, 
      required: true,
      enum: ['open', 'in-progress', 'waiting-for-customer', 'resolved', 'closed'],
      default: 'open'
    },
    orderId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Order' 
    },
    assignedTo: { 
      type: String 
    },
    messages: {
      type: [TicketMessageSchema],
      default: []
    },
    internalNotes: { 
      type: String 
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  { timestamps: true }
);

// Create a static method to generate ticket numbers
SupportTicketSchema.statics.generateTicketNumber = async function() {
  const count = await this.countDocuments();
  const date = new Date();
  const year = date.getFullYear().toString().substr(2, 2); // Get last 2 digits of year
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const ticketNumber = `TICKET-${year}${month}-${String(count + 1).padStart(4, '0')}`;
  return ticketNumber;
};

// Create or get the model (prevents model overwrite during hot reloading)
export const SupportTicket = mongoose.models.SupportTicket || mongoose.model('SupportTicket', SupportTicketSchema); 