'use client';

import { useRef } from 'react';
import { 
  PDFViewer, 
  PDFDownloadLink 
} from '@react-pdf/renderer';
import { Download, Printer } from 'lucide-react';
import { InvoicePDF } from './InvoicePDF';

// Define Order types
type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

type OrderItem = {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  imageUrl?: string;
};

type Order = {
  _id: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
  };
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: 'paid' | 'unpaid' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
};

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// HTML version of invoice for display
const InvoiceHTML = ({ order }: { order: Order }) => (
  <div className="invoice-container p-8 bg-white">
    <div className="header border-b pb-6 mb-6">
      <div className="flex justify-between items-start mb-6">
        <h1 className="text-3xl font-bold">INVOICE</h1>
        <div className="text-right text-sm text-gray-600">
          <p className="font-semibold">Afghan International Gems</p>
          <p>123 Gem Street, Kabul, Afghanistan</p>
          <p><EMAIL></p>
          <p>+93 123 456 7890</p>
        </div>
      </div>
      <div className="flex justify-between">
        <div>
          <p className="font-semibold text-sm">Invoice Number:</p>
          <p>{order.orderNumber}</p>
        </div>
        <div className="text-right">
          <p className="font-semibold text-sm">Date:</p>
          <p>{formatDate(order.createdAt)}</p>
        </div>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-8 mb-8">
      <div>
        <h2 className="font-semibold text-sm mb-2">Bill To:</h2>
        <p>{order.customer.name}</p>
        <p>{order.customer.email}</p>
        {order.customer.phone && <p>{order.customer.phone}</p>}
      </div>
      <div>
        <h2 className="font-semibold text-sm mb-2">Ship To:</h2>
        <p>{order.shippingAddress.street}</p>
        <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}</p>
        <p>{order.shippingAddress.country}</p>
      </div>
    </div>

    <div className="mb-8">
      <h2 className="font-semibold text-sm mb-2">Order Details</h2>
      <table className="w-full mb-4">
        <thead>
          <tr className="border-b text-sm text-gray-700">
            <th className="py-2 text-left">Product</th>
            <th className="py-2 text-right">Price</th>
            <th className="py-2 text-center">Qty</th>
            <th className="py-2 text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          {order.items.map((item, index) => (
            <tr key={index} className="border-b">
              <td className="py-3">{item.name}</td>
              <td className="py-3 text-right">{formatCurrency(item.price)}</td>
              <td className="py-3 text-center">{item.quantity}</td>
              <td className="py-3 text-right">{formatCurrency(item.price * item.quantity)}</td>
            </tr>
          ))}
        </tbody>
      </table>
      
      <div className="flex justify-end">
        <div className="w-1/3">
          <div className="flex justify-between py-2">
            <span className="text-sm text-gray-600">Subtotal:</span>
            <span>{formatCurrency(order.subtotal)}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-sm text-gray-600">Shipping:</span>
            <span>{formatCurrency(order.shipping)}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-sm text-gray-600">Tax:</span>
            <span>{formatCurrency(order.tax)}</span>
          </div>
          <div className="flex justify-between py-2 font-bold border-t">
            <span>Total:</span>
            <span>{formatCurrency(order.total)}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div className="mb-8">
      <h2 className="font-semibold text-sm mb-2">Payment Information</h2>
      <div className="grid grid-cols-2 border-b py-2">
        <div>Payment Method:</div>
        <div>{order.paymentMethod}</div>
      </div>
      <div className="grid grid-cols-2 border-b py-2">
        <div>Payment Status:</div>
        <div>{order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}</div>
      </div>
    </div>
    
    <div className="text-center text-sm text-gray-600 border-t pt-8 mt-8">
      <p>Thank you for your business!</p>
      <p>If you have any questions about this invoice, please contact our customer service.</p>
    </div>
  </div>
);

// The main component that handles view, print and download
export default function InvoiceView({ order }: { order: Order }) {
  const printRef = useRef<HTMLDivElement>(null);
  
  // Handle Print functionality
  const handlePrint = () => {
    if (printRef.current) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Invoice - ${order.orderNumber}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .invoice-container { max-width: 800px; margin: 0 auto; }
                .header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
                .flex { display: flex; }
                .justify-between { justify-content: space-between; }
                .items-start { align-items: flex-start; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                .text-3xl { font-size: 1.875rem; }
                .text-sm { font-size: 0.875rem; }
                .font-bold { font-weight: bold; }
                .font-semibold { font-weight: 600; }
                .text-gray-600 { color: #666; }
                .mb-2 { margin-bottom: 0.5rem; }
                .mb-4 { margin-bottom: 1rem; }
                .mb-6 { margin-bottom: 1.5rem; }
                .mb-8 { margin-bottom: 2rem; }
                .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
                .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
                .grid { display: grid; }
                .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                .gap-8 { gap: 2rem; }
                table { width: 100%; border-collapse: collapse; }
                th { text-align: left; font-weight: 600; }
                .border-b { border-bottom: 1px solid #eee; }
                .border-t { border-top: 1px solid #eee; }
                .w-full { width: 100%; }
                .w-1/3 { width: 33.333333%; }
                @media print { body { print-color-adjust: exact; -webkit-print-color-adjust: exact; } }
              </style>
            </head>
            <body>
              ${printRef.current.innerHTML}
              <script>
                window.onload = function() { window.print(); window.close(); }
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Action buttons */}
      <div className="flex justify-end space-x-4">
        <button 
          onClick={handlePrint}
          data-print-button
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Printer className="mr-2" size={16} />
          Print Invoice
        </button>
        <PDFDownloadLink 
          document={<InvoicePDF order={order} />} 
          fileName={`invoice-${order.orderNumber}.pdf`}
          data-download-button
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
        >
          {({ loading }: { loading: boolean }) => (
            <>
              <Download className="mr-2" size={16} />
              {loading ? 'Generating PDF...' : 'Download PDF'}
            </>
          )}
        </PDFDownloadLink>
      </div>
      
      {/* Preview area for printing */}
      <div ref={printRef} className="bg-white shadow-md">
        <InvoiceHTML order={order} />
      </div>
      
      {/* PDF viewer */}
      <div className="border rounded-md overflow-hidden" style={{ height: '600px' }}>
        <PDFViewer width="100%" height="100%" className="border-0">
          <InvoicePDF order={order} />
        </PDFViewer>
      </div>
    </div>
  );
} 