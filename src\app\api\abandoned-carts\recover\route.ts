import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import AbandonedCart from '@/models/AbandonedCart';

/**
 * Mark an abandoned cart as recovered
 * This endpoint is called after successful checkout
 * 
 * POST /api/abandoned-carts/recover
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const data = await request.json();
    
    // Validate required fields
    if (!data.sessionId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required field: sessionId'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Find the cart by session ID
    const cart = await AbandonedCart.findOne({ sessionId: data.sessionId });
    
    if (!cart) {
      return NextResponse.json({
        success: false,
        error: 'Cart not found'
      }, { status: 404 });
    }
    
    // Mark as recovered and include order ID if provided
    cart.recovered = true;
    if (data.orderId) {
      cart.convertedToOrderId = data.orderId;
    }
    
    await cart.save();
    
    return NextResponse.json({
      success: true,
      message: 'Cart marked as recovered successfully'
    });
  } catch (error: any) {
    console.error('Error marking cart as recovered:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to mark cart as recovered'
    }, { status: 500 });
  }
} 