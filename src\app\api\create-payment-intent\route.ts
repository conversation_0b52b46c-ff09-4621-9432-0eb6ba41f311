import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '');

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { items } = body;
    
    console.log('Creating payment intent for items:', JSON.stringify(items));
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Items are required.' },
        { status: 400 }
      );
    }
    
    // Calculate total amount
    const amount = items.reduce(
      (total, item) => total + (item.product.price * item.quantity),
      0
    );
    
    // Add shipping cost
    const totalAmount = amount + 15.00; // $15 shipping
    
    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Convert to cents
      currency: 'usd',
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        order_items: JSON.stringify(items.map(item => ({
          product_id: item.product._id,
          quantity: item.quantity,
          name: item.product.name,
        }))),
      },
    });
    
    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
    });
  } catch (error: any) {
    console.error('Error creating payment intent:', error);
    
    return NextResponse.json(
      { 
        error: 'An error occurred creating the payment intent.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
} 