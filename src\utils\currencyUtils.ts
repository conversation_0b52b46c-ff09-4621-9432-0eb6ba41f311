/**
 * Utility functions for currency conversion and formatting
 */

// Default exchange rates (fallback if API calls fail)
const DEFAULT_RATES = {
  USD: 1,
  EUR: 0.93
};

// Cache exchange rates with expiration
let cachedRates = { ...DEFAULT_RATES };
let lastFetchTime = 0;
const CACHE_EXPIRY = 60 * 60 * 1000; // 1 hour

/**
 * Fetch current exchange rates
 */
export async function getExchangeRates() {
  const now = Date.now();
  
  // Return cached rates if they're still fresh
  if (now - lastFetchTime < CACHE_EXPIRY) {
    return cachedRates;
  }
  
  try {
    // Fetch fresh rates
    const response = await fetch('https://open.er-api.com/v6/latest/USD');
    const data = await response.json();
    
    if (data && data.rates) {
      cachedRates = {
        USD: 1,
        EUR: data.rates.EUR || DEFAULT_RATES.EUR
      };
      
      lastFetchTime = now;
      return cachedRates;
    }
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
  }
  
  // Return default rates if fetch fails
  return DEFAULT_RATES;
}

/**
 * Convert price from USD to target currency
 */
export async function convertPrice(priceInUSD: number, targetCurrency: 'USD' | 'EUR') {
  if (targetCurrency === 'USD') {
    return priceInUSD;
  }
  
  const rates = await getExchangeRates();
  const convertedPrice = priceInUSD * rates[targetCurrency];
  
  return Number(convertedPrice.toFixed(2));
}

/**
 * Format price with currency symbol
 */
export function formatPrice(price: number, currency: 'USD' | 'EUR') {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`;
  } else {
    return `€${price.toFixed(2)}`;
  }
} 