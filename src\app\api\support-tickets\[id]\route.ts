import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import dbConnect from '@/lib/dbConnect';
import { SupportTicket } from '@/models/SupportTicket';

/**
 * GET /api/support-tickets/[id]
 * Retrieve a specific support ticket by ID
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const id = params.id;
    
    // Validate if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid ticket ID format' },
        { status: 400 }
      );
    }
    
    // Find the ticket
    const ticket = await SupportTicket.findById(id).lean();
    
    // Check if ticket exists
    if (!ticket) {
      return NextResponse.json(
        { error: 'Support ticket not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(ticket);
  } catch (error) {
    console.error('Error fetching support ticket:', error);
    return NextResponse.json(
      { error: 'Failed to fetch support ticket' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/support-tickets/[id]
 * Update a support ticket (status, priority, assignee, etc.)
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const id = params.id;
    
    // Validate if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid ticket ID format' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const updates = await request.json();
    
    // Set last updated timestamp
    updates.lastUpdated = new Date();
    
    // Find and update the ticket
    const updatedTicket = await SupportTicket.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );
    
    // Check if ticket exists
    if (!updatedTicket) {
      return NextResponse.json(
        { error: 'Support ticket not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error('Error updating support ticket:', error);
    return NextResponse.json(
      { error: 'Failed to update support ticket' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/support-tickets/[id]
 * Delete a support ticket
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await dbConnect();
    
    const id = params.id;
    
    // Validate if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid ticket ID format' },
        { status: 400 }
      );
    }
    
    // Find and delete the ticket
    const deletedTicket = await SupportTicket.findByIdAndDelete(id);
    
    // Check if ticket exists
    if (!deletedTicket) {
      return NextResponse.json(
        { error: 'Support ticket not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true, message: 'Support ticket deleted successfully' });
  } catch (error) {
    console.error('Error deleting support ticket:', error);
    return NextResponse.json(
      { error: 'Failed to delete support ticket' },
      { status: 500 }
    );
  }
} 