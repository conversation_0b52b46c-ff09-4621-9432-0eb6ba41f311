import { useState, useEffect } from 'react';
import { 
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { markCartAsRecovered } from '@/services/cartSyncService';

type StripePaymentElementProps = {
  clientSecret: string;
  onSuccess: (paymentIntentId: string) => void;
  onError: (error: string) => void;
}

export default function StripePaymentElement({ 
  clientSecret, 
  onSuccess, 
  onError 
}: StripePaymentElementProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    if (!stripe) {
      return;
    }

    // Check for payment intent completion on page load
    // This helps if the user is returning after a redirect
    const clientSecret = new URLSearchParams(window.location.search).get(
      'payment_intent_client_secret'
    );

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      if (!paymentIntent) return;
      
      switch (paymentIntent.status) {
        case 'succeeded':
          setMessage('Payment succeeded!');
          // Mark the abandoned cart as recovered
          markCartAsRecovered(paymentIntent.id);
          onSuccess(paymentIntent.id);
          break;
        case 'processing':
          setMessage('Your payment is processing.');
          break;
        case 'requires_payment_method':
          setMessage('Your payment was not successful, please try again.');
          break;
        default:
          setMessage('Something went wrong.');
          break;
      }
    });
  }, [stripe, onSuccess]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      return;
    }

    setIsLoading(true);
    setMessage(null);

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/checkout/success`,
      },
      redirect: 'if_required',
    });

    if (error) {
      // Show error to your customer
      setMessage(error.message || 'An unexpected error occurred.');
      onError(error.message || 'Payment failed');
    } else if (paymentIntent && paymentIntent.status === 'succeeded') {
      // The payment succeeded
      setMessage('Payment succeeded!');
      // Mark the abandoned cart as recovered
      await markCartAsRecovered(paymentIntent.id);
      onSuccess(paymentIntent.id);
    } else {
      setMessage('An unexpected error occurred.');
      onError('Payment failed');
    }

    setIsLoading(false);
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit}>
      <PaymentElement id="payment-element" />
      
      {message && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md text-blue-800">
          {message}
        </div>
      )}
      
      <button 
        disabled={isLoading || !stripe || !elements} 
        id="submit" 
        className={`mt-6 w-full py-3 px-4 rounded-md text-white font-medium 
          ${isLoading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'} 
          transition-colors`}
      >
        <span id="button-text">
          {isLoading ? <div className="spinner">Processing...</div> : "Pay now"}
        </span>
      </button>
    </form>
  );
} 