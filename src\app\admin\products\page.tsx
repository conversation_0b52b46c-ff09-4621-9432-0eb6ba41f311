'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { getCloudFrontVideoUrl, getCloudFrontImageUrl } from '@/lib/cloudfront';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

interface Product {
  _id: string;
  name: string;
  description: {
    en: string;
    fr?: string;
    it?: string;
  };
  price: number;
  category: {
    _id: string;
    name: string;
  };
  imageUrl: string;
  videoUrl: string;
  createdAt: string;
  weight: number;
}

const VideoPlayer = ({ src, className }: { src: string; className?: string }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div className="relative group">
      <video
        ref={videoRef}
        src={src}
        className={`w-full h-full object-cover ${className}`}
        loop
        muted
        playsInline
      />
      <button
        onClick={togglePlay}
        className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          isPlaying ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
        }`}
      >
        {!isPlaying && (
          <div className="w-16 h-16 flex items-center justify-center">
            <div className="w-0 h-0 border-t-[16px] border-t-transparent border-l-[28px] border-l-white border-b-[16px] border-b-transparent ml-2"></div>
          </div>
        )}
      </button>
    </div>
  );
};

export default function ProductsPage() {
  const router = useRouter();
  const { user } = useAdminAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch products');
      }
      
      setProducts(data.products);
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching products');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (productId: string, imageUrl: string, videoUrl: string) => {
    if (!confirm('Are you sure you want to delete this product?')) {
      return;
    }

    if (!user) {
      setError('You must be logged in to delete a product');
      return;
    }

    setDeletingId(productId);
    try {
      // Delete product from MongoDB and S3 files
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          videoUrl,
          // Include user information for logging
          userId: user._id,
          userName: `${user.firstName} ${user.lastName}`
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to delete product');
      }

      // Remove the product from the local state
      setProducts(products.filter(product => product._id !== productId));
    } catch (err: any) {
      setError(err.message || 'An error occurred while deleting the product');
    } finally {
      setDeletingId(null);
    }
  };

  const handleEdit = (productId: string) => {
    router.push(`/admin/edit-product/${productId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Products List</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Back to Admin
        </button>
      </div>

      {error && (
        <div className="mb-8 p-4 bg-red-50 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {products.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500">No products found. Add your first product!</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <div key={product._id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* Image */}
              {product.imageUrl && (
                <div className="aspect-w-16 aspect-h-9 h-[300px]">
                  <img
                    src={getCloudFrontImageUrl(product.imageUrl)}
                    alt={product.name}
                    className="object-cover w-full h-full object-center"
                  />
                </div>
              )}

              {/* Product Info */}
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h2 className="text-xl font-semibold">{product.name}</h2>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEdit(product._id)}
                      className="p-2 text-blue-500 hover:text-blue-700"
                      title="Edit product"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDelete(product._id, product.imageUrl, product.videoUrl)}
                      disabled={deletingId === product._id}
                      className="p-2 text-red-500 hover:text-red-700 disabled:opacity-50"
                      title="Delete product"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
                <p className="text-gray-600 mb-2">Weight: {product.weight} ct</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-lg font-bold text-blue-600">
                    {formatPrice(product.price)}
                  </span>
                  <span className="text-sm text-gray-500">
                    {product.category?.name || product.category}
                  </span>
                </div>

                {/* Video */}
                {product.videoUrl && (
                  <div className="relative w-full h-48">
                    <VideoPlayer
                      src={getCloudFrontVideoUrl(product.videoUrl)}
                      className="rounded-t-lg"
                    />
                  </div>
                )}

                <p className="text-gray-600 mt-20 mb-2">{product.description.en}</p>

                <div className="mt-2 text-sm text-gray-500">
                  Added on {formatDate(product.createdAt)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 