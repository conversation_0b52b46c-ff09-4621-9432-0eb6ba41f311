import Image from 'next/image';
import {
  ChevronDown,
  ChevronUp,
  Search as SearchIcon, // Renamed to avoid conflict with Search input component if any
  X as XIcon,
} from 'lucide-react'; // Assuming lucide-react is used in the project
import Range from 'rc-slider';
import 'rc-slider/assets/index.css';
import './NewSidebar.css'; // Import custom styles for the slider
import { useLanguage } from '@/contexts/LanguageContext';

// Types from shop/page.tsx - ideally these would be in a shared types file
type Subcategory = {
  _id: string;
  name: string;
};

type Category = {
  _id: string;
  name: string;
  subcategories: Subcategory[];
};

interface NewSidebarProps {
  categories: Category[];
  selectedCategories: string[]; // Changed from single to array
  selectedSubcategories: string[]; // Changed from single to array
  expandedCategories: Record<string, boolean>;
  priceRange: [number, number];
  searchQuery: string;
  onCategorySelect: (categoryId: string) => void;
  onSubcategorySelect: (subcategoryId: string) => void;
  onToggleCategory: (categoryId: string) => void;
  onPriceChange: (minPrice: number, maxPrice: number) => void;
  onSearchChange: (query: string) => void;
  onResetFilters: () => void;
  minProductPrice?: number; // Optional: For setting slider minimum
  maxProductPrice?: number; // Optional: For setting slider maximum
  subcategoryCounts?: Record<string, number>; // Optional: Product counts for each subcategory
}

const NewSidebar: React.FC<NewSidebarProps> = ({
  categories,
  selectedCategories, // Changed from selectedCategory
  selectedSubcategories, // Changed from selectedSubcategory
  expandedCategories,
  priceRange,
  searchQuery,
  onCategorySelect,
  onSubcategorySelect,
  onToggleCategory,
  onPriceChange,
  onSearchChange,
  onResetFilters,
  minProductPrice = 0,       // Default min price for slider
  maxProductPrice = 10000,   // Default max price for slider
  subcategoryCounts = {},    // Default empty object for product counts
}) => {
  const { translations, translateCategoryName, translateSubcategoryName } = useLanguage();

  const handleSliderChange = (values: number | number[]) => {
    if (Array.isArray(values)) {
      onPriceChange(values[0], values[1]);
    }
  };

  return (
    <div className="w-full bg-white rounded-lg shadow p-4 sm:p-6 mb-6 sticky top-24">
      {/* Search Input from original sidebar */}
      <div className="mb-6">
        <h3 className="text-gray-800 font-semibold text-base mb-2">{translations.search_products}</h3>
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder={translations.search_placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
          <SearchIcon size={18} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {/* Categories */}
      {categories.map((category, index) => (
        <div key={category._id} className={`relative ${index > 0 ? 'mt-4 pt-4 border-t border-gray-200' : ''}`}>
          <div className="w-full h-auto flex items-center justify-between mb-2">
            <button
              onClick={() => onCategorySelect(category._id)} 
              className={`text-gray-800 font-semibold text-base text-left flex-1 py-1 flex items-center ${selectedCategories.includes(category._id) ? 'text-blue-600' : ''}`}
            >
              <div className="flex-shrink-0 w-7 h-6 relative mr-2 flex items-center justify-center">
                {selectedCategories.includes(category._id) ? (
                  <Image
                    className="check-icon"
                    width={18} 
                    height={18} 
                    src="/images/check0.svg"
                    alt="Selected"
                  />
                ) : (
                  <div className="rounded-sm border border-gray-400 w-4 h-4 bg-white"></div>
                )}
              </div>
              {translateCategoryName(category.name)}
            </button>
            {category.subcategories && category.subcategories.length > 0 && (
              <button onClick={() => onToggleCategory(category._id)} className="p-1 text-gray-500 hover:text-gray-700">
                {expandedCategories[category._id] ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
              </button>
            )}
          </div>

          {expandedCategories[category._id] && category.subcategories && category.subcategories.length > 0 && (
            <div className="pl-2 mt-1 space-y-1 max-h-[300px] overflow-y-auto custom-scrollbar">
              {category.subcategories.map(subcategory => (
                <div key={subcategory._id} className="flex items-center py-1 pr-2 mb-px">
                  <button
                    onClick={() => onSubcategorySelect(subcategory._id)}
                    className="flex items-center w-full text-left"
                  >
                    <div className="flex-shrink-0 w-7 h-6 relative mr-2 flex items-center justify-center">
                      {selectedSubcategories.includes(subcategory._id) ? (
                        <Image
                          className="check-icon"
                          width={18} 
                          height={18} 
                          src="/images/check0.svg"
                          alt="Selected"
                        />
                      ) : (
                        <div className="rounded-sm border border-gray-400 w-4 h-4 bg-white"></div>
                      )}
                    </div>
                    <span
                      className={`text-gray-700 font-inter text-sm ${selectedSubcategories.includes(subcategory._id) ? 'font-medium text-blue-600' : 'hover:text-gray-900'}`}
                    >
                      {translateSubcategoryName(category.name, subcategory.name)} ({subcategoryCounts[subcategory._id] || 0})
                    </span>
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      {/* Price Range Filter with rc-slider */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h3 className="text-gray-800 font-semibold text-base mb-3">{translations.price_range}</h3>
        <div className="px-1 pt-2 pb-1">
          <Range
            range
            min={minProductPrice} // Use dynamic min/max if available from all products
            max={maxProductPrice}
            value={[priceRange[0], priceRange[1]]}
            onChange={handleSliderChange}
            allowCross={false}
            // className prop is for the wrapper, specific parts are styled via CSS
            // Handle, track, rail styles will be in NewSidebar.css
            handleStyle={[
              { borderColor: '#0D6EFD', backgroundColor: 'white', width: '18px', height: '18px', marginTop: '-7px' }, // left handle
              { borderColor: '#0D6EFD', backgroundColor: 'white', width: '18px', height: '18px', marginTop: '-7px' }  // right handle
            ]}
            trackStyle={[{ backgroundColor: '#0D6EFD', height: '4px' }]}
            railStyle={{ backgroundColor: '#A8D1FF', height: '4px' }} // Light blue for the inactive part
          />
        </div>
        <div className="flex justify-between items-center mt-2 text-sm text-gray-600">
          <span>${priceRange[0]}</span>
          <span>${priceRange[1]}</span>
        </div>
      </div>

      {/* Reset Filters Button from original sidebar */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <button
          onClick={onResetFilters}
          className="w-full py-2.5 px-4 bg-gray-100 hover:bg-gray-200 rounded-md text-gray-700 font-medium transition-colors text-sm flex items-center justify-center"
        >
          <XIcon size={16} className="mr-2 text-gray-600" />
          {translations.reset_filters}
        </button>
      </div>
    </div>
  );
};

export default NewSidebar; 