import mongoose, { Schema, Document } from 'mongoose';

export interface IBanner extends Document {
  title: string;
  description: string;
  imageUrl: string;
  linkUrl: string;
  position: string;
  type: 'banner' | 'popup' | 'advertisement';
  startDate: Date;
  endDate: Date;
  status: 'active' | 'inactive' | 'scheduled';
  createdAt: Date;
  updatedAt: Date;
}

const BannerSchema: Schema = new Schema(
  {
    title: { 
      type: String, 
      required: true,
      trim: true 
    },
    description: { 
      type: String, 
      default: '' 
    },
    imageUrl: { 
      type: String, 
      required: true 
    },
    linkUrl: { 
      type: String, 
      default: '' 
    },
    position: { 
      type: String, 
      enum: ['hero', 'middle', 'bottom', 'popup', 'sidebar'],
      default: 'hero'
    },
    type: { 
      type: String, 
      enum: ['banner', 'popup', 'advertisement'], 
      required: true 
    },
    startDate: { 
      type: Date, 
      required: true 
    },
    endDate: { 
      type: Date, 
      required: true 
    },
    status: { 
      type: String, 
      enum: ['active', 'inactive', 'scheduled'], 
      default: 'active' 
    }
  },
  {
    timestamps: true
  }
);

export default mongoose.models.Banner || mongoose.model<IBanner>('Banner', BannerSchema); 