'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AbandonedRedirectPage() {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the correct URL
    router.replace('/admin/orders/abandoned-carts');
  }, [router]);
  
  return (
    <div className="p-6 flex justify-center items-center">
      <div className="animate-spin h-10 w-10 border-4 border-blue-500 rounded-full border-t-transparent"></div>
    </div>
  );
} 