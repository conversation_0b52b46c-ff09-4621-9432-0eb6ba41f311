'use client';

import { useEffect, useRef, useCallback } from 'react';

// Dynamic import for hls.js to avoid SSR issues
let Hls: any = null;

interface HLSVideoPlayerProps {
  src?: string;
  chunkedSrc?: string; // HLS playlist URL
  className?: string;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  playsInline?: boolean;
  preload?: string;
  onLoadStart?: () => void;
  onCanPlay?: () => void;
  onError?: (error: Error) => void;
}

export default function HLSVideoPlayer({
  src,
  chunkedSrc,
  className = '',
  autoPlay = false,
  loop = false,
  muted = false,
  playsInline = false,
  preload = 'auto',
  onLoadStart,
  onCanPlay,
  onError
}: HLSVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<any>(null);

  // Better validation for chunked video URL
  const isValidChunkedSrc = chunkedSrc && 
    (chunkedSrc.includes('.m3u8') || chunkedSrc.includes('/api/hls-proxy/')) && 
    chunkedSrc.trim().length > 0;
  
  // Determine which URL to use - prefer chunked/HLS URL for faster loading
  const videoUrl = isValidChunkedSrc ? chunkedSrc : src;
  const isHLS = isValidChunkedSrc;

  // Enhanced logging for debugging video loading with direct S3 URLs [[memory:7612604151137385833]]
  useEffect(() => {
    const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
    
    if (isValidChunkedSrc && isHLS) {
      console.log(`🚀 ${deviceType.toUpperCase()} USING DIRECT HLS VIDEO:`, chunkedSrc);
      // Log if using CloudFront vs proxy for performance monitoring
      if (chunkedSrc?.includes('cloudfront.net')) {
        console.log(`⚡ ${deviceType.toUpperCase()} CLOUDFRONT ACCELERATION ACTIVE`);
      } else if (chunkedSrc?.includes('/api/hls-proxy/')) {
        console.log(`🔄 ${deviceType.toUpperCase()} Using HLS proxy fallback`);
      }
    } else if (src) {
      if (chunkedSrc) {
        console.log(`⚠️ ${deviceType.toUpperCase()} CHUNKED URL INVALID:`, chunkedSrc, 'falling back to regular video:', src);
      } else {
        console.log(`📹 ${deviceType.toUpperCase()} Using regular video (no chunked URL available):`, src);
      }
    } else {
      console.error(`❌ ${deviceType.toUpperCase()} NO VIDEO SOURCES AVAILABLE - chunkedSrc:`, chunkedSrc, 'src:', src);
    }
  }, [chunkedSrc, src, isHLS, isValidChunkedSrc]);

  // Load HLS.js dynamically
  const loadHls = useCallback(async () => {
    if (!Hls) {
      try {
        const hlsModule = await import('hls.js');
        Hls = hlsModule.default;
      } catch (error) {
        console.error('Failed to load HLS.js:', error);
        onError?.(new Error('HLS player failed to load'));
      }
    }
    return Hls;
  }, [onError]);

  // Initialize video player
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !videoUrl) return;

    // Cleanup function
    const cleanup = () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };

    // Initialize player
    const initPlayer = async () => {
      cleanup(); // Clean up any previous instance
      
      onLoadStart?.();

      if (isHLS) {
        // Use HLS.js for .m3u8 files
        const HlsClass = await loadHls();
        
        if (HlsClass && HlsClass.isSupported()) {
          console.log('🎬 Loading HLS video with hls.js:', videoUrl);
          
          // Ultra-aggressive buffer configuration to eliminate stalling [[memory:7612604151137385833]]
          const hls = new HlsClass({
            enableWorker: true,
            lowLatencyMode: false,
            
            // Ultra-aggressive buffer settings to completely prevent stalling
            backBufferLength: 90,        // 90 seconds back buffer
            maxBufferLength: 60,         // 60 seconds ahead buffer
            maxMaxBufferLength: 120,     // 120 seconds maximum buffer
            maxBufferSize: 200 * 1000 * 1000, // 200MB buffer for hero videos
            maxBufferHole: 1.0,          // Allow large gaps in buffer
            
            // Aggressive buffer management for stability
            nudgeOffset: 0.2,            // More aggressive nudging
            nudgeMaxRetry: 5,            // More retry attempts
            maxSeekHole: 3,              // Allow even larger seek holes
            
            // Force lowest quality for reliable buffering
            startLevel: 0,               // Start with lowest quality for faster buffering
            capLevelToPlayerSize: false, // Don't cap level to player size
            capLevelOnFPSDrop: false,    // Disable level dropping
            maxAutoLevel: 1,             // Limit to lower quality levels
            
            // Very generous timeouts
            manifestLoadTimeout: 15000,  // 15 second manifest timeout
            segmentLoadTimeout: 15000,   // 15 second segment timeout
            levelLoadTimeout: 15000,     // 15 second level timeout
            
            // Aggressive fragment retry settings
            fragLoadingTimeOut: 30000,   // 30 second fragment timeout
            fragLoadingMaxRetry: 10,     // 10 retry attempts
            fragLoadingRetryDelay: 500,  // Quick retry delay
            fragLoadingMaxRetryTimeout: 120000, // 2 minute max retry timeout
            
            // Aggressive level retry settings
            levelLoadingTimeOut: 15000,
            levelLoadingMaxRetry: 8,
            levelLoadingRetryDelay: 500,
            levelLoadingMaxRetryTimeout: 120000,
            
            // Very conservative bandwidth estimation
            abrEwmaDefaultEstimate: 100000, // Very low bandwidth estimate
            abrEwmaSlowVoD: 5,           // Very slow adaptation
            abrEwmaFastVoD: 5,           // Very slow adaptation
            maxStarvationDelay: 10,      // Allow very long starvation delay
            maxLoadingDelay: 10,         // Allow very long loading delay
            
            // Additional stability settings
            progressive: false,
            liveSyncDurationCount: 3,
            liveMaxLatencyDurationCount: 10,
            
            // Error recovery settings
            enableSoftwareAES: true,
            enableStreaming: true,
            forceKeyFrameOnDiscontinuity: true,
          });
          
          hlsRef.current = hls;
          
          // Enhanced event listeners with buffer monitoring
          hls.on(HlsClass.Events.MEDIA_ATTACHED, () => {
            const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
            console.log(`📱 ${deviceType.toUpperCase()} HLS media attached - Loading chunked segments...`);
            
            // Force initial buffering delay
            setTimeout(() => {
              if (video && video.readyState >= 3) {
                console.log(`🔧 ${deviceType.toUpperCase()} Pre-buffering complete, starting playback`);
              }
            }, 2000); // 2 second pre-buffer delay
          });

          hls.on(HlsClass.Events.FRAG_LOADED, (event: any, data: any) => {
            const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
            if (data.frag.sn === 0) { // First segment loaded
              console.log(`⚡ ${deviceType.toUpperCase()} FIRST HLS SEGMENT LOADED - Fast startup achieved!`);
            }
          });
          
          hls.on(HlsClass.Events.MANIFEST_PARSED, (event: any, data: any) => {
            const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
            console.log(`✅ ${deviceType.toUpperCase()} HLS CHUNKED VIDEO READY!`, {
              url: videoUrl,
              levels: data.levels?.length || 0,
              duration: data.levels?.[0]?.details?.totalduration || 'unknown'
            });
            
            // Don't call onCanPlay immediately - wait for sufficient buffering
            setTimeout(() => {
              if (video && video.buffered.length > 0) {
                const bufferLength = video.buffered.end(0) - video.currentTime;
                console.log(`📊 ${deviceType.toUpperCase()} Initial buffer length: ${bufferLength.toFixed(2)}s`);
                if (bufferLength > 3) { // Only proceed if we have at least 3 seconds buffered
                  onCanPlay?.();
                } else {
                  console.log(`⏳ ${deviceType.toUpperCase()} Waiting for more buffering...`);
                }
              }
            }, 1500); // 1.5 second delay
          });
          
          // Enhanced error handling with buffer stall recovery
          hls.on(HlsClass.Events.ERROR, (event: any, data: any) => {
            const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
            console.error(`❌ ${deviceType.toUpperCase()} HLS error:`, data);
            
            // Handle buffer stalling with aggressive recovery
            if (data.details === 'bufferStalledError') {
              console.warn(`⚠️ ${deviceType.toUpperCase()} Buffer stalled - attempting aggressive recovery`);
              
              // Log current buffer status
              if (hls.media && hls.media.buffered.length > 0) {
                const bufferedEnd = hls.media.buffered.end(hls.media.buffered.length - 1);
                const currentTime = hls.media.currentTime;
                const bufferLength = bufferedEnd - currentTime;
                console.warn(`🚨 ${deviceType.toUpperCase()} Buffer stall - only ${bufferLength.toFixed(3)}s buffered`);
              }
              
              // Aggressive recovery attempts
              try {
                // 1. Pause playback to allow buffering
                if (hls.media && !hls.media.paused) {
                  console.log(`⏸️ ${deviceType.toUpperCase()} Pausing for emergency buffering`);
                  hls.media.pause();
                }
                
                // 2. Stop and restart loading
                hls.stopLoad();
                setTimeout(() => {
                  hls.startLoad();
                  console.log(`🔄 ${deviceType.toUpperCase()} Restarted loading after stall`);
                }, 500);
                
                // 3. Resume playback after buffer builds up
                setTimeout(() => {
                  if (hls.media && hls.media.paused && hls.media.buffered.length > 0) {
                    const bufferLength = hls.media.buffered.end(0) - hls.media.currentTime;
                    if (bufferLength > 2) { // Resume if we have 2+ seconds
                      console.log(`▶️ ${deviceType.toUpperCase()} Resuming after buffering ${bufferLength.toFixed(2)}s`);
                      hls.media.play();
                    }
                  }
                }, 2000);
                
                return; // Don't treat as fatal error
              } catch (recoveryError) {
                console.error(`Failed to recover from buffer stall:`, recoveryError);
              }
            }
            
            if (data.fatal) {
              console.error(`💥 FATAL ${deviceType.toUpperCase()} HLS ERROR:`, {
                type: data.type,
                details: data.details,
                url: data.url || videoUrl,
                frag: data.frag?.url || 'N/A'
              });
              
              onError?.(new Error(`HLS error: ${data.type} - ${data.details}`));
              
              // Fallback to regular video if HLS fails
              if (src && src !== videoUrl) {
                console.log(`🔄 ${deviceType.toUpperCase()} Falling back to regular video:`, src);
                // Clear HLS instance first
                hls.destroy();
                hlsRef.current = null;
                // Set regular video source
                video.src = src;
              } else {
                console.error(`❌ ${deviceType.toUpperCase()} No fallback video available`);
              }
            } else {
              // Handle non-fatal errors based on type
              if (data.type === 'mediaError') {
                if (data.details === 'bufferStalledError') {
                  console.warn(`⚠️ ${deviceType.toUpperCase()} Buffer stalled - attempting recovery`);
                  // Try to recover from buffer stalling
                  try {
                    hls.recoverMediaError();
                  } catch (recoverError) {
                    console.error('Failed to recover from buffer stall:', recoverError);
                  }
                } else if (data.details === 'bufferFullError') {
                  console.warn(`⚠️ ${deviceType.toUpperCase()} Buffer full error - reducing buffer size`);
                  // Reduce buffer size temporarily
                  hls.config.maxBufferLength = Math.max(10, hls.config.maxBufferLength - 5);
                } else {
                  console.warn(`⚠️ ${deviceType.toUpperCase()} Non-fatal media error:`, data.details);
                  hls.recoverMediaError();
                }
              } else if (data.type === 'networkError') {
                console.warn(`⚠️ ${deviceType.toUpperCase()} Network error:`, data.details);
                // For network errors, let HLS.js handle retries automatically
              } else {
                console.warn(`⚠️ ${deviceType.toUpperCase()} Non-fatal HLS error:`, data.details);
              }
            }
          });
          
          // Monitor buffer health
          hls.on(HlsClass.Events.BUFFER_FLUSHED, () => {
            console.log('🚿 Buffer flushed - memory cleaned');
          });
          
          // Attach media and load source
          hls.attachMedia(video);
          hls.loadSource(videoUrl);
          
          // Shorter timeout for faster fallback on HLS issues
          const hlsTimeout = setTimeout(() => {
            if (hlsRef.current && src && src !== videoUrl) {
              const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
              console.log(`⏰ ${deviceType.toUpperCase()} HLS timeout - falling back to regular video`);
              hls.destroy();
              hlsRef.current = null;
              video.src = src;
            }
          }, 7000); // Reduced from 10 seconds to 7 seconds
          
          // Clear timeout on successful load
          hls.on(HlsClass.Events.MANIFEST_PARSED, () => {
            clearTimeout(hlsTimeout);
          });
        } 
        else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Native HLS support (Safari)
          const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
          console.log(`🍎 ${deviceType.toUpperCase()} Using native HLS support:`, videoUrl);
          video.src = videoUrl;
          onCanPlay?.();
        } 
        else {
          // No HLS support, fallback to regular video
          const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
          console.log(`⚠️ ${deviceType.toUpperCase()} No HLS support, using fallback video:`, src);
          if (src) {
            video.src = src;
            onCanPlay?.();
          } else {
            onError?.(new Error('No HLS support and no fallback video available'));
          }
        }
      } else {
        // Regular video file
        const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
        console.log(`🎥 ${deviceType.toUpperCase()} Loading regular video:`, videoUrl);
        video.src = videoUrl;
        onCanPlay?.();
      }
    };

    initPlayer();

    // Cleanup on unmount or URL change
    return cleanup;
  }, [videoUrl, isHLS, src, isValidChunkedSrc, loadHls, onLoadStart, onCanPlay, onError]);

  return (
    <video 
      ref={videoRef}
      className={className}
      autoPlay={autoPlay}
      loop={loop}
      muted={muted}
      playsInline={playsInline}
      preload={preload}
      onError={(e) => {
        const deviceType = window.innerWidth <= 768 ? 'mobile' : 'desktop';
        console.error(`${deviceType.toUpperCase()} Video element error:`, e);
        onError?.(new Error('Video playback error'));
      }}
    />
  );
} 