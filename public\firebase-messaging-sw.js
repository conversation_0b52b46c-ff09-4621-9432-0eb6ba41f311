// Firebase Cloud Messaging Service Worker

importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

let firebaseConfig = null;
let messaging = null;

// Listen for messages from the main thread (for config)
self.addEventListener('message', event => {
  console.log('Service worker received message:', event.data.type);
  
  if (event.data && event.data.type === 'FIREBASE_CONFIG') {
    firebaseConfig = event.data.config;
    console.log('Received Firebase config in service worker', firebaseConfig);
    
    // Initialize Firebase with the received config
    try {
      if (!firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        messaging = firebase.messaging();
        console.log('Firebase initialized successfully in service worker');
      } else {
        console.log('Firebase already initialized in service worker');
        messaging = firebase.messaging();
      }
      
      // Set background message handler
      messaging.onBackgroundMessage(function(payload) {
        console.log('[firebase-messaging-sw.js] Received background message ', payload);
        
        const notificationTitle = payload.notification?.title || 'New notification';
        const notificationOptions = {
          body: payload.notification?.body || 'You have a new notification',
          icon: payload.notification?.icon || '/logo.png',
          data: payload.data
        };
        
        return self.registration.showNotification(notificationTitle, notificationOptions);
      });
    } catch (error) {
      console.error('Error initializing Firebase in service worker:', error);
    }
  }
});

// Set initial configuration for immediate use (fallback)
self.firebaseConfig = {
  // Placeholder config will be overwritten by the message from main thread
  // This is just to prevent errors if the worker runs before receiving config
};

// Default background handler for early messages
self.addEventListener('push', function(event) {
  console.log('[Service Worker] Push Received:', event);
  
  let message;
  try {
    message = event.data.json();
  } catch(e) {
    message = {
      notification: {
        title: 'New Message',
        body: 'You have a new message',
        icon: '/logo.png'
      }
    };
  }
  
  const title = message.notification?.title || 'New notification';
  const options = {
    body: message.notification?.body || 'You have a new notification',
    icon: message.notification?.icon || '/logo.png',
    data: message.data || {}
  };
  
  event.waitUntil(self.registration.showNotification(title, options));
});

self.addEventListener('notificationclick', function(event) {
  console.log('[Service Worker] Notification click received', event);
  
  event.notification.close();
  
  // Get the notification data
  const data = event.notification.data || {};
  const url = data.url || '/';
  
  // Open a window/tab with the URL
  event.waitUntil(
    clients.matchAll({type: 'window'}).then(windowClients => {
      // Check if there is already a window/tab open with the target URL
      for (let i = 0; i < windowClients.length; i++) {
        const client = windowClients[i];
        // If so, just focus it.
        if (client.url === url && 'focus' in client) {
          return client.focus();
        }
      }
      // If not, then open the target URL in a new window/tab.
      if (clients.openWindow) {
        return clients.openWindow(url);
      }
    })
  );
}); 