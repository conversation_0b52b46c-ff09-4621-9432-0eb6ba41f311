'use client';

import React, { createContext, useState, useContext, useEffect } from 'react';

type Currency = 'USD' | 'EUR';

interface ExchangeRates {
  USD: number;
  EUR: number;
}

interface CurrencyContextType {
  currency: Currency;
  exchangeRates: ExchangeRates;
  setCurrency: (currency: Currency) => void;
  formatPrice: (priceInUSD: number) => string;
  convertPrice: (priceInUSD: number) => number;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export function CurrencyProvider({ children }: { children: React.ReactNode }) {
  const [currency, setCurrency] = useState<Currency>('USD');
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates>({ USD: 1, EUR: 0.93 });
  
  // Fetch exchange rates when component mounts
  useEffect(() => {
    const fetchExchangeRates = async () => {
      try {
        // First try the external API
        const response = await fetch('https://open.er-api.com/v6/latest/USD');
        const data = await response.json();
        
        if (data && data.rates) {
          setExchangeRates({
            USD: 1, // Base currency
            EUR: data.rates.EUR || 0.93 // Fallback value if API fails
          });
          return; // If successful, return early
        }
      } catch (error) {
        console.error('Error fetching exchange rates from external API:', error);
        // Continue to fallback API
      }
      
      // If external API fails, use our own API endpoint as fallback
      try {
        const fallbackResponse = await fetch('/api/exchange-rates');
        const fallbackData = await fallbackResponse.json();
        
        if (fallbackData && fallbackData.rates) {
          setExchangeRates({
            USD: 1,
            EUR: fallbackData.rates.EUR || 0.93
          });
        }
      } catch (fallbackError) {
        console.error('Error fetching exchange rates from fallback API:', fallbackError);
        // Keep default rates if both APIs fail
      }
    };
    
    fetchExchangeRates();
    
    // Refresh rates every hour
    const intervalId = setInterval(fetchExchangeRates, 60 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  // Save selected currency to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferredCurrency');
      if (savedCurrency && (savedCurrency === 'USD' || savedCurrency === 'EUR')) {
        setCurrency(savedCurrency as Currency);
      }
    }
  }, []);
  
  // Update localStorage when currency changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferredCurrency', currency);
    }
  }, [currency]);
  
  // Convert price from USD to selected currency
  const convertPrice = (priceInUSD: number): number => {
    const exchangeRate = exchangeRates[currency];
    const convertedPrice = priceInUSD * exchangeRate;
    return Number(convertedPrice.toFixed(2));
  };
  
  // Format price with currency symbol
  const formatPrice = (priceInUSD: number): string => {
    const convertedPrice = convertPrice(priceInUSD);
    
    if (currency === 'USD') {
      return `$${convertedPrice.toFixed(2)}`;
    } else {
      return `€${convertedPrice.toFixed(2)}`;
    }
  };
  
  return (
    <CurrencyContext.Provider value={{ 
      currency, 
      exchangeRates, 
      setCurrency, 
      formatPrice,
      convertPrice
    }}>
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
} 