import mongoose, { Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

// Define permission schema
const PermissionSchema = new Schema({
  resource: { type: String, required: true }, // e.g., 'products', 'orders', 'customers'
  actions: [{ type: String }]  // e.g., 'view', 'create', 'update', 'delete'
});

// Define role schema
const RoleSchema = new Schema({
  name: { type: String, required: true, unique: true },
  description: { type: String },
  permissions: [PermissionSchema]
}, { timestamps: true });

// Define admin user schema
const UserSchema = new Schema(
  {
    email: { type: String, required: true, unique: true, lowercase: true },
    password: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    role: { type: Schema.Types.ObjectId, ref: 'Role' },
    isActive: { type: Boolean, default: true },
    lastLogin: { type: Date },
    resetPasswordToken: { type: String },
    resetPasswordExpires: { type: Date }
  },
  { timestamps: true }
);

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 10);
  }
  next();
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function(candidatePassword: string) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Check if models exist before creating to prevent model overwrite during hot reloading
export const Role = mongoose.models.Role || mongoose.model('Role', RoleSchema);
export const User = mongoose.models.User || mongoose.model('User', UserSchema); 