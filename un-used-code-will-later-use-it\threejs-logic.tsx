import { useEffect } from 'react';

// Three.js script loading and initialization logic
export const useThreeJsLogic = (isMounted: boolean, enableAnimations: boolean) => {
  useEffect(() => {
    // Define a global THREE type to avoid TypeScript errors
    let threeJsInitialized = false;
    let THREE: any;

    // Skip if not mounted or animations are disabled
    if (!isMounted || !enableAnimations) return;

    // Check if Three.js is available
    if (typeof window !== 'undefined') {
      // Ensure canvas container exists before initializing
      const canvasContainer = document.getElementById('canvas-container');
      const canvas = document.getElementById('bg-canvas');
      
      if (!canvasContainer || !canvas) {
        console.warn('Canvas container or canvas element not found. Skipping Three.js animation initialization.');
        return;
      }

      // Load Three.js if not already loaded
      const script = document.createElement('script');
      script.src = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js";
      script.onload = () => {
        // Assign THREE to our local variable
        THREE = (window as any).THREE;
        
        // Double-check canvas exists before initializing
        const finalCanvas = document.getElementById('bg-canvas');
        if (finalCanvas) {
          // initFloatingCoins();
        } else {
          console.error('Canvas element still not found after script load');
        }
      };
      script.onerror = () => {
        console.error('Failed to load Three.js script');
      };
      document.head.appendChild(script);
    }

    // Return cleanup function
    return () => {
      if (threeJsInitialized) {
        threeJsInitialized = false;
        // Clean up event listeners
        window.removeEventListener('resize', () => {});
      }
    };
  }, [isMounted, enableAnimations]);
}; 