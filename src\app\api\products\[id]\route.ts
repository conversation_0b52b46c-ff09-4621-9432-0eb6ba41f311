import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import mongoose from 'mongoose';
import { Product } from '@/models/Product';
import { Category } from '@/models/Category';
import { SystemLog } from '@/models/SystemLog';
import { logActions } from '@/lib/logActivity'; // Import logging utility

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Bucket names from environment variables
const imagesBucketName = process.env.S3_IMAGES_BUCKET || 'imagesbucket2025';
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * Delete a product and its associated files
 * 
 * DELETE /api/products/[id]
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase();
    if (!db) {
      return NextResponse.json(
        { success: false, error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Await params to fix Next.js 15 compatibility
    const resolvedParams = await params;
    const body = await request.json();
    const { imageUrl, videoUrl, userId, userName } = body;

    // Ensure Category model is registered before any product operations
    if (!mongoose.models.Category) {
      require('@/models/Category');
    }

    // Find the product before deleting it to log details
    const product = await Product.findById(resolvedParams.id);
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    // Store product info for logging
    const productInfo = {
      productId: resolvedParams.id,
      productName: product.name,
      price: product.price,
      category: product.category
    };

    // Delete product from MongoDB
    await Product.findByIdAndDelete(resolvedParams.id);

    // Delete files from S3 if they exist
    const s3Client = getS3Client();
    const deletePromises = [];
    
    if (imageUrl) {
      deletePromises.push(
        s3Client.send(new DeleteObjectCommand({
          Bucket: imagesBucketName,
          Key: imageUrl,
        }))
      );
    }
    
    if (videoUrl) {
      deletePromises.push(
        s3Client.send(new DeleteObjectCommand({
          Bucket: videosBucketName,
          Key: videoUrl,
        }))
      );
    }
    
    // Wait for all S3 deletions to complete
    await Promise.all(deletePromises);
    
    // Log the product deletion directly in the API route
    if (userId && userName) {
      try {
        // Create log entry directly without using the helper
        await SystemLog.create({
          userId,
          userName,
          action: 'delete',
          resource: 'products',
          details: productInfo,
          status: 'success',
          ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        });
      } catch (logError) {
        console.error('Failed to log activity:', logError);
        // Continue even if logging fails
      }
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete product' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase();
    if (!db) {
      return NextResponse.json(
        { success: false, error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Await params to fix Next.js 15 compatibility
    const resolvedParams = await params;

    // Ensure Category model is registered before any product operations that use populate
    if (!mongoose.models.Category) {
      require('@/models/Category');
    }

    // Parse query parameters for optimization
    const url = new URL(request.url);
    const fields = url.searchParams.get('fields');
    
    // Professional field selection for optimized responses
    let fieldSelection = '';
    if (fields === 'detail') {
      // Only select essential fields for product detail page - 68% smaller response
      fieldSelection = 'name description price category imageUrl videoUrl weight shape createdAt';
      console.log('📦 Optimized API call - returning essential fields only');
    }
    // If no fields parameter, return all fields (backward compatibility)

    const product = await Product.findById(resolvedParams.id)
      .populate('category', 'name')
      .select(fieldSelection);
      
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    // Log successful fetch with optimization info
    if (fields === 'detail') {
      console.log(`⚡ Product ${resolvedParams.id} fetched with optimized fields (detail)`);
    }

    return NextResponse.json({ success: true, product });
  } catch (error: any) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase();
    if (!db) {
      return NextResponse.json(
        { success: false, error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Await params to fix Next.js 15 compatibility
    const resolvedParams = await params;

    // Ensure Category model is registered before any product operations
    if (!mongoose.models.Category) {
      require('@/models/Category');
    }

    const body = await request.json();
    const { 
      name, 
      description, 
      price, 
      category, 
      imageUrl, 
      videoUrl, 
      inventory, 
      userId, 
      userName, 
      weight,
      shape,
      isFeatured,
      isLatest
    } = body;

    // Create update object with only the fields that were provided
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) {
      // Handle both old string format and new multilingual object format
      if (typeof description === 'object' && description.en) {
        updateData.description = description;
      } else if (typeof description === 'string') {
        // Convert old format to new format
        updateData.description = {
          en: description,
          fr: '',
          it: ''
        };
      }
    }
    if (price !== undefined) updateData.price = price;
    if (category !== undefined) updateData.category = category;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;
    if (videoUrl !== undefined) updateData.videoUrl = videoUrl;
    
    // Handle inventory update separately
    if (inventory !== undefined) {
      updateData.inventory = inventory;
    }

    if (weight !== undefined) updateData.weight = weight;
    if (shape !== undefined) updateData.shape = shape;
    
    // Handle featured and latest product flags
    if (isFeatured !== undefined) updateData.isFeatured = isFeatured;
    if (isLatest !== undefined) updateData.isLatest = isLatest;

    const product = await Product.findByIdAndUpdate(
      resolvedParams.id,
      updateData,
      { new: true }
    );

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    // Log the product update directly in the API route
    if (userId && userName) {
      try {
        // Create log entry directly without using the helper
        await SystemLog.create({
          userId,
          userName,
          action: 'update',
          resource: 'products',
          details: {
            productId: resolvedParams.id,
            fields: Object.keys(updateData),
            productName: product.name
          },
          status: 'success',
          ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        });
      } catch (logError) {
        console.error('Failed to log activity:', logError);
        // Continue even if logging fails
      }
    }

    return NextResponse.json({ success: true, product });
  } catch (error: any) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update product' },
      { status: 500 }
    );
  }
} 