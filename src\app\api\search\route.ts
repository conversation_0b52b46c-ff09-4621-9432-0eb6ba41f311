import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Product } from '@/models/Product';

/**
 * Optimized search endpoint for header search
 * Only returns essential fields: name, price, image
 * 
 * GET /api/search?q=searchterm
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Parse query parameters
    const url = new URL(request.url);
    const searchQuery = url.searchParams.get('q');
    const limit = parseInt(url.searchParams.get('limit') || '5'); // Default to 5 results for header search
    
    // Validate search query
    if (!searchQuery || searchQuery.trim().length < 2) {
      return NextResponse.json({
        success: false,
        error: 'Search query must be at least 2 characters long'
      }, { status: 400 });
    }
    
    // Build optimized search query
    const query = {
      $or: [
        { name: { $regex: searchQuery.trim(), $options: 'i' } },
        { 'description.en': { $regex: searchQuery.trim(), $options: 'i' } },
        { 'description.tr': { $regex: searchQuery.trim(), $options: 'i' } },
        { 'description.cs': { $regex: searchQuery.trim(), $options: 'i' } }
      ]
    };
    
    // Fetch products with only essential fields for search results
    const products = await Product.find(query)
      .select('name price imageUrl images category') // Only select essential fields
      .limit(limit)
      .sort({ createdAt: -1 }) // Show newest products first
      .lean(); // Use lean() for better performance - returns plain JS objects
    
    // Transform results to ensure consistent image field
    const searchResults = products.map(product => ({
      _id: product._id,
      name: product.name,
      price: product.price,
      imageUrl: product.imageUrl,
      images: product.images,
      category: product.category
    }));
    
    return NextResponse.json({
      success: true,
      results: searchResults,
      total: searchResults.length
    });
    
  } catch (error) {
    console.error('Error performing search:', error);
    return NextResponse.json({
      success: false,
      error: 'Search failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 