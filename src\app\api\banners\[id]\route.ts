import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Banner from '@/models/Banner';
import mongoose from 'mongoose';

/**
 * Get a single banner by ID
 * 
 * GET /api/banners/[id]
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid banner ID format'
      }, { status: 400 });
    }

    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Find the banner
    const banner = await Banner.findById(params.id);
    
    if (!banner) {
      return NextResponse.json({
        success: false,
        error: 'Banner not found'
      }, { status: 404 });
    }
    
    return NextResponse.json(banner);
  } catch (error: any) {
    console.error('Error fetching banner:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch banner'
    }, { status: 500 });
  }
}

/**
 * Update a banner by ID
 * 
 * PUT /api/banners/[id]
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid banner ID format'
      }, { status: 400 });
    }
    
    // Parse the request body
    const updateData = await request.json();
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Update the banner
    const updatedBanner = await Banner.findByIdAndUpdate(
      params.id,
      { $set: updateData },
      { new: true, runValidators: true }
    );
    
    if (!updatedBanner) {
      return NextResponse.json({
        success: false,
        error: 'Banner not found'
      }, { status: 404 });
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Banner updated successfully',
      banner: updatedBanner
    });
  } catch (error: any) {
    console.error('Error updating banner:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update banner'
    }, { status: 500 });
  }
}

/**
 * Delete a banner by ID
 * 
 * DELETE /api/banners/[id]
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid banner ID format'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Delete the banner
    const deletedBanner = await Banner.findByIdAndDelete(params.id);
    
    if (!deletedBanner) {
      return NextResponse.json({
        success: false,
        error: 'Banner not found'
      }, { status: 404 });
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Banner deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting banner:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete banner'
    }, { status: 500 });
  }
} 