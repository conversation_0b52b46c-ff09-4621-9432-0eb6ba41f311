'use client';

import { useState, useEffect } from 'react';
import { getFCMToken, onForegroundMessage } from '@/lib/firebase';

type NotificationStatus = 'granted' | 'denied' | 'default' | 'unsupported';

interface UsePushNotificationsProps {
  userId?: string;
  vapidKey: string;
}

interface UsePushNotificationsReturn {
  status: NotificationStatus;
  token: string | null;
  loading: boolean;
  error: string | null;
  requestPermission: () => Promise<NotificationStatus>;
  setNotificationPreferences: (preferences: any) => Promise<boolean>;
}

/**
 * Custom hook for managing push notifications
 */
export default function usePushNotifications({ 
  userId, 
  vapidKey 
}: UsePushNotificationsProps): UsePushNotificationsReturn {
  const [status, setStatus] = useState<NotificationStatus>('default');
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Initialize the hook
  useEffect(() => {
    const init = async () => {
      try {
        // Check if notifications are supported in this environment
        if (!('Notification' in window)) {
          setStatus('unsupported');
          setLoading(false);
          return;
        }
        
        // Check current permission status
        setStatus(Notification.permission as NotificationStatus);
        
        // If permission is granted, get the FCM token
        if (Notification.permission === 'granted') {
          const fcmToken = await getFCMToken(vapidKey);
          setToken(fcmToken);
          
          // Register the token with our backend if we have a userId
          if (fcmToken && userId) {
            await registerToken(fcmToken, userId);
          }
        }
      } catch (err) {
        setError('Error initializing push notifications: ' + (err instanceof Error ? err.message : 'Unknown error'));
      } finally {
        setLoading(false);
      }
    };
    
    init();
    
    // Set up foreground message handler
    const unsubscribe = onForegroundMessage((payload) => {
      // Handle incoming messages while the app is in the foreground
      console.log('Received foreground message:', payload);
      
      // Show a notification using the browser's Notification API
      if (Notification.permission === 'granted' && payload.notification) {
        const { title, body } = payload.notification;
        new Notification(title || 'New Notification', { 
          body: body || '',
          icon: '/logo.png' 
        });
      }
    });
    
    // Clean up
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [userId, vapidKey]);
  
  /**
   * Request notification permission and get FCM token
   */
  const requestPermission = async (): Promise<NotificationStatus> => {
    try {
      if (!('Notification' in window)) {
        setStatus('unsupported');
        return 'unsupported';
      }
      
      // Request permission
      const permission = await Notification.requestPermission();
      setStatus(permission as NotificationStatus);
      
      // If permission is granted, get the FCM token
      if (permission === 'granted') {
        const fcmToken = await getFCMToken(vapidKey);
        setToken(fcmToken);
        
        // Register the token with our backend if we have a userId
        if (fcmToken && userId) {
          await registerToken(fcmToken, userId);
        }
      }
      
      return permission as NotificationStatus;
    } catch (err) {
      setError('Error requesting permission: ' + (err instanceof Error ? err.message : 'Unknown error'));
      return 'default';
    }
  };
  
  /**
   * Register FCM token with our backend
   */
  const registerToken = async (fcmToken: string, userId: string): Promise<boolean> => {
    try {
      // Get browser and device info
      const userAgent = navigator.userAgent;
      const browser = getBrowserInfo(userAgent);
      const os = getOSInfo(userAgent);
      
      // Send to our API
      const response = await fetch('/api/push-notifications/register-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          token: fcmToken,
          device: 'web',
          browser,
          os,
          notificationPermission: Notification.permission,
        }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to register token');
      }
      
      return true;
    } catch (err) {
      console.error('Error registering token:', err);
      return false;
    }
  };
  
  /**
   * Update notification preferences
   */
  const setNotificationPreferences = async (preferences: any): Promise<boolean> => {
    try {
      if (!token || !userId) {
        return false;
      }
      
      // Send to our API
      const response = await fetch('/api/push-notifications/register-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          token,
          notificationPreferences: preferences,
        }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to update preferences');
      }
      
      return true;
    } catch (err) {
      console.error('Error updating preferences:', err);
      return false;
    }
  };
  
  return {
    status,
    token,
    loading,
    error,
    requestPermission,
    setNotificationPreferences,
  };
}

// Helper function to get browser info from user agent
function getBrowserInfo(userAgent: string): string {
  const browsers = [
    { name: 'Chrome', pattern: /Chrome\/(\d+)/ },
    { name: 'Firefox', pattern: /Firefox\/(\d+)/ },
    { name: 'Safari', pattern: /Safari\/(\d+)/ },
    { name: 'Edge', pattern: /Edg(e?)\/(\d+)/ },
    { name: 'Opera', pattern: /OPR\/(\d+)/ },
  ];
  
  for (const browser of browsers) {
    const match = userAgent.match(browser.pattern);
    if (match) {
      return `${browser.name} ${match[1] || match[2]}`;
    }
  }
  
  return 'Unknown';
}

// Helper function to get OS info from user agent
function getOSInfo(userAgent: string): string {
  const systems = [
    { name: 'Windows', pattern: /Windows NT (\d+\.\d+)/ },
    { name: 'Mac', pattern: /Mac OS X (\d+[._]\d+)/ },
    { name: 'iOS', pattern: /iPhone OS (\d+)/ },
    { name: 'Android', pattern: /Android (\d+)/ },
    { name: 'Linux', pattern: /Linux/ },
  ];
  
  for (const system of systems) {
    const match = userAgent.match(system.pattern);
    if (match) {
      if (system.name === 'Mac') {
        return `${system.name} ${match[1].replace('_', '.')}`;
      }
      return match ? `${system.name} ${match[1] || ''}`.trim() : system.name;
    }
  }
  
  return 'Unknown';
} 