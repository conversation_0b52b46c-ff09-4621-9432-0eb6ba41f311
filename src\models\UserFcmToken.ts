import mongoose, { Schema } from 'mongoose';

// Define the UserFcmToken schema
const userFcmTokenSchema = new Schema({
  userId: {
    type: String,
    required: true,
    index: true,
  },
  token: {
    type: String,
    required: true,
    unique: true,
  },
  device: {
    type: String,
    // Information about the device, can be 'web', 'android', 'ios', etc.
  },
  browser: {
    type: String,
    // Information about the browser
  },
  os: {
    type: String,
    // Operating system info
  },
  notificationPermission: {
    type: String,
    enum: ['granted', 'denied', 'default'],
    default: 'default',
  },
  notificationPreferences: {
    order: {
      enabled: { type: Boolean, default: true },
      orderPlaced: { type: Boolean, default: true },
      orderConfirmed: { type: Boolean, default: true },
      orderShipped: { type: Boolean, default: true },
      orderDelivered: { type: Boolean, default: true },
      orderCancelled: { type: Boolean, default: true },
    },
    promotional: {
      enabled: { type: Boolean, default: false },
      newProducts: { type: <PERSON>olean, default: false },
      priceDrops: { type: Boolean, default: true },
      flashSales: { type: Boolean, default: true },
      couponExpiration: { type: Boolean, default: true },
      backInStock: { type: Boolean, default: true },
    },
    userActivity: {
      enabled: { type: Boolean, default: true },
      accountCreated: { type: Boolean, default: true },
      passwordChanged: { type: Boolean, default: true },
      reviewPosted: { type: Boolean, default: false },
      wishlistUpdates: { type: Boolean, default: false },
    },
  },
  quietHours: {
    enabled: { type: Boolean, default: true },
    start: { type: String, default: '22:00' },
    end: { type: String, default: '08:00' },
    timezone: { type: String, default: 'UTC' },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  lastUsed: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add pre-save hook to update timestamps
userFcmTokenSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create or get the UserFcmToken model
const UserFcmToken = mongoose.models.UserFcmToken || mongoose.model('UserFcmToken', userFcmTokenSchema);

export default UserFcmToken; 