import { NextResponse } from 'next/server';
import crypto from 'crypto';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';
import { sendEmail } from '@/lib/email'; // Assuming you have an email sending utility

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const { email } = await req.json();
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Find the user
    const user = await User.findOne({ email: email.toLowerCase() });
    
    // Don't reveal if the user exists (security best practice)
    if (!user) {
      console.log('No user found with email:', email);
      return NextResponse.json(
        { success: true, message: 'If your email exists in our system, you will receive password reset instructions.' },
        { status: 200 }
      );
    }
    
    console.log('User found:', user.email);
    
    // Generate random token
    const token = crypto.randomBytes(20).toString('hex');
    console.log('Generated reset token:', token);
    
    // Set token and expiration (1 hour from now)
    const expiry = new Date(Date.now() + 3600000); // 1 hour
    console.log('Before setting token - User object:', {
      id: user._id,
      email: user.email,
      hasToken: !!user.resetPasswordToken,
      hasExpiry: !!user.resetPasswordExpires
    });
    
    user.resetPasswordToken = token;
    user.resetPasswordExpires = expiry;
    console.log('Token expiry set to:', expiry.toISOString());
    console.log('After setting token - User object:', {
      email: user.email,
      tokenPreview: token.substring(0, 10) + '...',
      tokenLength: token.length,
      expiry: expiry
    });
    
    try {
      console.log('About to save user to database...');
      await user.save();
      console.log('User saved with reset token');
      
      // Verify token was actually saved
      console.log('DATABASE CHECK: Querying database to verify token was saved');
      const checkUser = await User.findOne({ email: user.email.toLowerCase() });
      if (checkUser) {
        console.log('User found after save:', {
          email: checkUser.email,
          hasToken: !!checkUser.resetPasswordToken,
          tokenMatch: checkUser.resetPasswordToken === token,
          tokenPreview: checkUser.resetPasswordToken ? 
            checkUser.resetPasswordToken.substring(0, 10) + '...' : 'none',
          tokenLength: checkUser.resetPasswordToken ? checkUser.resetPasswordToken.length : 0,
          expiryMatch: checkUser.resetPasswordExpires ? 
            checkUser.resetPasswordExpires.getTime() === expiry.getTime() : false
        });
      } else {
        console.log('ERROR: Could not find user after save!');
      }
    } catch (saveError) {
      console.error('ERROR SAVING USER WITH TOKEN:', saveError);
      throw saveError; // Re-throw to be caught by the outer try-catch
    }
    
    // Create reset URL with localhost for testing, or use configured domain
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const resetUrl = `${baseUrl}/admin/reset-password?token=${token}`;
    
    // Log the reset URL for testing purposes
    console.log('Password reset URL:', resetUrl);
    
    // Email content
    const emailContent = {
      to: user.email,
      subject: 'Password Reset - Afghan Int\'l Gems',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p>You requested a password reset for your admin account at Afghan Int'l Gems.</p>
          <p>Please click the button below to reset your password:</p>
          <div style="text-align: center; margin: 25px 0;">
            <a href="${resetUrl}" style="background-color: #4a6cf7; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; word-break: break-all;"><a href="${resetUrl}">${resetUrl}</a></p>
          <p><strong>Note:</strong> This link is valid for 1 hour.</p>
          <p>If you didn't request this, please ignore this email or contact support if you have concerns.</p>
          <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
          <p style="color: #777; font-size: 12px;">Afghan Int'l Gems Administration</p>
        </div>
      `
    };
    
    try {
      // Send email
      await sendEmail(emailContent);
      
      return NextResponse.json({
        success: true,
        message: 'If your email exists in our system, you will receive password reset instructions.'
      });
    } catch (emailError) {
      console.error('Error sending password reset email:', emailError);
      
      // Revert token in case of email failure
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      await user.save();
      
      return NextResponse.json(
        { error: 'Failed to send password reset email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Password reset request error:', error);
    return NextResponse.json(
      { error: 'Password reset request failed' },
      { status: 500 }
    );
  }
} 