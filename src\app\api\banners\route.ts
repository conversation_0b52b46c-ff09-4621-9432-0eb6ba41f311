import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Banner from '@/models/Banner';

/**
 * Get all banners with optional filtering
 * 
 * GET /api/banners
 */
export async function GET(request: Request) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    
    // Build query based on parameters
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }
    
    // Check if we need active banners only (current date is between start and end dates)
    if (status === 'active') {
      const now = new Date();
      query.startDate = { $lte: now };
      query.endDate = { $gte: now };
    }
    
    // Fetch banners
    const banners = await Banner.find(query).sort({ createdAt: -1 });
    
    return NextResponse.json(banners);
  } catch (error: any) {
    console.error('Error fetching banners:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch banners'
    }, { status: 500 });
  }
}

/**
 * Create a new banner
 * 
 * POST /api/banners
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const bannerData = await request.json();
    
    // Validate required fields
    if (!bannerData.title || !bannerData.imageUrl || !bannerData.type || !bannerData.startDate || !bannerData.endDate) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Create the banner
    const newBanner = new Banner(bannerData);
    await newBanner.save();
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Banner created successfully',
      banner: newBanner
    });
  } catch (error: any) {
    console.error('Error creating banner:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create banner'
    }, { status: 500 });
  }
} 