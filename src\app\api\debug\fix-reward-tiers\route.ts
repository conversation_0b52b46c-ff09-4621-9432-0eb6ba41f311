import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

// This is the reward tier structure
const rewardTiers = [
  {
    id: 6,
    name: '$100 Reward',
    spendRequired: 2000
  },
  {
    id: 7,
    name: '$200 Reward',
    spendRequired: 4000
  },
  {
    id: 8,
    name: '$350 Reward',
    spendRequired: 6000
  }
];

export async function GET() {
  try {
    await dbConnect();
    
    // Find all customers with rewards
    const customers = await Customer.find({ 'currentRewards.0': { $exists: true } });
    console.log(`Found ${customers.length} customers with rewards`);
    
    const results = [];
    
    // Process each customer
    for (const customer of customers) {
      if (!customer.currentRewards || customer.currentRewards.length <= 1) {
        continue; // Skip customers with 0 or 1 reward
      }
      
      console.log(`Processing ${customer.email} with ${customer.currentRewards.length} rewards`);
      
      // Sort rewards by spend requirement (highest first)
      const sortedRewards = [...customer.currentRewards].sort((a, b) => {
        const aReward = rewardTiers.find(r => r.id === a.id);
        const bReward = rewardTiers.find(r => r.id === b.id);
        return (bReward?.spendRequired || 0) - (aReward?.spendRequired || 0);
      });
      
      // Keep only the highest reward
      const highestReward = sortedRewards[0];
      
      if (highestReward) {
        // Update customer with only the highest reward
        customer.currentRewards = [highestReward];
        await customer.save();
        
        results.push({
          customer: customer.email,
          originalRewardCount: sortedRewards.length,
          keptReward: highestReward.name,
          success: true
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Fixed rewards for ${results.length} customers`,
      results
    });
  } catch (error: any) {
    console.error('Error fixing reward tiers:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fix reward tiers',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
} 