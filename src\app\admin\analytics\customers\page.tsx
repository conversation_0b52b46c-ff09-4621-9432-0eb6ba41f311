'use client';

import { useState } from 'react';
import { 
  LineChart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Map, Users, Clock, ShoppingBag, ThumbsUp, ThumbsDown, Heart } from "lucide-react";

// Mock data - replace with actual API calls in production
const mockCustomerData = {
  customerAcquisition: [
    { month: 'Jan', new: 120, returning: 80 },
    { month: 'Feb', new: 150, returning: 90 },
    { month: 'Mar', new: 180, returning: 110 },
    { month: 'Apr', new: 200, returning: 130 },
    { month: 'May', new: 220, returning: 150 },
    { month: 'Jun', new: 250, returning: 170 }
  ],
  ageDistribution: [
    { name: '18-24', value: 15 },
    { name: '25-34', value: 35 },
    { name: '35-44', value: 25 },
    { name: '45-54', value: 15 },
    { name: '55+', value: 10 }
  ],
  regionDistribution: [
    { name: 'North America', value: 45 },
    { name: 'Europe', value: 25 },
    { name: 'Asia', value: 18 },
    { name: 'Australia', value: 7 },
    { name: 'Other', value: 5 }
  ],
  customerLifetimeValue: [
    { segment: 'Premium', clv: 1200 },
    { segment: 'Regular', clv: 850 },
    { segment: 'Occasional', clv: 450 },
    { segment: 'New', clv: 200 },
    { segment: 'At Risk', clv: 100 }
  ],
  topCustomers: [
    { id: 1, name: 'John Smith', orders: 12, spend: 4850, lastPurchase: '2023-06-10' },
    { id: 2, name: 'Sarah Johnson', orders: 10, spend: 3950, lastPurchase: '2023-06-05' },
    { id: 3, name: 'Michael Brown', orders: 8, spend: 3200, lastPurchase: '2023-05-28' },
    { id: 4, name: 'Emily Davis', orders: 7, spend: 2950, lastPurchase: '2023-05-20' },
    { id: 5, name: 'Robert Wilson', orders: 6, spend: 2500, lastPurchase: '2023-05-15' }
  ],
  customerPreferences: {
    likes: [
      { category: 'Diamonds', percentage: 65 },
      { category: 'Gold Jewelry', percentage: 58 },
      { category: 'Gemstones', percentage: 52 },
      { category: 'Silver Jewelry', percentage: 43 },
      { category: 'Pearl Jewelry', percentage: 38 }
    ],
    dislikes: [
      { category: 'Fashion Jewelry', percentage: 25 },
      { category: 'Brass Jewelry', percentage: 20 },
      { category: 'Costume Jewelry', percentage: 18 },
      { category: 'Vintage Designs', percentage: 15 },
      { category: 'Minimalist Designs', percentage: 12 }
    ],
    trends: [
      { month: 'Jan', likeRate: 68, dislikeRate: 12 },
      { month: 'Feb', likeRate: 72, dislikeRate: 10 },
      { month: 'Mar', likeRate: 75, dislikeRate: 8 },
      { month: 'Apr', likeRate: 70, dislikeRate: 14 },
      { month: 'May', likeRate: 76, dislikeRate: 9 },
      { month: 'Jun', likeRate: 80, dislikeRate: 7 }
    ]
  }
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function CustomerInsightsScreen() {
  const [timeRange, setTimeRange] = useState<string>('month');
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Customer Insights</h1>
        
        <Select 
          value={timeRange} 
          onValueChange={(value: string) => setTimeRange(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Customer Acquisition */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Customer Acquisition</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockCustomerData.customerAcquisition}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="new" fill="#8884d8" name="New Customers" />
                <Bar dataKey="returning" fill="#82ca9d" name="Returning Customers" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* Demographics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-md font-medium">Age Distribution</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={mockCustomerData.ageDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {mockCustomerData.ageDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-md font-medium">Regional Distribution</CardTitle>
            <Map className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={mockCustomerData.regionDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {mockCustomerData.regionDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Customer Preferences - New Section */}
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-md font-medium">Customer Preferences</CardTitle>
          <Heart className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="likes" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="likes" className="flex items-center">
                <ThumbsUp className="h-4 w-4 mr-2" />
                Top Likes
              </TabsTrigger>
              <TabsTrigger value="dislikes" className="flex items-center">
                <ThumbsDown className="h-4 w-4 mr-2" />
                Top Dislikes
              </TabsTrigger>
              <TabsTrigger value="trends">Preference Trends</TabsTrigger>
            </TabsList>
            
            <TabsContent value="likes">
              <div className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    layout="vertical"
                    data={mockCustomerData.customerPreferences.likes}
                    margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} />
                    <YAxis type="category" dataKey="category" width={100} />
                    <Tooltip formatter={(value) => [`${value}%`, 'Preference Rate']} />
                    <Bar dataKey="percentage" fill="#4CAF50" name="Like Rate (%)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>
            
            <TabsContent value="dislikes">
              <div className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    layout="vertical"
                    data={mockCustomerData.customerPreferences.dislikes}
                    margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} />
                    <YAxis type="category" dataKey="category" width={100} />
                    <Tooltip formatter={(value) => [`${value}%`, 'Dislike Rate']} />
                    <Bar dataKey="percentage" fill="#FF5252" name="Dislike Rate (%)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>
            
            <TabsContent value="trends">
              <div className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={mockCustomerData.customerPreferences.trends}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="likeRate" stroke="#4CAF50" name="Like Rate (%)" />
                    <Line type="monotone" dataKey="dislikeRate" stroke="#FF5252" name="Dislike Rate (%)" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 p-4 bg-gray-50 rounded-md">
                <h4 className="font-medium mb-2">Insights:</h4>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  <li>Customer satisfaction has increased by 12% over the last 6 months</li>
                  <li>Diamonds and gold jewelry remain the most preferred categories</li>
                  <li>Preference for gemstones has increased by 8% since January</li>
                  <li>Fashion and costume jewelry continue to have lower preference rates</li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Customer Lifetime Value */}
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-md font-medium">Customer Lifetime Value by Segment</CardTitle>
          <ShoppingBag className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockCustomerData.customerLifetimeValue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="segment" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value}`, 'Lifetime Value']} />
                <Legend />
                <Bar dataKey="clv" fill="#8884d8" name="Customer Lifetime Value ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* Top Customers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-md font-medium">Top Customers</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Purchase</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockCustomerData.topCustomers.map((customer) => (
                  <tr key={customer.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{customer.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.orders}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.spend.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.lastPurchase}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 