import mongoose, { Schema } from 'mongoose';

// Define tax rule schema
const TaxRuleSchema = new Schema(
  {
    name: { 
      type: String, 
      required: [true, 'Tax rule name is required'],
      trim: true
    },
    rate: { 
      type: Number, 
      required: [true, 'Tax rate is required'],
      min: [0, 'Tax rate cannot be negative'],
      default: 0
    },
    country: { 
      type: String, 
      required: [true, 'Country is required'],
      trim: true
    },
    state: { 
      type: String, 
      default: '',
      trim: true
    },
    applyToShipping: { 
      type: Boolean, 
      default: false 
    },
    isDefault: { 
      type: Boolean, 
      default: false 
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Ensure only one default tax rule
TaxRuleSchema.pre('save', async function(next) {
  if (this.isDefault) {
    // If this document is being set as default, unset any existing default
    const model = mongoose.model('TaxRule');
    await model.updateMany(
      { _id: { $ne: this._id }, isDefault: true },
      { isDefault: false }
    );
  }
  next();
});

// Check if model exists before creating to prevent model overwrite during hot reloading
export const TaxRule = mongoose.models.TaxRule || mongoose.model('TaxRule', TaxRuleSchema); 