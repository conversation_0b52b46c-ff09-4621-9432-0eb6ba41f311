import { NextRequest, NextResponse } from 'next/server';
import { TranslateClient, TranslateTextCommand } from '@aws-sdk/client-translate';

// AWS Translate configuration from environment variables
const translateConfig = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get AWS Translate client
const getTranslateClient = () => {
  return new TranslateClient(translateConfig);
};

/**
 * Test translation endpoint using AWS Translate
 * Accepts English text and returns translations in French and Italian
 * 
 * POST /api/test-translate
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { text } = await request.json();
    
    // Validate input
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Please provide valid English text to translate'
      }, { status: 400 });
    }

    // Check if text is too long (AWS Translate has a 5000 byte limit)
    if (text.length > 5000) {
      return NextResponse.json({
        success: false,
        error: 'Text is too long. Maximum 5000 characters allowed.'
      }, { status: 400 });
    }

    const client = getTranslateClient();
    
    // Translate to French and Italian in parallel
    const [frenchTranslation, italianTranslation] = await Promise.all([
      // Translate to French
      client.send(new TranslateTextCommand({
        Text: text.trim(),
        SourceLanguageCode: 'en',
        TargetLanguageCode: 'fr'
      })),
      // Translate to Italian
      client.send(new TranslateTextCommand({
        Text: text.trim(),
        SourceLanguageCode: 'en',
        TargetLanguageCode: 'it'
      }))
    ]);

    // Return successful response with translations
    return NextResponse.json({
      success: true,
      translations: {
        english: text.trim(),
        french: frenchTranslation.TranslatedText || '',
        italian: italianTranslation.TranslatedText || ''
      },
      message: 'Translation completed successfully'
    });

  } catch (error: any) {
    console.error('Translation error:', error);
    
    // Handle specific AWS errors
    if (error.name === 'CredentialsError' || error.name === 'UnauthorizedOperation') {
      return NextResponse.json({
        success: false,
        error: 'AWS credentials are not properly configured. Please check your AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.'
      }, { status: 401 });
    }
    
    if (error.name === 'UnsupportedLanguagePairException') {
      return NextResponse.json({
        success: false,
        error: 'The specified language pair is not supported by AWS Translate.'
      }, { status: 400 });
    }
    
    if (error.name === 'TextSizeLimitExceededException') {
      return NextResponse.json({
        success: false,
        error: 'The text is too long for translation. Please reduce the text size.'
      }, { status: 400 });
    }
    
    // Generic error response
    return NextResponse.json({
      success: false,
      error: 'Translation failed. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

/**
 * GET endpoint to test if the translation service is available
 * 
 * GET /api/test-translate
 */
export async function GET() {
  try {
    // Check if AWS credentials are configured
    const hasCredentials = !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY);
    
    if (!hasCredentials) {
      return NextResponse.json({
        success: false,
        error: 'AWS credentials are not configured',
        status: 'not_configured'
      }, { status: 500 });
    }

    // Test with a simple translation
    const client = getTranslateClient();
    const testTranslation = await client.send(new TranslateTextCommand({
      Text: 'Hello',
      SourceLanguageCode: 'en',
      TargetLanguageCode: 'fr'
    }));

    return NextResponse.json({
      success: true,
      message: 'AWS Translate service is working correctly',
      status: 'ready',
      testTranslation: {
        original: 'Hello',
        translated: testTranslation.TranslatedText,
        language: 'French'
      }
    });

  } catch (error: any) {
    console.error('Translation service test error:', error);

    // More detailed error information
    let errorMessage = 'Translation service is not available';
    let errorDetails = error.message || 'Unknown error';

    if (error.name === 'CredentialsError' || error.message?.includes('credentials')) {
      errorMessage = 'AWS credentials are not properly configured';
      errorDetails = 'Missing or invalid AWS_ACCESS_KEY_ID or AWS_SECRET_ACCESS_KEY';
    } else if (error.name === 'UnknownEndpoint' || error.message?.includes('endpoint')) {
      errorMessage = 'AWS Translate service endpoint not found';
      errorDetails = `Check if AWS Translate is available in region: ${translateConfig.region}`;
    } else if (error.name === 'NetworkingError' || error.message?.includes('network')) {
      errorMessage = 'Network error connecting to AWS Translate';
      errorDetails = 'Check internet connection and AWS service status';
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      status: 'error',
      details: errorDetails,
      rawError: process.env.NODE_ENV === 'development' ? {
        name: error.name,
        message: error.message,
        code: error.code,
        statusCode: error.statusCode
      } : undefined
    }, { status: 500 });
  }
}
