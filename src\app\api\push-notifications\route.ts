import { NextRequest, NextResponse } from 'next/server';
import Notification from '@/models/Notification';
import UserFcmToken from '@/models/UserFcmToken';
import connectDB from '@/lib/mongoose';
import admin from 'firebase-admin';

// We use the firebaseAdmin from @/lib/firebase-admin.ts which already handles initialization

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  try {
    // Make sure to trim any whitespace from credentials
    const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY
      ? process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n').trim()
      : undefined;

    const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID?.trim();
    const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL?.trim();

    console.log('Firebase Admin SDK initializing with project ID:', projectId);
    
    if (!projectId || !clientEmail || !privateKey) {
      throw new Error(
        `Missing Firebase Admin credentials: 
        ProjectID: ${projectId ? 'OK' : 'MISSING'}, 
        ClientEmail: ${clientEmail ? 'OK' : 'MISSING'}, 
        PrivateKey: ${privateKey ? 'OK' : 'MISSING'}`
      );
    }

    admin.initializeApp({
      credential: admin.credential.cert({
        projectId,
        clientEmail,
        privateKey,
      }),
    });
    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Connect to the database
    await connectDB();

    // Parse the request body
    const { 
      type, 
      subType, 
      title, 
      body, 
      data,
      image,
      recipientGroup,
      manualRecipients,
      scheduledDate,
      scheduledTime,
      sendNow,
    } = await request.json();

    // Validate the request
    if (!type || !subType || !title || !body) {
      return NextResponse.json(
        { error: 'Type, subType, title, and body are required fields' },
        { status: 400 }
      );
    }

    // Determine recipients based on the group
    let recipients = [];

    if (recipientGroup === 'manual' && manualRecipients) {
      // Manual recipients are provided as an array of user IDs
      const userIds = manualRecipients.split(',').map((id: string) => id.trim());
      const fcmTokens = await UserFcmToken.find({ 
        userId: { $in: userIds },
        isActive: true,
        notificationPermission: 'granted',
      });

      recipients = fcmTokens.map(token => ({
        userId: token.userId,
        fcmToken: token.token,
        status: 'pending',
      }));
    } else {
      // Get recipients based on the selected group and notification preferences
      recipients = await getRecipientsForGroup(recipientGroup, type, subType);
    }

    // Check if we have recipients
    if (recipients.length === 0) {
      return NextResponse.json(
        { error: 'No recipients found for the selected group' },
        { status: 400 }
      );
    }

    // Create notification data for database
    const notificationData = {
      type,
      subType,
      title,
      body,
      data: data || {},
      image,
      recipients,
      status: sendNow ? 'sending' : 'scheduled',
      scheduledAt: !sendNow ? new Date(`${scheduledDate}T${scheduledTime}`) : undefined,
      stats: {
        total: recipients.length,
        sent: 0,
        delivered: 0,
        opened: 0,
        failed: 0,
      },
    };

    // Create a new notification in the database
    const notification = new Notification(notificationData);
    await notification.save();

    // If not to be sent immediately, return success
    if (!sendNow) {
      return NextResponse.json({ 
        success: true, 
        message: `Notification scheduled for ${scheduledDate} at ${scheduledTime}`,
        notificationId: notification._id
      });
    }

    // Send push notification immediately
    try {
      console.log('Starting push notification sending process with notification:', 
        {
          id: notification._id.toString(),
          type: notification.type,
          subType: notification.subType,
          recipientCount: notification.recipients.length,
          tokens: notification.recipients.map((r: any) => r.fcmToken.substring(0, 10) + '...')
        }
      );
      
      const results = await sendPushNotification(notification);
      
      console.log('Push notification sending results:', results);
      
      // Update notification stats in the database
      notification.stats.sent = results.successCount;
      notification.stats.failed = results.failureCount;
      notification.status = results.successCount > 0 ? 'sent' : 'failed';
      
      // Update individual recipient statuses
      if (results.responses && results.responses.length > 0) {
        results.responses.forEach((response: any, index: number) => {
          if (notification.recipients[index]) {
            notification.recipients[index].status = response.success ? 'sent' : 'failed';
            notification.recipients[index].sentAt = new Date();
            if (!response.success && response.error) {
              notification.recipients[index].error = response.error.message;
            }
          }
        });
      }
      
      await notification.save();

      return NextResponse.json({ 
        success: true, 
        message: `Push notification sent successfully to ${results.successCount} recipients`,
        notificationId: notification._id,
        stats: notification.stats
      });
    } catch (error) {
      // Update the notification status to failed
      notification.status = 'failed';
      await notification.save();

      throw error; // Re-throw to be caught by the outer catch block
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
    return NextResponse.json(
      { error: 'Failed to send push notification: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// Helper function to get recipients based on group and notification preferences
async function getRecipientsForGroup(group: string, type: string, subType: string) {
  try {
    let query: any = {
      isActive: true,
      notificationPermission: 'granted',
    };

    // Set query based on notification preferences
    const preferencePath = `notificationPreferences.${type}.enabled`;
    const specificPreferencePath = `notificationPreferences.${type}.${subType}`;
    
    query[preferencePath] = true;
    query[specificPreferencePath] = true;

    // Additional filters based on group
    switch (group) {
      case 'all':
        // No additional filters
        break;
      case 'new':
        query.createdAt = { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }; // Last 7 days
        break;
      case 'active':
        query.lastUsed = { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }; // Last 30 days
        break;
      case 'inactive':
        query.lastUsed = { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }; // More than 30 days
        break;
      // Add more groups as needed
    }

    // Get FCM tokens that match the query
    const fcmTokens = await UserFcmToken.find(query);

    // Format recipients for the notification
    return fcmTokens.map(token => ({
      userId: token.userId,
      fcmToken: token.token,
      status: 'pending',
    }));
  } catch (error) {
    console.error('Error getting recipients:', error);
    return [];
  }
}

// Helper function to send push notification using Firebase Cloud Messaging
async function sendPushNotification(notification: any) {
  try {
    // Verify Firebase Admin is initialized
    if (!admin.apps.length) {
      console.error('Firebase Admin SDK is not initialized');
      throw new Error('Firebase Admin SDK is not initialized');
    }
    
    console.log('Firebase Admin SDK initialized:', {
      appCount: admin.apps.length,
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID?.trim()
    });
    
    // Get FCM tokens from recipients
    const tokens = notification.recipients.map((recipient: any) => recipient.fcmToken);
    
    console.log(`Processing ${tokens.length} FCM tokens`);
    
    if (tokens.length === 0) {
      console.warn('No valid FCM tokens to send to');
      return {
        successCount: 0,
        failureCount: 0,
        responses: [],
      };
    }
    
    // Log sample of tokens (first 3, partial for security)
    const tokenSamples = tokens.slice(0, 3).map((t: string) => 
      t.substring(0, 10) + '...' + t.substring(t.length - 10)
    );
    console.log('Token samples:', tokenSamples);
    
    // Match the implementation from test-token endpoint which is known to work
    // Process each token individually - slower but more reliable for debugging
    let successCount = 0;
    let failureCount = 0;
    let responses: any[] = [];
    
    // Process each token individually, just like in the test endpoint
    console.log('Processing each token individually using admin.messaging().send()');
    
    for (let i = 0; i < tokens.length; i++) {
      try {
        const token = tokens[i];
        console.log(`Sending to token ${i+1}/${tokens.length}: ${token.substring(0, 10)}...`);
        
        const message = {
          notification: {
            title: notification.title,
            body: notification.body,
            ...(notification.image && { imageUrl: notification.image }),
          },
          data: {
            ...notification.data,
            notificationId: notification._id.toString(),
            type: notification.type,
            subType: notification.subType,
            click_action: notification.clickAction || '/',
          },
          token: token,
        };
        
        // Send using admin.messaging().send() - same as test endpoint
        const messageId = await admin.messaging().send(message);
        console.log(`Successfully sent message ${i+1}/${tokens.length}`);
        
        successCount++;
        responses.push({ success: true, messageId });
      } catch (error: any) {
        console.error(`Error sending to token ${i+1}/${tokens.length}:`, error);
        failureCount++;
        responses.push({ 
          success: false, 
          error: { 
            code: error.code || 'unknown', 
            message: error.message || String(error) 
          } 
        });
      }
    }
    
    console.log(`Finished processing all tokens: ${successCount} successful, ${failureCount} failed`);
    return { successCount, failureCount, responses };
    
  } catch (error) {
    console.error('Error in push notification function:', error);
    throw error;
  }
}

// GET endpoint to fetch notification history
export async function GET(request: NextRequest) {
  try {
    // Connect to the database
    await connectDB();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (status) query.status = status;
    
    // Get total count
    const total = await Notification.countDocuments(query);
    
    // Get notifications
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);
    
    return NextResponse.json({
      success: true,
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
} 