'use client';

import { useState, useEffect } from 'react';
import ConnectionStatus from "@/components/ConnectionStatus";
import S3ConnectionStatus from "@/components/S3ConnectionStatus";
import Link from 'next/link';

export default function ConnectionStatusPage() {
  const [isLoading, setIsLoading] = useState(true);
  
  // Add a small delay to ensure components are ready
  useEffect(() => {
    // Prefetch API calls
    Promise.all([
      fetch('/api/check-connection').catch(() => {}),
      fetch('/api/check-s3-connections').catch(() => {})
    ]).finally(() => {
      // Set loading to false after a short delay
      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    });
  }, []);
  
  if (isLoading) {
    return (
      <div className="min-h-screen p-8 flex items-center justify-center">
        <div className="animate-spin h-10 w-10 border-4 border-indigo-600 border-t-transparent rounded-full"></div>
        <p className="ml-4 text-lg text-gray-600">Loading connection status...</p>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen p-8 pb-20 flex flex-col items-center">
      <header className="w-full max-w-4xl mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">Connection Status</h1>
        <p className="text-gray-600">
          View the connection status for MongoDB and AWS S3 buckets.
        </p>
      </header>
      
      <main className="w-full max-w-4xl space-y-12">
        {/* MongoDB Connection Status */}
        <section>
          <h2 className="text-xl font-semibold mb-4">MongoDB Connection Status</h2>
          <ConnectionStatus />
        </section>
        
        {/* S3 Bucket Connection Status */}
        <section>
          <S3ConnectionStatus />
        </section>
        
        {/* Back to Home Button */}
        <div className="mt-8 flex justify-center">
          <Link href="/" className="text-blue-500 hover:text-blue-700">
            Back to Home
          </Link>
        </div>
      </main>
    </div>
  );
} 