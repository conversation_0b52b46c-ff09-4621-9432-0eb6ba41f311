'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js';
import { CreditCard, Wallet, Building2, ShoppingCart, ArrowLeft, User, Mail, Phone, MapPin, CheckCircle, Package } from 'lucide-react';
import { FaApple } from 'react-icons/fa';
import { AiOutlineApple } from 'react-icons/ai';
import StripePaymentElement from '@/components/StripePaymentElement';
import { useCartStore } from '@/store/useCartStore';
import { markCartAsRecovered } from '@/services/cartSyncService';
import { useAuth } from '@/contexts/AuthContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useLanguage } from '@/contexts/LanguageContext';
import RelatedProducts from '@/components/RelatedProducts';
import CheckoutSkeleton from '@/components/CheckoutSkeleton';

// Initialize Stripe with publishable key
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

// Define CartItem and Product types
type Product = {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  videoUrl?: string;
  createdAt: string;
}

type CartItem = {
  product: Product;
  quantity: number;
}

type PaymentMethod = 'apple-pay' | 'stripe' | 'paypal' | 'bank-transfer' | null;

type CheckoutStep = 'customer-info' | 'payment';

// Define customer information type
type CustomerInfo = {
  email: string;
  phone: string;
  fullName: string;
  address: string;
}

// Add this new type for discount information
type DiscountInfo = {
  _id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  discountAmount: number;
  description: string;
}

// Define order summary type
type OrderSummary = {
  subtotal: number;
  shipping: number;
  total: number;
  date: string;
}



// Progress Bar Component
function CheckoutProgressBar({ currentStep, orderPlaced }: { currentStep: CheckoutStep; orderPlaced?: boolean }) {
  const { translations } = useLanguage();
  
  const steps = [
    { id: 'customer-info', name: translations.customer_info, icon: User },
    { id: 'payment', name: translations.place_order, icon: Package }
  ];

  const getStepIndex = (step: CheckoutStep) => {
    return steps.findIndex(s => s.id === step);
  };

  const currentStepIndex = getStepIndex(currentStep);

  return (
    <div id="progress-bar-container" className="w-full bg-[#f8f8f8] rounded-lg pt-4 px-4 pb-[27px] sm:pt-6 sm:px-6 sm:pb-[27px]">
      <div id="progress-bar-steps" className="flex items-center justify-between max-w-full mx-auto" style={{ paddingLeft: '20px' }}>
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStepIndex && !orderPlaced;
          const isCompleted = index < currentStepIndex || (orderPlaced && index <= 1);
          const isUpcoming = index > currentStepIndex && !orderPlaced;

          return (
            <div key={step.id} id={`step-${step.id}`} className="flex items-center flex-1">
              {/* Step Circle */}
              <div id={`step-${step.id}-circle-container`} className="flex items-center flex-shrink-0">
                <div
                  id={`step-${step.id}-circle`}
                  className={`
                    w-8 h-8 sm:w-10 sm:h-10 min-w-[2rem] min-h-[2rem] sm:min-w-[2.5rem] sm:min-h-[2.5rem] max-w-[2rem] max-h-[2rem] sm:max-w-[2.5rem] sm:max-h-[2.5rem] rounded-full flex items-center justify-center border-2 transition-all duration-300 flex-shrink-0 aspect-square
                    ${isCompleted 
                      ? 'bg-green-500 border-green-500 text-white' 
                      : isActive 
                        ? 'bg-blue-500 border-blue-500 text-white' 
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }
                  `}
                >
                  {isCompleted ? (
                    <CheckCircle size={16} className="sm:w-5 sm:h-5" />
                  ) : (
                    <span className="font-inter text-xs sm:text-sm font-medium tracking-wide" style={{ fontSize: '12px', lineHeight: '1.75em', letterSpacing: '0.7px', textTransform: 'uppercase' }}>{index + 1}</span>
                  )}
                </div>
                
                {/* Step Label */}
                <div id={`step-${step.id}-label`} className="ml-2 sm:ml-3 min-w-0 flex-1">
                  <p
                    className={`
                      font-dosis text-xs sm:text-sm font-normal transition-colors duration-300 truncate tracking-wide
                      ${isActive || isCompleted ? 'text-gray-900' : 'text-gray-500'}
                    `}
                    style={{ fontSize: '12px', lineHeight: '1.75em', letterSpacing: '0.7px', textTransform: 'capitalize' }}
                  >
                    {step.name}
                  </p>
                </div>
              </div>

              {/* Progress Line */}
              {index < steps.length - 1 && (
                <div id={`progress-line-${index}`} className="mx-2 sm:mx-4 md:mx-8 flex-1 max-w-[3rem] sm:max-w-[6rem] md:max-w-[8rem]" style={index === 0 ? { width: '40px' } : {}}>
                  <div id={`progress-line-track-${index}`} className="h-0.5 bg-gray-200 relative">
                    <div
                      id={`progress-line-fill-${index}`}
                      className={`
                        h-full transition-all duration-500 ease-in-out
                        ${isCompleted || orderPlaced ? 'bg-green-500 w-full' : 'bg-gray-200 w-0'}
                      `}
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Component to handle search params
function CheckoutContent() {
  const { items, clearCart } = useCartStore();
  const { customer, isAuthenticated, loginWithCheckoutInfo } = useAuth();
  const { formatPrice, currency, convertPrice } = useCurrency();
  const { translations } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [orderReference, setOrderReference] = useState<string>('');
  const [mounted, setMounted] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<CheckoutStep>('customer-info');
  const [orderSummary, setOrderSummary] = useState<OrderSummary | null>(null);
  const [showAutoFillNotification, setShowAutoFillNotification] = useState(false);
  const [customerReward, setCustomerReward] = useState<{ amount: number, applied: boolean }>({ amount: 0, applied: false });
  const [applyingReward, setApplyingReward] = useState(false);
  const [previousUrl, setPreviousUrl] = useState<string>('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    email: '',
    phone: '',
    fullName: '',
    address: ''
  });
  const [customerInfoErrors, setCustomerInfoErrors] = useState<Record<string, string>>({});
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Add new state for discount code
  const [discountCode, setDiscountCode] = useState('');
  const [applyingDiscount, setApplyingDiscount] = useState(false);
  const [appliedDiscount, setAppliedDiscount] = useState<DiscountInfo | null>(null);
  const [discountError, setDiscountError] = useState<string | null>(null);
  
  // Check if came back from a canceled checkout
  const canceled = searchParams.get('canceled');
  
  // Set mounted to true on client-side and store previous URL
  useEffect(() => {
    setMounted(true);
    
    // First try to get previous URL from sessionStorage (if set by product page)
    const storedPreviousUrl = sessionStorage.getItem('previousProductUrl');
    if (storedPreviousUrl) {
      setPreviousUrl(storedPreviousUrl);
    } else {
      // Try to get the referrer URL as fallback
      const referrer = document.referrer;
      if (referrer && referrer.includes('/product/')) {
        setPreviousUrl(referrer);
      } else {
        // Fall back to storing a default product page
        setPreviousUrl('/');
      }
    }
    
    // Ensure the page is scrolled to the top when checkout loads
    window.scrollTo(0, 0);
  }, []);
  
  // Initialize customer info from existing authenticated customer if available
  useEffect(() => {
    if (isAuthenticated && customer) {
      setCustomerInfo({
        email: customer.email,
        phone: customer.phone,
        fullName: `${customer.firstName} ${customer.lastName}`,
        address: `${customer.address.street}, ${customer.address.city}, ${customer.address.province}, ${customer.address.country} ${customer.address.postalCode}`
      });
      
      // Check if customer has rewards and set them
      if (customer.currentRewards && customer.currentRewards.length > 0) {
        const rewardMatch = customer.currentRewards[0].name.match(/\$(\d+)/);
        if (rewardMatch && rewardMatch[1]) {
          const amount = parseInt(rewardMatch[1], 10);
          setCustomerReward({ amount, applied: false });
        }
      }
    }
  }, [isAuthenticated, customer]);
  
  // Get a payment intent when stripe payment method is selected
  useEffect(() => {
    if (selectedPaymentMethod === 'stripe' && items.length > 0 && !clientSecret) {
      const createPaymentIntent = async () => {
        try {
          setIsLoading(true);
          
          const response = await fetch('/api/create-payment-intent', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              items,
              currency
            }),
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            throw new Error(data.details || data.error || 'Could not create payment intent');
          }
          
          setClientSecret(data.clientSecret);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
          setError(errorMessage);
          console.error('Error creating payment intent:', err);
        } finally {
          setIsLoading(false);
        }
      };
      
      createPaymentIntent();
    }
  }, [selectedPaymentMethod, items, clientSecret, currency]);
  
  // Handle customer information input change
  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setCustomerInfo(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error for this field if it exists
    if (customerInfoErrors[name]) {
      setCustomerInfoErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
    
    // For email and phone fields, try to lookup customer info after typing stops
    if ((name === 'email' || name === 'phone') && value.length > 3) {
      // Use a debounce to avoid too many requests while typing
      const debounceTimer = setTimeout(() => {
        lookupCustomerInfo(name, value);
      }, 500);
      
      return () => clearTimeout(debounceTimer);
    }
  };
  
  // Lookup customer information by email or phone
  const lookupCustomerInfo = async (field: string, value: string) => {
    if (!value || value.length < 4) return;
    
    try {
      const response = await fetch(`/api/customers/lookup?${field}=${encodeURIComponent(value)}`);
      
      if (!response.ok) {
        console.error('Failed to lookup customer');
        return;
      }
      
      const data = await response.json();
      
      if (data.found) {
        // Auto-fill form with customer information
        setCustomerInfo({
          email: data.customer.email,
          phone: data.customer.phone,
          fullName: `${data.customer.firstName} ${data.customer.lastName}`,
          address: `${data.customer.address.street}, ${data.customer.address.city}, ${data.customer.address.province}, ${data.customer.address.country} ${data.customer.address.postalCode}`
        });
        
        // Show auto-fill notification to the user
        setShowAutoFillNotification(true);
        
        // Scroll to notification to ensure it's visible
        setTimeout(() => {
          // Find the notification element and scroll to it
          const notificationElement = document.querySelector('.animate-fadeIn');
          if (notificationElement) {
            notificationElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
        
        // Hide notification after 5 seconds
        setTimeout(() => {
          setShowAutoFillNotification(false);
        }, 5000);
      }
    } catch (error) {
      console.error('Error looking up customer:', error);
    }
  };
  
  // Validate customer info form
  const validateCustomerInfo = (): boolean => {
    const errors: Record<string, string> = {};
    
    // Basic validation
    if (!customerInfo.email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(customerInfo.email)) errors.email = 'Email is invalid';
    
    if (!customerInfo.phone) errors.phone = 'Phone number is required';
    if (!customerInfo.fullName) errors.fullName = 'Full name is required';
    if (!customerInfo.address) errors.address = 'Address is required';
    
    setCustomerInfoErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Add function to apply discount code
  const handleApplyDiscount = async () => {
    if (!discountCode.trim()) {
      setDiscountError('Please enter a discount code');
      return;
    }
    
    setApplyingDiscount(true);
    setDiscountError(null);
    
    try {
      const response = await fetch('/api/verify-discount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: discountCode,
          cartTotal: subtotal,
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        setDiscountError(data.error || 'Failed to apply discount code');
        setAppliedDiscount(null);
        return;
      }
      
      // Set the applied discount
      setAppliedDiscount(data.discount);
      
      // Success message
      setDiscountError(null);
      
    } catch (err) {
      console.error('Error applying discount code:', err);
      setDiscountError('An error occurred. Please try again.');
      setAppliedDiscount(null);
    } finally {
      setApplyingDiscount(false);
    }
  };
  
  // Function to remove applied discount
  const handleRemoveDiscount = () => {
    setAppliedDiscount(null);
    setDiscountCode('');
    setDiscountError(null);
  };
  
  // Handle continue to payment
  const handleContinueToPayment = async () => {
    if (validateCustomerInfo()) {
      try {
        setIsLoading(true);
        
        // Parse full name into first and last name for the auth system
        const nameParts = customerInfo.fullName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';
        
        // Create a compatible customer info object for the auth system
        const authCustomerInfo = {
          email: customerInfo.email,
          phone: customerInfo.phone,
          firstName: firstName,
          lastName: lastName,
          address: {
            street: customerInfo.address,
            city: '',
            province: '',
            country: '',
            postalCode: ''
          }
        };
        
        // Attempt to authenticate customer with checkout information
        await loginWithCheckoutInfo(authCustomerInfo);
        
        // Continue to payment step
        setCheckoutStep('payment');
      } catch (error) {
        console.error('Error saving customer info:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };
  
  // Handle successful payment
  const handlePaymentSuccess = (paymentId: string) => {
    // Mark cart as recovered
    markCartAsRecovered(paymentId);
    
    // Clear cart
    clearCart();
    
    // Mark order as placed
    setOrderPlaced(true);
    
    // Payment completed - order is placed
  };
  
  // Add CSS animation for the notification
  useEffect(() => {
    if (showAutoFillNotification) {
      // Create a style element for the animation
      const styleEl = document.createElement('style');
      styleEl.innerHTML = `
        @keyframes fadeInSlideDown {
          0% { opacity: 0; transform: translateY(-10px); }
          100% { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeInSlideDown 0.5s ease forwards;
        }
      `;
      document.head.appendChild(styleEl);
      
      // Clean up
      return () => {
        document.head.removeChild(styleEl);
      };
    }
  }, [showAutoFillNotification]);
  
  // Format card number (still needed for Stripe display)
  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Add space after every 4 digits
    const formatted = digits.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    // Limit to 19 characters (16 digits + 3 spaces)
    return formatted.substring(0, 19);
  };
  
  // Format expiry date as MM/YY (still needed for Stripe display)
  const formatExpiryDate = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Add slash after first 2 digits
    if (digits.length > 2) {
      return `${digits.substring(0, 2)}/${digits.substring(2, 4)}`;
    }
    
    return digits;
  };
  
  // Calculate order totals including discount
  const subtotal = mounted ? items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0) : 0;
  const shipping = subtotal > 0 ? 15 : 0; // $15 shipping fee
  const discountAmount = appliedDiscount ? appliedDiscount.discountAmount : 0;
  const rewardAmount = customerReward.applied ? customerReward.amount : 0;
  const total = subtotal + shipping - discountAmount - rewardAmount;

  // Function to fetch customer rewards
  useEffect(() => {
    const fetchCustomerRewards = async () => {
      if (isAuthenticated && customer?._id) {
        try {
          const response = await fetch(`/api/customers/${customer._id}`);
          if (response.ok) {
            const customerData = await response.json();
            
            // ONLY check for rewards in the currentRewards array
            // Do NOT calculate eligible rewards that haven't been issued yet
            let rewardAmount = 0;
            
            if (customerData.currentRewards && customerData.currentRewards.length > 0) {
              // Extract the dollar amount from the reward name (e.g., "$100 Reward" → 100)
              const match = customerData.currentRewards[0].name.match(/\$(\d+)/);
              if (match && match[1]) {
                rewardAmount = parseInt(match[1], 10);
              }
            }
            
            console.log(`Customer rewards check:
              Current rewards available: $${rewardAmount}
            `);
            
            // Only set rewards if there are current rewards
            setCustomerReward({ 
              amount: rewardAmount, 
              applied: false 
            });
          }
        } catch (error) {
          console.error('Error fetching customer rewards:', error);
        }
      }
    };
    
    fetchCustomerRewards();
  }, [isAuthenticated, customer]);

  // Function to apply reward to order
  const handleApplyReward = () => {
    if (customerReward.amount > 0 && !customerReward.applied) {
      setApplyingReward(true);
      
      // Apply the reward immediately and recalculate the total
      setCustomerReward(prev => ({ ...prev, applied: true }));
      
      // Show processing animation briefly for feedback
      setTimeout(() => {
        setApplyingReward(false);
        
        // Update order summary with new total after discount
        if (orderSummary) {
          setOrderSummary({
            ...orderSummary,
            total: subtotal + shipping - customerReward.amount
          });
        }
      }, 500);
    }
  };
  
  // Stripe appearance options
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#3b82f6',
    },
  };
  
  // Modify the handleBankTransferOrder function to include discount information
  const handleBankTransferOrder = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Calculate final totals including any discounts and rewards
      const finalTotal = subtotal + shipping - discountAmount - (customerReward.applied ? customerReward.amount : 0);
      
      // Prepare order data
      const orderData = {
        customer: {
          _id: customer?._id,
          email: customerInfo.email,
          name: customerInfo.fullName,
          phone: customerInfo.phone
        },
        items: items.map(item => ({
          productId: item.product._id,
          name: item.product.name,
          price: item.product.price,
          quantity: item.quantity,
          imageUrl: item.product.imageUrl || ''
        })),
        shippingAddress: {
          street: customerInfo.address || 'Complete address provided',
          city: 'See street address',
          state: 'See street address', 
          country: 'See street address',
          postalCode: 'See street address'
        },
        subtotal: subtotal,
        shipping: shipping,
        discount: discountAmount + (customerReward.applied ? customerReward.amount : 0),
        discountBreakdown: {
          couponDiscount: discountAmount,
          rewardDiscount: customerReward.applied ? customerReward.amount : 0,
        },
        tax: 0, // Tax is 0 in this case
        total: finalTotal,
        paymentMethod: 'bank-transfer',
        currency: currency,
        reward: customerReward.applied ? {
          id: customer?.currentRewards?.[0]?.id,
          name: customer?.currentRewards?.[0]?.name,
          amount: customerReward.amount
        } : null,
        appliedDiscount: appliedDiscount ? {
          id: appliedDiscount._id,
          code: appliedDiscount.code,
          type: appliedDiscount.type,
          value: appliedDiscount.value,
          amount: appliedDiscount.discountAmount
        } : null
      };
      
      // Place order
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        console.error('Order creation failed:', data);
        throw new Error(data.message || data.error || `Failed to place order: ${response.status}`);
      }
      
      // If a discount was applied, record its usage
      if (appliedDiscount) {
        try {
          await fetch('/api/apply-discount', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              discountId: appliedDiscount._id
            }),
          });
        } catch (discountError) {
          console.error('Error recording discount usage:', discountError);
          // Continue with order completion even if discount recording fails
        }
      }
      
      // If a reward was applied, update the customer's spent rewards
      if (customerReward.applied && customer?._id) {
        try {
          await fetch(`/api/customers/${customer._id}/apply-reward`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              rewardId: customer?.currentRewards?.[0]?.id || Date.now(),
              rewardName: customer?.currentRewards?.[0]?.name || `$${customerReward.amount} Reward`,
              amount: customerReward.amount,
              spentDate: new Date().toISOString().split('T')[0],
              orderId: data.order.orderNumber
            }),
          });
        } catch (rewardError) {
          console.error('Error applying reward:', rewardError);
          // Continue with order completion even if reward application fails
        }
      }
      
      // Set order summary
      setOrderSummary({
        subtotal: subtotal,
        shipping: shipping,
        total: finalTotal,
        date: new Date().toLocaleDateString()
      });
      
      // Set order reference
      setOrderReference(data.order.orderNumber);
      
      // Clear cart
      clearCart();
      
      // Order placed
      setOrderPlaced(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(`Payment failed: ${errorMessage}`);
      console.error('Error processing bank transfer order:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // If using a payment method that has its own handling
  const handleProceedToPayment = () => {
    if (selectedPaymentMethod) {
      if (selectedPaymentMethod === 'bank-transfer') {
        // Process bank transfer order
        handleBankTransferOrder();
      }
      // For Stripe and PayPal, the components handle the payment
    } else {
      setError('Please select a payment method');
    }
  };
  
  // PayPal button options
  const paypalOptions = {
    clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
    currency: currency === 'USD' ? 'USD' : 'EUR',
    intent: 'capture',
    createOrder: async () => {
      try {
        const response = await fetch('/api/create-paypal-order', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            items,
            currency
          }),
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to create PayPal order');
        }
        
        return data.id;
      } catch (error) {
        console.error('Error creating PayPal order:', error);
        setError('Failed to create PayPal order. Please try again.');
      }
    },
    onApprove: async (data: any) => {
      try {
        const response = await fetch('/api/capture-paypal-payment', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ orderID: data.orderID }),
        });
        
        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to capture payment');
        }
        
        // Handle successful payment
        handlePaymentSuccess(result.id);
        
        // Redirect to success page
        router.push('/checkout/success');
      } catch (error) {
        console.error('Error capturing PayPal payment:', error);
        setError('Failed to process payment. Please try again.');
      }
    },
    onError: (err: any) => {
      console.error('PayPal error:', err);
      setError('Payment failed. Please try again.');
    },
    onCancel: () => {
      setError('Payment was cancelled.');
    },
  };
  
  // Render the loading state or error
  if (!mounted) {
    return null; // Wait for component to be mounted on client
  }
  
  // Show customer information form
  if (checkoutStep === 'customer-info') {
    return (
      <div id="customer-info-container" className="container mx-auto px-4 pb-8 bg-[#f8f8f8]">
        <div id="customer-info-wrapper" className="max-w-5xl mx-auto">
          {/* Progress Bar */}
          <CheckoutProgressBar currentStep={checkoutStep} orderPlaced={orderPlaced} />
          {isAuthenticated && customer && (
            <div id="welcome-message-container" className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-blue-700">
                {translations.welcome_back.replace('{name}', customer.firstName)}
              </p>
            </div>
          )}
          

          
          {/* Auto-fill notification - Moved to a more visible position */}
          {showAutoFillNotification && (
            <div id="auto-fill-notification" className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200 shadow-md flex items-center animate-fadeIn">
              <CheckCircle className="text-green-500 mr-2 flex-shrink-0" size={24} />
              <p className="text-green-700 font-medium">
                {translations.auto_fill_notification}
              </p>
            </div>
          )}
          
          <div id="main-content-layout" className="flex flex-col lg:flex-row gap-8">
            <div id="form-column" className="lg:w-2/3">
              <div id="form-container" className="bg-[#f8f8f8] rounded-lg px-6 pb-6 mb-4">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                </h2>
                
                <div id="form-fields-grid" className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div id="email-field-container">
                    <label htmlFor="email" className="block font-dosis text-sm font-medium text-gray-700 mb-1 tracking-wide" style={{ fontSize: '14px', lineHeight: '1.5em', letterSpacing: '0.7px', textTransform: 'capitalize' }}>
                      {translations.email_address}
                    </label>
                    <div id="email-input-wrapper" className="relative">
                      <div id="email-icon-wrapper" className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={16} className="text-gray-400" />
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={customerInfo.email}
                        onChange={handleCustomerInfoChange}
                        className={`pl-10 w-full p-2 rounded-md bg-white focus:ring-blue-500 focus:border-blue-500 ${
                          customerInfoErrors.email ? '' : ''
                        }`}
                        style={{ 
                          borderWidth: '1.5px', 
                          borderStyle: 'solid', 
                          borderColor: customerInfoErrors.email ? '#ef4444' : '#d1d5db' 
                        }}
                      />
                    </div>
                    {customerInfoErrors.email && (
                      <p className="mt-1 text-sm text-red-500">{customerInfoErrors.email}</p>
                    )}
                  </div>
                  
                  <div id="phone-field-container">
                    <label htmlFor="phone" className="block font-dosis text-sm font-medium text-gray-700 mb-1 tracking-wide" style={{ fontSize: '14px', lineHeight: '1.5em', letterSpacing: '0.7px', textTransform: 'capitalize' }}>
                      {translations.phone_number}
                    </label>
                    <div id="phone-input-wrapper" className="relative">
                      <div id="phone-icon-wrapper" className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Phone size={16} className="text-gray-400" />
                      </div>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={customerInfo.phone}
                        onChange={handleCustomerInfoChange}
                        className={`pl-10 w-full p-2 rounded-md bg-white focus:ring-blue-500 focus:border-blue-500 ${
                          customerInfoErrors.phone ? '' : ''
                        }`}
                        style={{ 
                          borderWidth: '1.5px', 
                          borderStyle: 'solid', 
                          borderColor: customerInfoErrors.phone ? '#ef4444' : '#d1d5db' 
                        }}
                      />
                    </div>
                    {customerInfoErrors.phone && (
                      <p className="mt-1 text-sm text-red-500">{customerInfoErrors.phone}</p>
                    )}
                  </div>
                  
                  <div id="full-name-field-container" className="md:col-span-2">
                    <label htmlFor="fullName" className="block font-dosis text-sm font-medium text-gray-700 mb-1 tracking-wide" style={{ fontSize: '14px', lineHeight: '1.5em', letterSpacing: '0.7px', textTransform: 'capitalize' }}>
                      {translations.full_name}
                    </label>
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={customerInfo.fullName}
                      onChange={handleCustomerInfoChange}
                      className={`w-full p-2 rounded-md bg-white focus:ring-blue-500 focus:border-blue-500 ${
                        customerInfoErrors.fullName ? '' : ''
                      }`}
                      style={{ 
                        borderWidth: '1.5px', 
                        borderStyle: 'solid', 
                        borderColor: customerInfoErrors.fullName ? '#ef4444' : '#d1d5db' 
                      }}
                    />
                    {customerInfoErrors.fullName && (
                      <p className="mt-1 text-sm text-red-500">{customerInfoErrors.fullName}</p>
                    )}
                  </div>
                </div>
                
                <div id="address-field-container">
                  <label htmlFor="address" className="block font-dosis text-sm font-medium text-gray-700 mb-1 tracking-wide" style={{ fontSize: '14px', lineHeight: '1.5em', letterSpacing: '0.7px', textTransform: 'capitalize' }}>
                    {translations.shipping_address || 'Shipping Address'}
                  </label>
                  <textarea
                    id="address"
                    name="address"
                    value={customerInfo.address}
                    onChange={handleCustomerInfoChange}
                    rows={3}
                    className={`w-full p-2 rounded-md bg-white focus:ring-blue-500 focus:border-blue-500 resize-vertical ${
                      customerInfoErrors.address ? '' : ''
                    }`}
                    style={{ 
                      borderWidth: '1.5px', 
                      borderStyle: 'solid', 
                      borderColor: customerInfoErrors.address ? '#ef4444' : '#d1d5db' 
                    }}
                  />
                  {customerInfoErrors.address && (
                    <p className="mt-1 text-sm text-red-500">{customerInfoErrors.address}</p>
                  )}
                </div>
                
                <div id="submit-button-container" className="mt-6">
                  <button
                    onClick={handleContinueToPayment}
                    disabled={isLoading}
                    className="w-full text-white rounded-md py-3 px-6 flex items-center justify-center font-medium transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{ background: 'linear-gradient(135deg, #51575F 0%, #1F2937 100%)' }}
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {translations.processing}
                      </span>
                    ) : (
                      translations.continue_to_payment
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Related Products Section - Customer Info Step */}
          {items.length > 0 && (
            <div id="related-products-section" className="mt-8">
              <div id="related-products-container" className="pt-6">
                <RelatedProducts 
                  currentProductIds={items.map(item => item.product._id)} 
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
  
  // Render payment methods (same as before)
  return (
    <div id="payment-step-main-container" className="container mx-auto px-4 pb-8 bg-[#f8f8f8]">
      <div id="payment-step-content-wrapper" className="max-w-5xl mx-auto">
        {/* Progress Bar */}
        <CheckoutProgressBar currentStep={checkoutStep} orderPlaced={orderPlaced} />
        
        {/* Display error if any */}
        {error && (
          <div id="payment-step-error-display" className="mb-6 p-4 bg-red-50 text-red-600 rounded-md">
            {error}
          </div>
        )}
        
        {/* Main content layout */}
                  <div id="payment-step-main-layout" className="flex flex-col lg:flex-row gap-0 lg:gap-8">
                    {/* Left column - Payment Summary */}
          <div id="payment-step-left-column" className="lg:w-2/3">
            <div id="payment-summary-card" className="bg-[#f8f8f8] rounded-lg p-6">
                              <h2 className="font-dosis font-medium text-[18px] leading-[1.05em] tracking-[0.035em] capitalize text-center mb-4">{translations.payment_summary}</h2>
              
                                <div id="payment-summary-items" className="space-y-4 mb-6">
                <div id="subtotal-row" className="flex justify-between">
                  <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center text-gray-600">{translations.subtotal}</span>
                  <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">{orderPlaced ? formatPrice(orderSummary?.subtotal || 0) : formatPrice(subtotal)}</span>
                </div>
                <div id="shipping-row" className="flex justify-between">
                  <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center text-gray-600">{translations.shipping}</span>
                  <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">{orderPlaced ? formatPrice(orderSummary?.shipping || 0) : formatPrice(shipping)}</span>
                </div>
                
                {/* Discount Code Section - Moved from customer info step */}
                {!orderPlaced && (
                  <div id="discount-code-section" className="pt-4 border-t border-gray-200">
                    <h3 className="font-dosis font-normal text-[14px] leading-[1.5em] tracking-[0.05em] capitalize text-center mb-3 flex items-center">
                      <span className="mr-2 text-blue-500">%</span>
                      {translations.discount_code}
                    </h3>
                    
                    <div id="discount-code-input-row" className="flex items-center space-x-2 mb-3">
                      <input
                        type="text"
                        value={discountCode}
                        onChange={(e) => setDiscountCode(e.target.value)}
                        className="w-56 p-2 border-[1.5px] rounded-md bg-white focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={handleApplyDiscount}
                        disabled={applyingDiscount || !discountCode.trim()}
                        className="bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                      >
                        {applyingDiscount ? translations.applying : translations.apply}
                      </button>
                    </div>
                    
                    {discountError && (
                      <p className="mb-3 text-sm text-red-600">{discountError}</p>
                    )}
                    
                    {appliedDiscount && (
                      <div id="applied-discount-card" className="mb-3 p-3 bg-green-50 border border-green-200 rounded-md">
                        <div id="applied-discount-content" className="flex justify-between items-center">
                          <div id="applied-discount-info">
                            <p className="font-medium text-green-700">
                              {appliedDiscount.code} <span className="text-sm font-normal">({appliedDiscount.description})</span>
                            </p>
                            <p className="text-sm text-green-600">
                              -{appliedDiscount.type === 'percentage' ? `${appliedDiscount.value}%` : `$${appliedDiscount.value}`}
                            </p>
                          </div>
                          <button
                            onClick={handleRemoveDiscount}
                            className="text-sm text-gray-500 hover:text-red-500"
                          >
                            {translations.remove}
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Add discount code info in Payment Summary */}
                {appliedDiscount && !orderPlaced && (
                  <div id="discount-summary-row" className="flex justify-between text-green-600">
                    <span className="font-dosis font-normal text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">{translations.discount.replace('{code}', appliedDiscount.code)}</span>
                    <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">-{formatPrice(appliedDiscount.discountAmount)}</span>
                  </div>
                )}
                
                {/* Add reward discount info in Payment Summary */}
                {(orderPlaced ? customerReward.applied : customerReward.amount > 0) && (
                  <div id="reward-section" className="pt-2 border-t border-dashed border-gray-200 mt-2">
                    <div id="reward-available-row" className="flex justify-between items-center">
                      <span className="font-dosis font-normal text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center text-gray-600">{translations.rewards_available}</span>
                      <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center text-green-600">{formatPrice(customerReward.amount)}</span>
                    </div>
                    
                    {customerReward.applied && (
                      <div id="applied-reward-row" className="flex justify-between items-center mt-1 text-green-600">
                        <span className="font-dosis font-normal text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">{translations.applied_reward}</span>
                        <span className="font-dosis font-medium text-[16px] leading-[1.3125em] tracking-[0.04375em] capitalize text-center">-{formatPrice(customerReward.amount)}</span>
                      </div>
                    )}
                    
                    {!orderPlaced && !customerReward.applied && (
                      <div id="apply-reward-button-container" className="flex justify-end mt-1">
                        <button
                          onClick={handleApplyReward}
                          disabled={applyingReward}
                          className="px-3 py-1 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                        >
                          {applyingReward ? (
                            <>
                              <div className="animate-spin h-3 w-3 border-2 border-white rounded-full border-t-transparent mr-1"></div>
                              {translations.processing}
                            </>
                          ) : (
                            translations.apply_reward
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                )}
                
                <div id="total-row" className="pt-4 border-t border-gray-200 flex justify-between font-semibold">
                  <span className="font-dosis font-medium text-[18px] leading-[1.05em] tracking-[0.035em] capitalize text-center">{translations.total}</span>
                  <span className="font-dosis font-medium text-[17px] leading-[1.235em] tracking-[0.06em] capitalize text-center">{orderPlaced ? formatPrice(orderSummary?.total || 0) : formatPrice(total)}</span>
                </div>
                
                {!orderPlaced && customerReward.amount > 0 && !customerReward.applied && (
                  <div id="reward-save-message" className="mt-1 text-sm text-green-600 text-right">
                    {translations.apply_reward_to_save.replace('{amount}', formatPrice(customerReward.amount))}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Right column - Payment Method Selection or Order Confirmation */}
          <div id="payment-step-right-column" className="lg:w-1/3">
            {orderPlaced ? (
              <div id="order-confirmation-card" className="bg-white rounded-lg shadow-md p-6 mb-4 space-y-6">
                <div id="order-success-message" className="p-4 bg-blue-50 rounded-md">
                  <h3 className="font-semibold text-blue-800 mb-2">{translations.order_placed_successfully}</h3>
                  <p className="text-blue-700 mb-2">{translations.your_order_reference.replace('{reference}', orderReference)}</p>
                  <p className="text-blue-700 text-sm">{translations.bank_transfer_instruction}</p>
                </div>
                
                <div id="bank-details-card" className="border border-gray-200 rounded-md p-4">
                  <h3 className="font-semibold mb-3">{translations.our_bank_details}</h3>
                  <div id="bank-details-list" className="space-y-2 text-sm">
                    <p><span className="font-medium">{translations.name}</span> SAFIZIAULLAH</p>
                    <p><span className="font-medium">{translations.bank}</span> UniCredit</p>
                    <p><span className="font-medium">{translations.account_number}</span> ************</p>
                    <p><span className="font-medium">{translations.sort_code}</span> 01105</p>
                    <p><span className="font-medium">{translations.iban}</span> IT04N0200801105************</p>
                    <p><span className="font-medium">{translations.bic}</span> UNCRITM1AA5</p>
                  </div>
                </div>
                
                <div id="order-details-card" className="border border-gray-200 rounded-md p-4">
                  <h3 className="font-semibold mb-3">{translations.order_details}</h3>
                  <div id="order-details-list" className="space-y-2 text-sm">
                    <p><span className="font-medium">{translations.order_reference}</span> {orderReference}</p>
                    <p><span className="font-medium">{translations.order_date}</span> {orderSummary?.date || new Date().toLocaleDateString()}</p>
                    <p><span className="font-medium">{translations.subtotal}</span> {formatPrice(orderSummary?.subtotal || 0)}</p>
                    <p><span className="font-medium">{translations.shipping}</span> {formatPrice(orderSummary?.shipping || 0)}</p>
                    
                    {/* Show applied discount in order confirmation */}
                    {appliedDiscount && (
                      <p>
                        <span className="font-medium">{translations.discount.replace('{code}', appliedDiscount.code)}</span> 
                        <span className="text-green-600"> -{formatPrice(appliedDiscount.discountAmount)}</span>
                      </p>
                    )}
                    
                    {customerReward.applied && (
                      <p>
                        <span className="font-medium">{translations.reward_applied}</span> 
                        <span className="text-green-600"> -{formatPrice(customerReward.amount)}</span>
                      </p>
                    )}
                    <p className="font-medium text-[18px]">{translations.total}: {formatPrice(orderSummary?.total || 0)}</p>
                  </div>
                </div>
                
                <div id="order-confirmation-footer" className="mt-6 space-y-4">
                  <p className="text-sm text-gray-600">
                    {translations.payment_confirmation_message}
                  </p>
                  <button
                    onClick={() => {
                      router.push('/');
                    }}
                    className="w-full py-2 px-4 bg-gradient-to-tl from-[#51575F] to-[#1F2937] text-white rounded-md hover:from-[#6B7280] hover:to-[#374151] hover:shadow-lg active:from-[#4B5563] active:to-[#111827] active:scale-95 active:shadow-sm transition-all duration-300"
                  >
                    {translations.return_to_home}
                  </button>
                </div>
              </div>
            ) : (
              <div id="payment-method-selection-card" className="rounded-lg p-6 mb-4" style={{backgroundColor: '#f8f8f8'}}>
                                  <h3 className="font-dosis font-medium text-[16px] leading-[1.05em] tracking-[0.035em] capitalize text-center mb-4">{translations.select_payment_method}</h3>
                
                {/* Apple Pay */}
                <div 
                  id="apple-pay-option"
                  onClick={() => setSelectedPaymentMethod('apple-pay')}
                  className={`p-4 border rounded-md mb-3 flex items-center cursor-pointer transition-colors ${
                    selectedPaymentMethod === 'apple-pay' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-white'
                  }`}
                >
                  <div id="apple-pay-icon" className="flex items-center justify-center mr-3 text-gray-600 w-6 h-6">
                    <AiOutlineApple size={24} />
                  </div>
                  <div id="apple-pay-text">
                    <p className="font-dosis font-medium">Apple Pay</p>
                    <p className="font-dosis text-xs text-gray-500 tracking-wider">Quick and secure checkout with Apple Pay</p>
                  </div>
                </div>
                
                {/* Credit/Debit Card (Stripe) */}
                <div 
                  id="stripe-option"
                  onClick={() => setSelectedPaymentMethod('stripe')}
                  className={`p-4 border rounded-md mb-3 flex items-center cursor-pointer transition-colors ${
                    selectedPaymentMethod === 'stripe' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-white'
                  }`}
                >
                  <CreditCard className="mr-3 text-gray-600" size={24} />
                  <div id="stripe-text">
                    <p className="font-dosis font-medium">{translations.credit_debit_card}</p>
                    <p className="font-dosis text-xs text-gray-500 tracking-wider">{translations.secure_payment_via_stripe}</p>
                  </div>
                </div>
                
                {/* PayPal */}
                <div 
                  id="paypal-option"
                  onClick={() => setSelectedPaymentMethod('paypal')}
                  className={`p-4 border rounded-md mb-3 flex items-center cursor-pointer transition-colors ${
                    selectedPaymentMethod === 'paypal' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-white'
                  }`}
                >
                  <Wallet className="mr-3 text-gray-600" size={24} />
                  <div id="paypal-text">
                    <p className="font-dosis font-medium">{translations.paypal}</p>
                    <p className="font-dosis text-xs text-gray-500 tracking-wider">{translations.pay_with_paypal}</p>
                  </div>
                </div>
                
                {/* Bank Transfer */}
                <div 
                  id="bank-transfer-option"
                  onClick={() => setSelectedPaymentMethod('bank-transfer')}
                  className={`p-4 border rounded-md mb-3 flex items-center cursor-pointer transition-colors ${
                    selectedPaymentMethod === 'bank-transfer' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-white'
                  }`}
                >
                  <Building2 className="mr-3 text-gray-600" size={24} />
                  <div id="bank-transfer-text">
                    <p className="font-dosis font-medium">{translations.direct_bank_transfer}</p>
                    <p className="font-dosis text-xs text-gray-500 tracking-wider">{translations.pay_directly_to_bank}</p>
                  </div>
                </div>
                
                {/* Payment-specific components remain unchanged */}
                {/* Bank Transfer Button */}
                {selectedPaymentMethod === 'bank-transfer' && (
                <div id="bank-transfer-component" className="mt-4">
                      <button
                        onClick={handleBankTransferOrder}
                        disabled={isLoading}
                        className={`w-full py-3 px-4 rounded-md text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#a3003f]/50 
                          ${isLoading ? 'bg-gray-400 cursor-not-allowed opacity-50' : 'bg-gradient-to-tl from-[#51575F] to-[#1F2937] hover:from-[#424751] hover:to-[#111827] active:from-[#383c44] active:to-[#0f172a]'}
                          `}
                      >
                        {isLoading ? translations.processing : translations.place_order_show_bank}
                      </button>
                    <p className="font-dosis mt-4 text-sm text-gray-600">
                      {translations.bank_transfer_explanation}
                    </p>
                    </div>
                  )}
              
              {selectedPaymentMethod === 'paypal' && (
                <div id="paypal-component" className="mt-4">
                  <PayPalScriptProvider options={{ 
                    clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
                    currency: currency === 'USD' ? 'USD' : 'EUR',
                    'disable-funding': 'venmo,paylater'
                  }}>
                    <PayPalButtons
                      style={{ layout: 'vertical' }}
                      {...paypalOptions}
                      className="w-full"
                    />
                  </PayPalScriptProvider>
                </div>
              )}
              
              {selectedPaymentMethod === 'stripe' && clientSecret && (
                <div id="stripe-component" className="mt-4">
                  <Elements stripe={stripePromise} options={{ clientSecret, appearance }}>
                    <StripePaymentElement
                      clientSecret={clientSecret}
                      onSuccess={handlePaymentSuccess}
                      onError={setError}
                    />
                  </Elements>
                </div>
              )}
              
              {selectedPaymentMethod === 'apple-pay' && (
                <div id="apple-pay-component" className="mt-4">
                  <button
                    onClick={async () => {
                      try {
                        setIsLoading(true);
                        
                        // Create Apple Pay session via Stripe
                        const response = await fetch('/api/create-payment-intent', {
                          method: 'POST',
                          headers: { 'Content-Type': 'application/json' },
                          body: JSON.stringify({ 
                            items,
                            currency,
                            paymentMethod: 'apple-pay'
                          }),
                        });
                        
                        const data = await response.json();
                        
                        if (!response.ok) {
                          throw new Error(data.details || data.error || 'Could not process Apple Pay payment');
                        }
                        
                        // Simulate completion for now (actual implementation would use the Apple Pay JS API)
                        setTimeout(() => {
                          handlePaymentSuccess(data.clientSecret?.split('_secret')[0] || 'apple-pay-test');
                          // In production, would use actual payment confirmation
                        }, 1500);
                      } catch (err) {
                        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
                        setError(errorMessage);
                        console.error('Error with Apple Pay:', err);
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                    disabled={isLoading}
                    className="w-full py-3 px-4 rounded-md text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-black/25 bg-black"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-gray-300 border-t-white rounded-full animate-spin mr-3"></div>
                        {translations.processing}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <span>Pay with</span>
                        <div className="ml-2 flex items-center">
                          <FaApple className="mr-1" size={18} />
                          <span className="font-medium text-white">Pay</span>
                        </div>
                      </div>
                    )}
                  </button>
                </div>
              )}
              
              <div id="secure-payment-notice" className="mt-4 text-center text-sm text-gray-500">
                <p className="font-dosis tracking-wider">{translations.secure_payment_processing}</p>
              </div>
            </div>
            )}
          </div>
        </div>
        
        {/* Related Products Section - Show on payment step */}
        {items.length > 0 && (
          <div id="related-products-section-payment" className="mt-8">
            <div id="related-products-container-payment" className="pt-6">
              <RelatedProducts 
                currentProductIds={items.map(item => item.product._id)} 
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Main export that uses Suspense boundary
export default function CheckoutPage() {
  return (
    <Suspense fallback={<CheckoutSkeleton />}>
      <CheckoutContent />
    </Suspense>
  );
} 