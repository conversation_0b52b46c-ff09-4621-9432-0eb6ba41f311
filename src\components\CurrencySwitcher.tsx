'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { DollarSign, Euro, Globe } from 'lucide-react';

type SupportedCurrency = 'USD' | 'EUR';
type SupportedLanguage = 'English' | 'French' | 'Italian';

interface CurrencySwitcherProps {
  iconType?: 'currency' | 'language';
  textClassName?: string;
  dropdownClassName?: string;
  iconColor?: string;
  isHeroSection?: boolean;
}

export default function CurrencySwitcher({ iconType = 'currency', textClassName, dropdownClassName, iconColor, isHeroSection }: CurrencySwitcherProps) {
  const { currency, setCurrency } = useCurrency();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>('English');

  useEffect(() => {
    if (typeof window !== 'undefined' && iconType === 'language') {
      const savedLanguage = localStorage.getItem('preferredLanguage');
      if (savedLanguage && (savedLanguage === 'English' || savedLanguage === 'French' || savedLanguage === 'Italian')) {
        setSelectedLanguage(savedLanguage as SupportedLanguage);
      }
    }
  }, [iconType]);

  useEffect(() => {
    if (typeof window !== 'undefined' && iconType === 'language') {
      localStorage.setItem('preferredLanguage', selectedLanguage);
    }
  }, [selectedLanguage, iconType]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    // Add event listener when dropdown is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (option: SupportedCurrency | SupportedLanguage) => {
    if (iconType === 'language') {
      setSelectedLanguage(option as SupportedLanguage);
    } else {
      setCurrency(option as SupportedCurrency);
    }
    setIsOpen(false);
  };
  
  const renderIcon = () => {
    const iconProps = { size: 16, color: iconColor };
    if (iconType === 'language') {
      return <Globe {...iconProps} />;
    } else {
      return currency === 'USD' ? <DollarSign {...iconProps} /> : <Euro {...iconProps} />;
    }
  };

  const renderText = () => {
    if (iconType === 'language') {
      return selectedLanguage;
    } else {
      return currency;
    }
  };

  const options = iconType === 'language' 
    ? ['English', 'French', 'Italian'] 
    : ['USD', 'EUR'];

  const currentSelectedOption = iconType === 'language' ? selectedLanguage : currency;

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className={`flex items-center space-x-1 transition-colors py-1 px-2 pl-1 pr-0 rounded-md ${
          isHeroSection
            ? 'text-white hover:text-blue-300 hover:bg-white/10'
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
        }`}
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        {renderIcon()}
        <span className={textClassName}>{renderText()}</span>
        <svg 
          className={`w-3 h-3 transform ${isOpen ? 'rotate-180' : ''} transition-transform`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      
      {isOpen && (
        <div
          className={`absolute z-10 right-0 mt-2 w-32 rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 focus:outline-none ${dropdownClassName || 'bg-white'}`}
          role="menu"
          aria-orientation="vertical"
        >
          {options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleOptionSelect(option as SupportedCurrency | SupportedLanguage)}
              className={`w-full text-left px-4 py-2 pl-1 pr-0 text-sm flex items-center space-x-2 ${
                isHeroSection
                  ? currentSelectedOption === option
                    ? 'bg-white/20 text-white'
                    : 'text-white hover:bg-white/10'
                  : currentSelectedOption === option
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-700 hover:bg-gray-100'
              }`}
              role="menuitem"
            >
              {iconType === 'language' ? <Globe size={16} color={iconColor} /> : (option === 'USD' ? <DollarSign size={16} color={iconColor} /> : <Euro size={16} color={iconColor} />)}
              <span className={textClassName}>{option}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 