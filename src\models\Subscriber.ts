import mongoose, { Schema, Document } from 'mongoose';

// Define the Subscriber interface
export interface ISubscriber extends Document {
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

// Define the Subscriber schema
const SubscriberSchema = new Schema<ISubscriber>(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true, lowercase: true, trim: true },
  },
  { timestamps: true }
);

// Check if model exists before creating (to avoid model overwrite during hot reloading)
const Subscriber = mongoose.models.Subscriber || mongoose.model<ISubscriber>('Subscriber', SubscriberSchema);

export default Subscriber; 