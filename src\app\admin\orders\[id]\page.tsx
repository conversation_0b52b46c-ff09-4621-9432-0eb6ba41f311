'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Package, Truck, CreditCard, User, MapPin, Calendar, Clock, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

// Define Order types (similar to the ones in the orders page)
type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

type OrderItem = {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  imageUrl?: string;
};

type Order = {
  _id: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
  };
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: 'paid' | 'unpaid' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
};

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [newStatus, setNewStatus] = useState<OrderStatus | ''>('');
  
  // Fetch order data
  useEffect(() => {
    fetchOrder();
  }, [params.id]);
  
  const fetchOrder = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const orderId = params.id;
      
      // Fetch order from API
      const response = await fetch(`/api/orders/${orderId}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch order');
      }
      
      setOrder(data.order);
      setNewStatus(data.order.status);
    } catch (err) {
      setError('Failed to fetch order details. Please try again later.');
      console.error('Error fetching order:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const updateOrderStatus = async () => {
    if (!newStatus || newStatus === order?.status) return;
    
    setUpdatingStatus(true);
    
    try {
      // Update order status via API
      const response = await fetch(`/api/orders/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to update order status');
      }
      
      // Update local state with the updated order
      setOrder(data.order);
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setUpdatingStatus(false);
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-500" />;
      case 'processing':
        return <RefreshCw className="text-blue-500" />;
      case 'shipped':
        return <Truck className="text-purple-500" />;
      case 'delivered':
        return <CheckCircle className="text-green-500" />;
      case 'cancelled':
        return <AlertCircle className="text-red-500" />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  if (loading) {
    return (
      <div className="p-6 flex justify-center">
        <div className="animate-spin h-10 w-10 border-4 border-blue-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }
  
  if (error || !order) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 text-red-600">
          {error || 'Order not found'}
        </div>
        <div className="mt-4">
          <Link 
            href="/admin/orders" 
            className="text-blue-600 hover:text-blue-800 inline-flex items-center"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Orders
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <Link 
          href="/admin/orders" 
          className="text-blue-600 hover:text-blue-800 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Orders
        </Link>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold mb-2">{order.orderNumber}</h1>
          <div className="flex items-center text-gray-500">
            <Calendar size={16} className="mr-1" />
            <span className="mr-3">Placed on {formatDate(order.createdAt)}</span>
          </div>
        </div>
        <div className="mt-4 md:mt-0 flex items-center">
          <div className="flex items-center mr-4">
            <span className="mr-2">Status:</span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center ${getStatusColor(order.status)}`}>
              {getStatusIcon(order.status)}
              <span className="ml-1">{order.status.charAt(0).toUpperCase() + order.status.slice(1)}</span>
            </span>
          </div>
          <div className="flex items-center">
            <select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value as OrderStatus)}
              className="mr-2 px-3 py-1 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Update Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <button 
              onClick={updateOrderStatus}
              disabled={updatingStatus || !newStatus || newStatus === order.status}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {updatingStatus ? 'Updating...' : 'Update'}
            </button>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Customer Information */}
        <div className="bg-white rounded-md shadow p-4">
          <div className="flex items-center mb-3">
            <User size={18} className="mr-2 text-blue-600" />
            <h2 className="text-lg font-medium">Customer Information</h2>
          </div>
          <div className="text-gray-700">
            <p className="font-medium">{order.customer.name}</p>
            <p>{order.customer.email}</p>
            {order.customer.phone && <p>{order.customer.phone}</p>}
          </div>
        </div>
        
        {/* Shipping Information */}
        <div className="bg-white rounded-md shadow p-4">
          <div className="flex items-center mb-3">
            <MapPin size={18} className="mr-2 text-blue-600" />
            <h2 className="text-lg font-medium">Shipping Address</h2>
          </div>
          <div className="text-gray-700">
            <p>{order.shippingAddress.street}</p>
            <p>
              {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
            </p>
            <p>{order.shippingAddress.country}</p>
          </div>
        </div>
        
        {/* Payment Information */}
        <div className="bg-white rounded-md shadow p-4">
          <div className="flex items-center mb-3">
            <CreditCard size={18} className="mr-2 text-blue-600" />
            <h2 className="text-lg font-medium">Payment Information</h2>
          </div>
          <div className="text-gray-700">
            <p><span className="font-medium">Method:</span> {order.paymentMethod}</p>
            <p>
              <span className="font-medium">Status:</span> 
              <span className={order.paymentStatus === 'paid' ? 'text-green-600' : 
                  order.paymentStatus === 'refunded' ? 'text-red-600' : 'text-yellow-600'}>
                {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
              </span>
            </p>
          </div>
        </div>
      </div>
      
      {/* Tracking Information */}
      {order.status === 'shipped' || order.status === 'delivered' ? (
        <div className="bg-white rounded-md shadow p-4 mb-6">
          <div className="flex items-center mb-3">
            <Truck size={18} className="mr-2 text-blue-600" />
            <h2 className="text-lg font-medium">Tracking Information</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-500">Carrier</p>
              <p className="font-medium">{order.carrier || 'Not available'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Tracking Number</p>
              <p className="font-medium">{order.trackingNumber || 'Not available'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Estimated Delivery</p>
              <p className="font-medium">{order.estimatedDelivery || 'Not available'}</p>
            </div>
          </div>
        </div>
      ) : null}
      
      {/* Order Items */}
      <div className="bg-white rounded-md shadow p-4 mb-6">
        <div className="flex items-center mb-4">
          <Package size={18} className="mr-2 text-blue-600" />
          <h2 className="text-lg font-medium">Order Items</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {order.items.map((item, index) => (
                <tr key={index}>
                  <td className="px-4 py-4 flex items-center">
                    {item.imageUrl ? (
                      <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 mr-3">
                        <Image
                          src={item.imageUrl.startsWith('http') ? item.imageUrl : `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN || ''}/${item.imageUrl}`}
                          alt={item.name}
                          width={48}
                          height={48}
                          className="h-full w-full object-cover object-center"
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 bg-gray-100 mr-3"></div>
                    )}
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">{item.name}</h3>
                      <p className="text-sm text-gray-500">ID: {item.productId}</p>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-right text-sm text-gray-700">${item.price.toFixed(2)}</td>
                  <td className="px-4 py-4 text-right text-sm text-gray-700">{item.quantity}</td>
                  <td className="px-4 py-4 text-right text-sm font-medium text-gray-900">${(item.price * item.quantity).toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Order Summary */}
      <div className="bg-white rounded-md shadow p-4 mb-6">
        <h2 className="text-lg font-medium mb-4">Order Summary</h2>
        <div className="border-t pt-4">
          <div className="flex justify-between py-1 text-sm">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">${order.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between py-1 text-sm">
            <span className="text-gray-600">Shipping</span>
            <span className="font-medium">${order.shipping.toFixed(2)}</span>
          </div>
          <div className="flex justify-between py-1 text-sm">
            <span className="text-gray-600">Tax</span>
            <span className="font-medium">${order.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between py-3 text-base font-medium border-t mt-2">
            <span>Total</span>
            <span>${order.total.toFixed(2)}</span>
          </div>
        </div>
      </div>
      
      {/* Order Notes */}
      {order.notes && (
        <div className="bg-white rounded-md shadow p-4 mb-6">
          <h2 className="text-lg font-medium mb-2">Notes</h2>
          <p className="text-gray-700">{order.notes}</p>
        </div>
      )}
    </div>
  );
} 