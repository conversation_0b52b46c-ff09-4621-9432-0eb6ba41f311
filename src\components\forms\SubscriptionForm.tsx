'use client';

import { useState, FormEvent } from 'react';
import { toast } from 'sonner';
import { Mail, User, Loader2, ArrowR<PERSON> } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

export function SubscriptionForm() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{name?: string; email?: string}>({});
  const [touched, setTouched] = useState<{name?: boolean; email?: boolean}>({});
  const { translations } = useLanguage();

  const validateField = (fieldName: string, value: string) => {
    const newErrors = { ...errors };
    
    if (fieldName === 'name') {
      if (!value.trim()) {
        newErrors.name = 'Name is required';
      } else if (value.trim().length < 2) {
        newErrors.name = 'Name must be at least 2 characters';
      } else {
        delete newErrors.name;
      }
    }
    
    if (fieldName === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!value.trim()) {
        newErrors.email = 'Email is required';
      } else if (!emailRegex.test(value)) {
        newErrors.email = 'Please enter a valid email address';
      } else {
        delete newErrors.email;
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBlur = (fieldName: string) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    const value = fieldName === 'name' ? name : email;
    validateField(fieldName, value);
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    // Mark all fields as touched
    setTouched({ name: true, email: true });
    
    // Validate all fields
    const isNameValid = validateField('name', name);
    const isEmailValid = validateField('email', email);
    
    if (!isNameValid || !isEmailValid) {
      toast.error("Please fix the errors before submitting.");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim(), email: email.trim() }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || "You have successfully subscribed to our newsletter.");
        setName('');
        setEmail('');
        setTouched({});
        setErrors({});
      } else {
        toast.error(data.message || "Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error('Subscription submission error:', error);
      toast.error("An unexpected error occurred. Please try again later.");
    }
    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5">
      {/* Name Input */}
      <div className="relative">
        <label htmlFor="name" className="sr-only">
          Your Name
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <User className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            onBlur={() => handleBlur('name')}
            placeholder={translations.your_name}
            required
            aria-invalid={touched.name && errors.name ? 'true' : 'false'}
            aria-describedby={touched.name && errors.name ? 'name-error' : undefined}
            className={`
              custom-placeholder
              w-full pl-12 pr-4 py-4 
              bg-white border rounded-xl 
              text-base sm:text-lg text-gray-700 
              placeholder-gray-400 
              transition-all duration-200 ease-in-out
              focus:outline-none focus:ring-0
              ${touched.name && errors.name 
                ? 'border-red-400 focus:border-red-500 shadow-red-100' 
                : 'border-gray-200 focus:border-[#D3821F] hover:border-gray-300'
              }
              focus:shadow-lg focus:shadow-[#D3821F]/10
              hover:shadow-md
              font-medium
              min-h-[56px] sm:min-h-[60px]
            `}
          />
        </div>
        {touched.name && errors.name && (
          <p id="name-error" className="mt-2 text-sm text-red-600 font-medium" role="alert">
            {errors.name}
          </p>
        )}
      </div>

      {/* Email Input */}
      <div className="relative">
        <label htmlFor="email" className="sr-only">
          Email Address
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <Mail className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onBlur={() => handleBlur('email')}
            placeholder={translations.your_email}
            required
            aria-invalid={touched.email && errors.email ? 'true' : 'false'}
            aria-describedby={touched.email && errors.email ? 'email-error' : undefined}
            className={`
              custom-placeholder
              w-full pl-12 pr-4 py-4 
              bg-white border rounded-xl 
              text-base sm:text-lg text-gray-700 
              placeholder-gray-400 
              transition-all duration-200 ease-in-out
              focus:outline-none focus:ring-0
              ${touched.email && errors.email 
                ? 'border-red-400 focus:border-red-500 shadow-red-100' 
                : 'border-gray-200 focus:border-[#D3821F] hover:border-gray-300'
              }
              focus:shadow-lg focus:shadow-[#D3821F]/10
              hover:shadow-md
              font-medium
              min-h-[56px] sm:min-h-[60px]
            `}
          />
        </div>
        {touched.email && errors.email && (
          <p id="email-error" className="mt-2 text-sm text-red-600 font-medium" role="alert">
            {errors.email}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-2">
        <button 
          type="submit" 
          disabled={isLoading || Object.keys(errors).length > 0} 
          className="subscribe-button"
        >
          {isLoading && <Loader2 className="loading-icon" />}
          {isLoading ? translations.subscribing : (
            <>
              {translations.subscribe_now}
              <ArrowRight className="arrow-icon" style={{ width: '20px', height: '20px' }} />
            </>
          )}
        </button>
      </div>

      {/* Privacy Notice */}
      <div className="pt-2">
        <p className="privacy-notice-text text-sm sm:text-base text-gray-600 text-center leading-relaxed">
          {translations.no_spam_notice}
        </p>
      </div>
    </form>
  );
} 