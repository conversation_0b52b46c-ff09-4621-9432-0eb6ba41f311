import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Subscriber from '@/models/Subscriber';
import { sendEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const { name, email } = await request.json();

    if (!name || !email) {
      return NextResponse.json({ message: 'Name and email are required' }, { status: 400 });
    }

    // Check if email already exists
    const existingSubscriber = await Subscriber.findOne({ email });
    if (existingSubscriber) {
      return NextResponse.json({ message: 'Email already subscribed' }, { status: 409 });
    }

    const newSubscriber = new Subscriber({ name, email });
    await newSubscriber.save();

    // Send welcome email
    const emailSent = await sendEmail({
      to: email,
      subject: 'Welcome to Afghan Int\'l Gems!',
      html: `<h3>Hi ${name},</h3><p>Thank you for subscribing to our newsletter. You\'ll be the first to know about new arrivals, special offers, and more.</p><p>Warmly,</p><p>The Afghan Int\'l Gems Team</p>`,
    });

    if (!emailSent) {
      // Log error but don't fail the subscription if email fails
      console.error(`Failed to send welcome email to ${email}`);
    }

    return NextResponse.json({ message: 'Subscribed successfully', subscriber: newSubscriber }, { status: 201 });
  } catch (error) {
    console.error('Subscription error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

export async function GET() {
  try {
    await dbConnect();

    const subscribers = await Subscriber.find({}).sort({ createdAt: -1 }); // Fetch all, sort by newest

    return NextResponse.json({ subscribers }, { status: 200 });
  } catch (error) {
    console.error('Error fetching subscribers:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
} 