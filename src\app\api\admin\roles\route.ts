import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Role } from '@/models/User';

// GET /api/admin/roles - Get all roles
export async function GET() {
  try {
    await dbConnect();
    
    const roles = await Role.find().sort({ name: 1 }).lean();
    
    return NextResponse.json(roles);
  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    );
  }
}

// POST /api/admin/roles - Create a new role
export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const roleData = await req.json();
    
    // Validate
    if (!roleData.name) {
      return NextResponse.json(
        { error: 'Role name is required' },
        { status: 400 }
      );
    }
    
    // Check if role with this name already exists
    const existingRole = await Role.findOne({ name: roleData.name });
    if (existingRole) {
      return NextResponse.json(
        { error: 'Role with this name already exists' },
        { status: 409 }
      );
    }
    
    // Create the role
    const newRole = new Role(roleData);
    await newRole.save();
    
    return NextResponse.json(newRole, { status: 201 });
  } catch (error) {
    console.error('Error creating role:', error);
    return NextResponse.json(
      { error: 'Failed to create role' },
      { status: 500 }
    );
  }
} 