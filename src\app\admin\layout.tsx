'use client';

import React, { useState } from 'react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import RequireAuth from '@/components/admin/RequireAuth';
import PagePermissionGuard from '@/components/admin/PagePermissionGuard';
import { usePathname } from 'next/navigation';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isLoginPage = pathname === '/admin/login';
  
  // Add sidebar state management
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  // Toggle sidebar function
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // If we're on the login page, don't wrap with RequireAuth
  if (isLoginPage) {
    return (
      <div className="flex h-screen bg-gray-100">
        <div className="flex-1 overflow-auto">
          <main>{children}</main>
        </div>
      </div>
    );
  }

  // Check for auth-exempt pages (login, forgot password, reset password)
  const isAuthExemptPage = 
    pathname === '/admin/login' || 
    pathname === '/admin/forgot-password' || 
    pathname.startsWith('/admin/reset-password');
  
  // If we're on an auth-exempt page, don't wrap with RequireAuth
  if (isAuthExemptPage) {
    return (
      <div className="flex h-screen bg-gray-100">
        <div className="flex-1 overflow-auto">
          <main>{children}</main>
        </div>
      </div>
    );
  }

  // For all other admin routes, use authentication and the sidebar
  return (
    <RequireAuth>
      <PagePermissionGuard>
        <div className="flex h-screen bg-gray-100">
          {/* Sidebar - Pass down the state and toggle function */}
          <AdminSidebar 
            isSidebarOpen={isSidebarOpen} 
            toggleSidebar={toggleSidebar} 
          />
          
          {/* Main Content - Adjust based on sidebar state */}
          <div 
            className={`transition-all duration-300 ease-in-out flex-1 overflow-auto ${
              isSidebarOpen ? 'ml-64' : 'ml-0'
            }`}
          >
            <header className="bg-white shadow-sm">
              <div className="px-6 py-4">
                <h2 className="text-xl font-semibold text-gray-800">Admin Panel</h2>
              </div>
            </header>
            <main className="p-6">
              {children}
            </main>
          </div>
        </div>
      </PagePermissionGuard>
    </RequireAuth>
  );
} 