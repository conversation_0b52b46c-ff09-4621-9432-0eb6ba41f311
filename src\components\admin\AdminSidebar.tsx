'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Package, Plus, List, Home, ChevronDown, ChevronRight, LogOut, Settings, ShoppingCart, Users, Tag, BarChart, FileText, MessageSquare, RotateCcw, ShoppingBag, Shield, ChevronLeft, ImageIcon, Mail, Bell } from 'lucide-react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import PermissionGuard from './PermissionGuard';

// Add props to receive the sidebar state and a function to toggle it
interface AdminSidebarProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}

export default function AdminSidebar({ isSidebarOpen, toggleSidebar }: AdminSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { logout, hasPermission } = useAdminAuth();
  // Remove the internal state for the sidebar
  const [expandedMenus, setExpandedMenus] = useState({
    productManagement: true,
    orderManagement: false,
    dashboardAnalytics: false,
    customerManagement: false,
    marketingPromotions: false,
    settingsConfigurations: false,
  });

  // Check if user has any permissions for a resource group
  const hasAnyPermission = (resource: string) => {
    return hasPermission(resource, 'view') || 
           hasPermission(resource, 'create') || 
           hasPermission(resource, 'update') || 
           hasPermission(resource, 'delete');
  };

  const toggleMenu = (menu: keyof typeof expandedMenus) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  const isActiveLink = (path: string) => {
    return pathname === path;
  };

  const handleLogout = async () => {
    await logout();
    router.push('/admin/login');
  };

  return (
    <>
      <button
        onClick={toggleSidebar}
        className={`fixed top-16 z-50 p-2 rounded-md bg-white shadow-md hover:bg-gray-100 transition-all duration-300 ${
          isSidebarOpen ? 'left-60' : 'left-4'
        }`}
      >
        {isSidebarOpen ? <ChevronLeft size={24} /> : <ChevronRight size={24} />}
      </button>
      
      <div className={`w-64 bg-white shadow-md fixed h-[70vh] transition-transform duration-300 ease-in-out z-40 ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="p-4 border-b">
          <h1 className="text-lg font-semibold">Admin Dashboard</h1>
        </div>
        
        <div className="p-4 overflow-y-auto h-[calc(70vh-4rem)]">
          <nav className="space-y-2">
            {/* Dashboard */}
            <Link 
              href="/admin" 
              className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
            >
              <Home size={18} className="mr-2" />
              <span>Dashboard</span>
            </Link>
            
            {/* Product Management */}
            {hasAnyPermission('products') && (
              <div className="space-y-1">
                <button 
                  onClick={() => toggleMenu('productManagement')}
                  className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
                >
                  <div className="flex items-center">
                    <Package size={18} className="mr-2" />
                    <span>Product Management</span>
                  </div>
                  {expandedMenus.productManagement ? (
                    <ChevronDown size={16} />
                  ) : (
                    <ChevronRight size={16} />
                  )}
                </button>
                
                {expandedMenus.productManagement && (
                  <div className="pl-8 space-y-1">
                    <PermissionGuard resource="products" action="view">
                      <Link
                        href="/admin/products" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/products') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <List size={16} className="mr-2" />
                        <span>View Products</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="products" action="create">
                      <Link
                        href="/admin/add-product"
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/add-product') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <Plus size={16} className="mr-2" />
                        <span>Add New Product</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="categories" action="view">
                      <Link
                        href="/admin/categories" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/categories') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <Tag size={16} className="mr-2" />
                        <span>Categories</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="inventory" action="view">
                      <Link 
                        href="/admin/inventory" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/inventory') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <BarChart size={16} className="mr-2" />
                        <span>Inventory</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="products" action="update">
                      <Link 
                        href="/admin/bulk-import-export" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/bulk-import-export') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <FileText size={16} className="mr-2" />
                        <span>Bulk Import/Export</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="reviews" action="view">
                      <Link 
                        href="/admin/reviews" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/reviews') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <MessageSquare size={16} className="mr-2" />
                        <span>Reviews & Ratings</span>
                      </Link>
                    </PermissionGuard>
                    
                    <Link 
                      href="/admin/hero-videos" 
                      className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/hero-videos') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                    >
                      <ImageIcon size={16} className="mr-2" />
                      <span>Hero Videos</span>
                    </Link>
                  </div>
                )}
              </div>
            )}
            
            {/* Orders */}
            {hasAnyPermission('orders') && (
              <div className="space-y-1">
                <button 
                  onClick={() => toggleMenu('orderManagement')}
                  className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
                >
                  <div className="flex items-center">
                    <ShoppingCart size={18} className="mr-2" />
                    <span>Order Management</span>
                  </div>
                  {expandedMenus.orderManagement ? (
                    <ChevronDown size={16} />
                  ) : (
                    <ChevronRight size={16} />
                  )}
                </button>
                
                {expandedMenus.orderManagement && (
                  <div className="pl-8 space-y-1">
                    <PermissionGuard resource="orders" action="view">
                      <Link
                        href="/admin/orders" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/orders') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <List size={16} className="mr-2" />
                        <span>All Orders</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="returns" action="view">
                      <Link
                        href="/admin/orders/returns" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/orders/returns') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <RotateCcw size={16} className="mr-2" />
                        <span>Returns & Refunds</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="orders" action="view">
                      <Link
                        href="/admin/orders/abandoned-carts" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/orders/abandoned-carts') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <ShoppingBag size={16} className="mr-2" />
                        <span>Abandoned Carts</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="orders" action="view">
                      <Link
                        href="/admin/orders/invoices" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/orders/invoices') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <FileText size={16} className="mr-2" />
                        <span>Order Invoices</span>
                      </Link>
                    </PermissionGuard>
                  </div>
                )}
              </div>
            )}
            
            {/* Dashboard & Analytics */}
            {hasAnyPermission('analytics') && (
              <div className="space-y-1">
                <button 
                  onClick={() => toggleMenu('dashboardAnalytics')}
                  className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
                >
                  <div className="flex items-center">
                    <BarChart size={18} className="mr-2" />
                    <span>Dashboard & Analytics</span>
                  </div>
                  {expandedMenus.dashboardAnalytics ? (
                    <ChevronDown size={16} />
                  ) : (
                    <ChevronRight size={16} />
                  )}
                </button>
                
                {expandedMenus.dashboardAnalytics && (
                  <div className="pl-8 space-y-1">
                    <PermissionGuard resource="analytics" action="view">
                      <Link
                        href="/admin/analytics/dashboard" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/analytics/dashboard') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <BarChart size={16} className="mr-2" />
                        <span>Dashboard</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="analytics" action="view">
                      <Link
                        href="/admin/analytics/sales" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/analytics/sales') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <FileText size={16} className="mr-2" />
                        <span>Sales Reports</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="analytics" action="view">
                      <Link
                        href="/admin/analytics/products" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/analytics/products') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <Package size={16} className="mr-2" />
                        <span>Product Performance</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="analytics" action="view">
                      <Link
                        href="/admin/analytics/customers" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/analytics/customers') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <Users size={16} className="mr-2" />
                        <span>Customer Insights</span>
                      </Link>
                    </PermissionGuard>
                  </div>
                )}
              </div>
            )}
            
            {/* Customer Management */}
            {hasAnyPermission('customers') && (
              <div className="space-y-1">
                <button 
                  onClick={() => toggleMenu('customerManagement')}
                  className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
                >
                  <div className="flex items-center">
                    <Users size={18} className="mr-2" />
                    <span>Customer Management</span>
                  </div>
                  {expandedMenus.customerManagement ? (
                    <ChevronDown size={16} />
                  ) : (
                    <ChevronRight size={16} />
                  )}
                </button>
                
                {expandedMenus.customerManagement && (
                  <div className="pl-8 space-y-1">
                    <PermissionGuard resource="customers" action="view">
                      <Link
                        href="/admin/customers/view" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/customers/view') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <List size={16} className="mr-2" />
                        <span>View Customers</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="customers" action="view">
                      <Link
                        href="/admin/customers/loyalty" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/customers/loyalty') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <Tag size={16} className="mr-2" />
                        <span>Loyalty & Rewards</span>
                      </Link>
                    </PermissionGuard>
                    
                    <PermissionGuard resource="customers" action="view">
                      <Link
                        href="/admin/customers/support" 
                        className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/customers/support') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                      >
                        <MessageSquare size={16} className="mr-2" />
                        <span>Customer Support & Tickets</span>
                      </Link>
                    </PermissionGuard>
                  </div>
                )}
              </div>
            )}
            
            {/* User Roles & Permissions - Only for users with 'users' permission */}
            <PermissionGuard resource="users" action="view">
              <Link 
                href="/admin/user-roles" 
                className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/user-roles') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
              >
                <Settings size={18} className="mr-2" />
                <span>User Roles & Permissions</span>
              </Link>
            </PermissionGuard>
            
            {/* Marketing & Promotions */}
            <div className="space-y-1">
              <button 
                onClick={() => toggleMenu('marketingPromotions')}
                className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
              >
                <div className="flex items-center">
                  <Tag size={18} className="mr-2" />
                  <span>Marketing & Promotions</span>
                </div>
                {expandedMenus.marketingPromotions ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </button>
              
              {expandedMenus.marketingPromotions && (
                <div className="pl-8 space-y-1">
                  <Link
                    href="/admin/marketing/discounts" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/marketing/discounts') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <Tag size={16} className="mr-2" />
                    <span>Discounts & Coupons</span>
                  </Link>
                  
                  <Link
                    href="/admin/banners" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/banners') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <ImageIcon size={16} className="mr-2" />
                    <span>Banners & Advertisements</span>
                  </Link>
                  
                  <Link
                    href="/admin/email-sms-marketing" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/email-sms-marketing') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <Mail size={16} className="mr-2" />
                    <span>Email & SMS Marketing</span>
                  </Link>
                  
                  <Link
                    href="/admin/push-notifications" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/push-notifications') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <Bell size={16} className="mr-2" />
                    <span>Push Notifications</span>
                  </Link>
                </div>
              )}
            </div>
            
            {/* Settings & Configurations */}
            <div className="space-y-1">
              <button 
                onClick={() => toggleMenu('settingsConfigurations')}
                className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-blue-50 text-gray-700"
              >
                <div className="flex items-center">
                  <Settings size={18} className="mr-2" />
                  <span>Settings & Configurations</span>
                </div>
                {expandedMenus.settingsConfigurations ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </button>
              
              {expandedMenus.settingsConfigurations && (
                <div className="pl-8 space-y-1">
                  <Link
                    href="/admin/settings/general" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/settings/general') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <Settings size={16} className="mr-2" />
                    <span>General Settings</span>
                  </Link>
                  
                  <Link
                    href="/admin/settings/logs" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/settings/logs') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <FileText size={16} className="mr-2" />
                    <span>System Logs & Activity</span>
                  </Link>
                  
                  <Link
                    href="/admin/settings/tax-payment" 
                    className={`flex items-center p-2 rounded-md hover:bg-blue-50 ${isActiveLink('/admin/settings/tax-payment') ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                  >
                    <ShoppingBag size={16} className="mr-2" />
                    <span>Tax & Payment settings</span>
                  </Link>
                </div>
              )}
            </div>
            
            {/* Logout */}
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 rounded-md hover:bg-red-50 text-red-600"
            >
              <LogOut size={18} className="mr-2" />
              <span>Logout</span>
            </button>
          </nav>
        </div>
      </div>
    </>
  );
} 