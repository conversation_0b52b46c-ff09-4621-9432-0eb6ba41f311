'use client';

import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage, MessagePayload, isSupported, deleteToken } from 'firebase/messaging';

// Firebase configuration from environment variables or window object
const getFirebaseConfig = () => {
  // Check if we're in a browser context
  if (typeof window !== 'undefined') {
    // Try to get the config from window variables injected by the script in layout.tsx
    if (window.ENV_FIREBASE_API_KEY) {
      return {
        apiKey: window.ENV_FIREBASE_API_KEY,
        authDomain: window.ENV_FIREBASE_AUTH_DOMAIN,
        projectId: window.ENV_FIREBASE_PROJECT_ID,
        storageBucket: window.ENV_FIREBASE_STORAGE_BUCKET, 
        messagingSenderId: window.ENV_FIREBASE_MESSAGING_SENDER_ID,
        appId: window.ENV_FIREBASE_APP_ID,
      };
    }
  }
  
  // Fall back to environment variables
  return {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  };
};

// Initialize Firebase lazily
let firebaseApp: any = null;
let messaging: any = null;

// Initialize Firebase if we're in a browser
const initializeFirebase = async () => {
  if (typeof window === 'undefined') return null;
  
  if (!firebaseApp) {
    const config = getFirebaseConfig();
    firebaseApp = initializeApp(config);
  }
  
  return firebaseApp;
};

// Initialize messaging safely
const initializeMessaging = async () => {
  try {
    if (typeof window === 'undefined') return null;
    
    // Check if messaging is supported
    const isMessagingSupported = await isSupported();
    if (!isMessagingSupported) {
      console.error('Firebase messaging is not supported in this browser');
      return null;
    }
    
    const app = await initializeFirebase();
    if (!app) return null;
    
    if (!messaging) {
      messaging = getMessaging(app);
    }
    
    return messaging;
  } catch (error) {
    console.error('Error initializing messaging:', error);
    return null;
  }
};

/**
 * Get FCM token for push notifications
 */
const getFCMToken = async (vapidKey: string): Promise<string | null> => {
  try {
    const messagingInstance = await initializeMessaging();
    if (!messagingInstance) return null;
    
    // Get token
    let currentToken;
    try {
      currentToken = await getToken(messagingInstance, {
        vapidKey: vapidKey,
        serviceWorkerRegistration: await getServiceWorkerRegistration(),
      });
    } catch (error: any) {
      // If token is not registered or invalid, try to delete and regenerate
      if (error.code === 'messaging/registration-token-not-registered') {
        console.log('Token not registered or invalid, attempting to refresh token');
        // Try to delete the existing token first
        try {
          await deleteToken(messagingInstance);
          console.log('Old token deleted successfully');
        } catch (deleteError) {
          console.warn('Could not delete old token:', deleteError);
        }
        
        // Get a fresh token
        currentToken = await getToken(messagingInstance, {
          vapidKey: vapidKey,
          serviceWorkerRegistration: await getServiceWorkerRegistration(),
        });
      } else {
        // Re-throw other errors
        throw error;
      }
    }
    
    if (currentToken) {
      console.log('FCM Token obtained');
      return currentToken;
    } else {
      console.log('No registration token available');
      return null;
    }
  } catch (error) {
    console.error('An error occurred while retrieving token:', error);
    return null;
  }
};

/**
 * Get service worker registration
 */
const getServiceWorkerRegistration = async () => {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return undefined;
  }
  
  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    const fcmServiceWorker = registrations.find(reg => 
      reg.active && 
      reg.active.scriptURL.includes('firebase-messaging-sw.js')
    );
    return fcmServiceWorker;
  } catch (error) {
    console.error('Error getting service worker registration:', error);
    return undefined;
  }
};

/**
 * Set up foreground message handler
 */
const onForegroundMessage = (callback: (payload: MessagePayload) => void) => {
  if (typeof window === 'undefined') return () => {};
  
  const setupListener = async () => {
    const messagingInstance = await initializeMessaging();
    if (!messagingInstance) return () => {};
    
    return onMessage(messagingInstance, callback);
  };
  
  // Setup the listener and return a no-op if it fails
  try {
    return setupListener();
  } catch (error) {
    console.error('Error setting up message listener:', error);
    return () => {};
  }
};

/**
 * Request notification permission and register FCM token
 */
async function requestNotificationPermission() {
  try {
    console.log('Starting notification permission request process');
    
    // Check if we're in a browser and notifications are supported
    if (typeof window === 'undefined') {
      console.log('Not in browser environment');
      return null;
    }
    
    if (!('Notification' in window)) {
      console.log('Notifications not supported');
      return null;
    }
    
    console.log('Current permission status:', Notification.permission);
    
    // If already granted, don't re-request
    if (Notification.permission === 'granted') {
      console.log('Permission already granted, getting token');
      // Get token and save it anyway
      const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || window.ENV_FIREBASE_VAPID_KEY || '';
      if (vapidKey) {
        const token = await getFCMToken(vapidKey);
        if (token) {
          await saveFCMTokenToDatabase(token);
          return token;
        }
      }
      return 'already-granted';
    }
    
    // Request permission
    console.log('Requesting notification permission...');
    const permission = await Notification.requestPermission();
    console.log('Permission response:', permission);
    
    if (permission !== 'granted') {
      console.log('Notification permission not granted');
      return permission;
    }
    
    // Get token
    const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || window.ENV_FIREBASE_VAPID_KEY || '';
    if (!vapidKey) {
      console.error('VAPID key not found');
      return 'granted-no-vapid';
    }
    
    console.log('Getting FCM token with VAPID key:', vapidKey.substring(0, 10) + '...');
    const currentToken = await getFCMToken(vapidKey);
    
    if (currentToken) {
      console.log('Got FCM token, saving to database...');
      // Save the token to the database
      await saveFCMTokenToDatabase(currentToken);
      return currentToken;
    } else {
      console.log('No registration token available');
      return 'granted-no-token';
    }
  } catch (error) {
    console.error('An error occurred while requesting permission:', error);
    return 'error';
  }
}

/**
 * Save FCM token to database
 */
async function saveFCMTokenToDatabase(token: string) {
  if (typeof window === 'undefined') return;
  
  try {
    // Make sure the token is valid before saving
    const response = await fetch('/api/push-notifications/verify-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });
    
    const verification = await response.json();
    
    if (!verification.valid) {
      console.warn('Token validation failed, not saving token');
      return false;
    }
    
    // Save the valid token
    const saveResponse = await fetch('/api/push-notifications/register-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });
    
    const data = await saveResponse.json();
    
    if (!data.success) {
      console.error('Failed to save token:', data.error);
      return false;
    } else {
      console.log('Token saved successfully');
      return true;
    }
  } catch (error) {
    console.error('Error saving FCM token:', error);
    return false;
  }
}

// TypeScript declaration for window object
declare global {
  interface Window {
    ENV_FIREBASE_API_KEY?: string;
    ENV_FIREBASE_AUTH_DOMAIN?: string;
    ENV_FIREBASE_PROJECT_ID?: string;
    ENV_FIREBASE_STORAGE_BUCKET?: string;
    ENV_FIREBASE_MESSAGING_SENDER_ID?: string;
    ENV_FIREBASE_APP_ID?: string;
    ENV_FIREBASE_VAPID_KEY?: string;
  }
}

export { firebaseApp, messaging, getFCMToken, onForegroundMessage, requestNotificationPermission, getServiceWorkerRegistration }; 