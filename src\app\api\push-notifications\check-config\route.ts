import { NextRequest, NextResponse } from 'next/server';
import admin from 'firebase-admin';

function maskString(input: string | undefined): string {
  if (!input) return 'undefined';
  if (input.length <= 8) return '***';
  return input.substring(0, 4) + '...' + input.substring(input.length - 4);
}

export async function GET(request: NextRequest) {
  try {
    // Check client-side Firebase config
    const clientConfig = {
      apiKey: maskString(process.env.NEXT_PUBLIC_FIREBASE_API_KEY),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
      vapidKey: maskString(process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY),
    };
    
    // Check Admin SDK config
    const adminConfig = {
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
      clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL?.split('@')[0] + '@...',
      privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY ? 'Present (starts with: ' + 
        process.env.FIREBASE_ADMIN_PRIVATE_KEY.substring(0, 15).replace(/\\n/g, '') + '...)' : 'Missing',
    };
    
    // Check if Admin SDK is initialized
    const isAdminInitialized = admin.apps.length > 0;
    
    // If Admin SDK is initialized, try to get project info
    let projectInfo = null;
    if (isAdminInitialized) {
      try {
        // The app object might provide some information
        const app = admin.app();
        projectInfo = {
          name: app.name,
          options: {
            projectId: app.options.projectId,
            // Don't include sensitive credential info
          },
        };
      } catch (e) {
        console.error('Error getting admin app info:', e);
      }
    }
    
    // Check that client and admin project IDs match
    const projectsMatch = clientConfig.projectId === adminConfig.projectId;
    
    return NextResponse.json({
      success: true,
      clientConfig,
      adminConfig,
      adminInitialized: isAdminInitialized,
      projectInfo,
      projectsMatch,
      message: projectsMatch 
        ? 'Client and Admin configurations have matching project IDs' 
        : 'WARNING: Client and Admin configurations have DIFFERENT project IDs!',
    });
  } catch (error) {
    console.error('Error checking Firebase config:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { success: false, error: `Failed to check Firebase configuration: ${errorMessage}` },
      { status: 500 }
    );
  }
} 