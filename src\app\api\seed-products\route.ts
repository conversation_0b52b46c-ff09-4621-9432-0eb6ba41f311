import { NextResponse } from 'next/server';
import { seedProducts } from '@/lib/seedData';

export async function GET() {
  try {
    await seedProducts();
    return NextResponse.json({ success: true, message: 'Products seeded successfully' });
  } catch (error) {
    console.error('Error seeding products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to seed products' },
      { status: 500 }
    );
  }
} 