import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const customerInfo = await req.json();
    
    if (!customerInfo.email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Check if customer already exists
    let customer = await Customer.findOne({ email: customerInfo.email.toLowerCase() }).lean();
    
    if (customer) {
      // Update existing customer with the latest info
      await Customer.findByIdAndUpdate(customer._id, {
        $set: {
          firstName: customerInfo.firstName,
          lastName: customerInfo.lastName,
          phone: customerInfo.phone,
          address: customerInfo.address
        }
      });
      
      // Get the updated customer
      customer = await Customer.findById(customer._id).lean();
    } else {
      // Create a new customer
      const newCustomer = new Customer({
        email: customerInfo.email.toLowerCase(),
        firstName: customerInfo.firstName,
        lastName: customerInfo.lastName,
        phone: customerInfo.phone,
        address: customerInfo.address,
        orders: [],
        wishlist: []
      });
      
      await newCustomer.save();
      customer = await Customer.findById(newCustomer._id).lean();
    }
    
    return NextResponse.json({ 
      success: true,
      customer
    });
  } catch (error) {
    console.error('Error during checkout login:', error);
    return NextResponse.json(
      { error: 'Checkout login failed' },
      { status: 500 }
    );
  }
} 