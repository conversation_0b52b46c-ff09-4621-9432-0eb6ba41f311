'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCartStore } from '@/store/useCartStore';
import dynamic from 'next/dynamic';

// PERFORMANCE OPTIMIZATION: Dynamic imports for components to reduce initial bundle size
const ProductCard = dynamic(() => import("@/components/ProductCard"), {
  loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>,
  ssr: true
});

const ProductCardSkeleton = dynamic(() => import('@/components/ProductCardSkeleton'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>,
  ssr: true
});

// PERFORMANCE OPTIMIZATION: Dynamic import for toast notifications
import { toast } from 'sonner';
import '@/styles/homepage/our-gems.css';
import '../styles/homepage/hero.css';
import '../styles/homepage/container.css';
import '../styles/homepage/featured-products.css';
import '../styles/homepage/why-us.css';
import '../styles/homepage/latest-products.css';
import '../styles/homepage/latest-articles.css';
import { useLanguage } from '@/contexts/LanguageContext';
import { getCloudFrontImageUrl } from '@/lib/cloudfront-client';
import HLSVideoPlayer from '@/components/HLSVideoPlayer';

// Define Product type based on our schema
type Product = {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  videoUrl?: string;
  createdAt: string;
  isFeatured?: boolean;
  isLatest?: boolean;
  weight?: number;
}



export default function Home() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [latestProducts, setLatestProducts] = useState<Product[]>([]);
  const [imageProducts, setImageProducts] = useState<Product[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [featuredProductsLoading, setFeaturedProductsLoading] = useState(true);
  const [latestProductsLoading, setLatestProductsLoading] = useState(true);
  const [featuredProductsError, setFeaturedProductsError] = useState<string | null>(null);
  const [featuredProductsErrorType, setFeaturedProductsErrorType] = useState<string | null>(null);
  const [featuredProductsErrorTime, setFeaturedProductsErrorTime] = useState<string | null>(null);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [latestProductsError, setLatestProductsError] = useState<string | null>(null);
  const [latestProductsErrorType, setLatestProductsErrorType] = useState<string | null>(null);
  const [latestProductsErrorTime, setLatestProductsErrorTime] = useState<string | null>(null);
  const [latestProductsRetryAttempts, setLatestProductsRetryAttempts] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  // Add this state to the Home component
  const [heroBtnLoading, setHeroBtnLoading] = useState(false);

  // Add language context
  const { language, translations } = useLanguage();

  // Add router for navigation
  const router = useRouter();

  // Error categorization helper function
  const categorizeError = (error: any): { type: string; message: string } => {
    const errorString = error?.message || error?.toString() || 'Unknown error';
    const timestamp = new Date().toLocaleTimeString();

    // Network/Fetch errors
    if (errorString.includes('fetch') || errorString.includes('NetworkError') || errorString.includes('Failed to fetch')) {
      return { type: 'NETWORK_ERROR', message: `Network connection failed: ${errorString}` };
    }
    if (errorString.includes('timeout') || errorString.includes('TIMEOUT')) {
      return { type: 'NETWORK_ERROR', message: `Request timeout: ${errorString}` };
    }
    if (errorString.includes('ENOTFOUND') || errorString.includes('getaddrinfo')) {
      return { type: 'NETWORK_ERROR', message: `DNS resolution failed: ${errorString}` };
    }
    
    // Database errors
    if (errorString.includes('MongoDB') || errorString.includes('database') || errorString.includes('connection')) {
      return { type: 'DATABASE_ERROR', message: `Database error: ${errorString}` };
    }
    if (errorString.includes('query') || errorString.includes('find') || errorString.includes('aggregate')) {
      return { type: 'DATABASE_ERROR', message: `Database query failed: ${errorString}` };
    }
    
    // Parsing errors
    if (errorString.includes('JSON') || errorString.includes('parse') || errorString.includes('Unexpected token')) {
      return { type: 'PARSE_ERROR', message: `Response parsing failed: ${errorString}` };
    }
    
    // API/HTTP errors
    if (errorString.includes('500') || errorString.includes('503') || errorString.includes('502')) {
      return { type: 'API_ERROR', message: `Server error: ${errorString}` };
    }
    if (errorString.includes('404') || errorString.includes('Not Found')) {
      return { type: 'API_ERROR', message: `API endpoint not found: ${errorString}` };
    }
    
    // Cache errors (including hero videos cache)
    if (errorString.includes('localStorage') || errorString.includes('cache') || errorString.includes('Storage') || errorString.includes('Hero videos cache')) {
      return { type: 'CACHE_ERROR', message: `Cache error: ${errorString}` };
    }
    
    // System errors (Method #7: Concurrent but Independent wrapper errors)
    if (errorString.includes('system') || errorString.includes('Method #7')) {
      return { type: 'SYSTEM_ERROR', message: `System error: ${errorString}` };
    }
    
    // Default fallback
    return { type: 'UNKNOWN_ERROR', message: `Unexpected error: ${errorString}` };
  };



  // Navigation functions for why-us section
  const navigateToFacetedGems = () => {
    router.push('/shop?category=Faceted Gems');
  };

  const navigateToRoughGems = () => {
    router.push('/shop?category=Rough Gems');
  };

  const navigateToJewelry = () => {
    router.push('/shop?category=LapisLazuli');
  };

  // State to track screen size for responsive video
  const [isMobile, setIsMobile] = useState(false);
  
  // State for hero videos
  const [heroVideos, setHeroVideos] = useState<{mobile?: string, desktop?: string, mobileChunked?: string, desktopChunked?: string}>({});
  const [heroVideosLoading, setHeroVideosLoading] = useState(true);
  
  // Scroll to top on page load - home page always starts from top
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Handle client-side mounting to prevent hydration errors
  useEffect(() => {
    setIsMounted(true);
    
    // Check initial screen size for responsive video
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768); // 768px is md breakpoint
    };
    
    // Check on mount
    checkScreenSize();
    
    // Listen for resize events
    window.addEventListener('resize', checkScreenSize);
    
    // Listen for the openCart event from the header
    const handleOpenCart = () => {
      setIsCartOpen(true);
    };
    
    window.addEventListener('openCart', handleOpenCart);
    
    return () => {
      window.removeEventListener('resize', checkScreenSize);
      window.removeEventListener('openCart', handleOpenCart);
    };
  }, []);
  

  
  // Load hero videos directly from S3 via CloudFront with smart caching [[memory:7612604151137385833]]
  useEffect(() => {
    const loadDirectHeroVideos = async () => {
      // Smart cache check - avoid regenerating URLs for 10 minutes
      const cacheKey = 'homepage-hero-videos-direct-v1';
      const cacheTimestamp = 'homepage-hero-videos-direct-timestamp-v1';
      const cached = localStorage.getItem(cacheKey);
      const timestamp = localStorage.getItem(cacheTimestamp);
      const now = Date.now();
      const tenMinutes = 10 * 60 * 1000; // 10 minutes cache duration
      
      if (cached && timestamp && now - parseInt(timestamp) < tenMinutes) {
        try {
          const cachedVideoMap = JSON.parse(cached);
          setHeroVideos(cachedVideoMap);
          setHeroVideosLoading(false);
          console.log('💾 Direct hero videos loaded from cache');
          return;
        } catch (error) {
          console.log('Direct hero videos cache parse error, regenerating URLs');
        }
      }
      
      try {
        console.log('🚀 Loading direct hero videos from CloudFront');
        
        // Direct CloudFront URLs - no function needed
        const directMobileUrl = 'https://d39l10jdryt7ww.cloudfront.net/mobile+hero.mp4';
        const directDesktopUrl = 'https://d39l10jdryt7ww.cloudfront.net/desktop+hero.mp4';
        
        // Create optimized video map with direct CloudFront URLs
        const videoMap: {mobile?: string, desktop?: string, mobileChunked?: string, desktopChunked?: string} = {
          // Use direct CloudFront URLs for both regular and chunked
          mobile: directMobileUrl,
          desktop: directDesktopUrl,
          mobileChunked: directMobileUrl,  // Direct CloudFront URL
          desktopChunked: directDesktopUrl // Direct CloudFront URL
        };
        
        console.log('🎬 Generated video map:', videoMap);
        setHeroVideos(videoMap);
        
        // Cache successful URLs for 10 minutes [[memory:7612604151137385833]]
        localStorage.setItem(cacheKey, JSON.stringify(videoMap));
        localStorage.setItem(cacheTimestamp, Date.now().toString());
        console.log('💾 Direct hero videos cached successfully');
        console.log('✅ Hero videos ready:', {
          mobile: directMobileUrl,
          desktop: directDesktopUrl,
          videoMap: videoMap
        });
        
      } catch (error) {
        console.error('❌ Error loading direct hero videos:', error);
        
        // Fallback to hardcoded URLs as backup
        const fallbackVideoMap = {
          mobile: '/api/hls-proxy/mobile-chunk/mobile-playlist.m3u8',
          desktop: '/api/hls-proxy/desktop-chunk/desktop-playlist.m3u8',
          mobileChunked: '/api/hls-proxy/mobile-chunk/mobile-playlist.m3u8',
          desktopChunked: '/api/hls-proxy/desktop-chunk/desktop-playlist.m3u8'
        };
        
        console.log('🔄 Using fallback video URLs:', fallbackVideoMap);
        setHeroVideos(fallbackVideoMap);
      } finally {
        setHeroVideosLoading(false);
      }
    };
    
    loadDirectHeroVideos();
  }, []);

  // Fetch products to display
  useEffect(() => {
    const fetchProducts = async () => {
      // Clear previous errors on new fetch attempt
      setFeaturedProductsError(null);
      setFeaturedProductsErrorType(null);
      setFeaturedProductsErrorTime(null);
      setLatestProductsError(null);
      setLatestProductsErrorType(null);
      setLatestProductsErrorTime(null);
      
      // PERFORMANCE OPTIMIZATION: Smart cache check - avoid refetching for 10 minutes [[memory:173771]]
      const cacheKey = 'homepage-products';
      const cacheTimestamp = 'homepage-products-timestamp';
      const cached = localStorage.getItem(cacheKey);
      const timestamp = localStorage.getItem(cacheTimestamp);
      const now = Date.now();
      const tenMinutes = 10 * 60 * 1000; // Extended from 5 to 10 minutes for better performance
      
      if (cached && timestamp && now - parseInt(timestamp) < tenMinutes) {
        try {
          const cachedData = JSON.parse(cached);
          setFeaturedProducts(cachedData.featuredProducts || []);
          setLatestProducts(cachedData.latestProducts || []);
          setFeaturedProductsLoading(false);
          setLatestProductsLoading(false);
          setLoading(false);
          // Reset retry attempts on successful cache load
          setRetryAttempts(0);
          setLatestProductsRetryAttempts(0);
          return;
        } catch (error) {
          console.log('Cache parse error, fetching fresh data');
          const errorInfo = categorizeError(error);
          setFeaturedProductsError(errorInfo.message);
          setFeaturedProductsErrorType(errorInfo.type);
          setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
        }
      }
      
      try {
        // Method #7: Concurrent but Independent - Each API call has individual error handling
        console.log('🚀 Fetching products with Method #7: Concurrent but Independent - Maximum performance');
        
        // Create individual promises with independent error handling
        const mainProductsPromise = fetch('/api/products?fields=homepage&limit=50').catch(e => {
          console.error('❌ Main products fetch failed:', e);
          return null;
        });
        
        const featuredProductsPromise = fetch('/api/products?featured=true&fields=homepage&limit=10').catch(e => {
          console.error('❌ Featured products fetch failed:', e);
          return null;
        });
        
        const latestProductsPromise = fetch('/api/products?latest=true&fields=homepage&limit=20').catch(e => {
          console.error('❌ Latest products fetch failed:', e);
          return null;
        });
        
        // Execute all promises concurrently but independently
        const [mainResponse, featuredResponse, latestResponse] = await Promise.all([
          mainProductsPromise,     // results[0] - Main products
          featuredProductsPromise, // results[1] - Featured products  
          latestProductsPromise    // results[2] - Latest products
        ]);
        
        // Convert responses to allSettled-like format for existing code compatibility
        const results = [
          mainResponse ? { status: 'fulfilled' as const, value: mainResponse } : { status: 'rejected' as const, reason: new Error('Main products fetch failed') },
          featuredResponse ? { status: 'fulfilled' as const, value: featuredResponse } : { status: 'rejected' as const, reason: new Error('Featured products fetch failed') },
          latestResponse ? { status: 'fulfilled' as const, value: latestResponse } : { status: 'rejected' as const, reason: new Error('Latest products fetch failed') }
        ];
        
        // Process each API result independently - Professional error isolation
        let data, featuredData, latestData;
        let mainProductsSuccess = false;
        let featuredProductsSuccess = false;
        let latestProductsSuccess = false;
        
        // Process Main Products API (results[0])
        if (results[0].status === 'fulfilled') {
          const response = results[0].value;
          if (response.ok) {
            try {
              data = await response.json();
              if (data && data.products) {
                mainProductsSuccess = true;
                console.log('✅ Main products loaded successfully');
              } else {
                throw new Error('Invalid main products response structure - missing products array');
              }
            } catch (parseError) {
              console.error('❌ Main products JSON parse error:', parseError);
              const errorInfo = categorizeError(parseError);
              setFeaturedProductsError(`Main products parse error: ${errorInfo.message}`);
              setFeaturedProductsErrorType(errorInfo.type);
              setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
            }
          } else {
            console.error('❌ Main products API failed:', response.status, response.statusText);
            const errorInfo = categorizeError(new Error(`Main products API failed with status ${response.status}: ${response.statusText}`));
            setFeaturedProductsError(errorInfo.message);
            setFeaturedProductsErrorType(errorInfo.type);
            setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
          }
        } else {
          console.error('❌ Main products fetch failed:', results[0].reason);
          const errorInfo = categorizeError(results[0].reason);
          setFeaturedProductsError(`Main products fetch failed: ${errorInfo.message}`);
          setFeaturedProductsErrorType(errorInfo.type);
          setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
        }
        
        // Process Featured Products API (results[1]) - Independent of main products
        if (results[1].status === 'fulfilled') {
          const featuredResponse = results[1].value;
          if (featuredResponse.ok) {
            try {
              featuredData = await featuredResponse.json();
              if (featuredData && featuredData.products) {
                featuredProductsSuccess = true;
                console.log(`✅ Featured products loaded successfully: ${featuredData.products.length} products`);
              } else {
                console.warn('⚠️ Featured products response structure invalid');
                setFeaturedProductsError('Featured products response missing products array');
                setFeaturedProductsErrorType('DATA_ERROR');
                setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
              }
            } catch (parseError) {
              console.error('❌ Featured products JSON parse error:', parseError);
              const errorInfo = categorizeError(parseError);
              setFeaturedProductsError(`Featured products parse error: ${errorInfo.message}`);
              setFeaturedProductsErrorType(errorInfo.type);
              setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
            }
          } else {
            console.error('❌ Featured products API failed:', featuredResponse.status, featuredResponse.statusText);
            const errorInfo = categorizeError(new Error(`Featured products API failed with status ${featuredResponse.status}: ${featuredResponse.statusText}`));
            setFeaturedProductsError(errorInfo.message);
            setFeaturedProductsErrorType(errorInfo.type);
            setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
          }
        } else {
          console.error('❌ Featured products fetch failed:', results[1].reason);
          const errorInfo = categorizeError(results[1].reason);
          setFeaturedProductsError(`Featured products fetch failed: ${errorInfo.message}`);
          setFeaturedProductsErrorType(errorInfo.type);
          setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
        }
        
        // Process Latest Products API (results[2]) - Independent of other APIs with professional error handling
        if (results[2].status === 'fulfilled') {
          const latestResponse = results[2].value;
          if (latestResponse.ok) {
            try {
              latestData = await latestResponse.json();
              if (latestData && latestData.products) {
                latestProductsSuccess = true;
                console.log(`✅ Latest products loaded successfully: ${latestData.products.length} products`);
              } else {
                console.warn('⚠️ Latest products response structure invalid');
                setLatestProductsError('Latest products response missing products array');
                setLatestProductsErrorType('DATA_ERROR');
                setLatestProductsErrorTime(new Date().toLocaleTimeString());
              }
            } catch (parseError) {
              console.error('❌ Latest products JSON parse error:', parseError);
              const errorInfo = categorizeError(parseError);
              setLatestProductsError(`Latest products parse error: ${errorInfo.message}`);
              setLatestProductsErrorType(errorInfo.type);
              setLatestProductsErrorTime(new Date().toLocaleTimeString());
            }
          } else {
            console.error('❌ Latest products API failed:', latestResponse.status, latestResponse.statusText);
            const errorInfo = categorizeError(new Error(`Latest products API failed with status ${latestResponse.status}: ${latestResponse.statusText}`));
            setLatestProductsError(errorInfo.message);
            setLatestProductsErrorType(errorInfo.type);
            setLatestProductsErrorTime(new Date().toLocaleTimeString());
          }
        } else {
          console.error('❌ Latest products fetch failed:', results[2].reason);
          const errorInfo = categorizeError(results[2].reason);
          setLatestProductsError(`Latest products fetch failed: ${errorInfo.message}`);
          setLatestProductsErrorType(errorInfo.type);
          setLatestProductsErrorTime(new Date().toLocaleTimeString());
        }
        
        // Professional data processing - Handle successful API responses
        if (mainProductsSuccess && data && data.products && data.products.length > 0) {
          // Filter products that have images for the carousel
          const productsWithImages = data.products.filter(
            (product: Product) => product.imageUrl
          );
          setImageProducts(productsWithImages);
          
          console.log(`✅ Main products processed: ${data.products.length} products`);
        } else if (!mainProductsSuccess) {
          console.warn('⚠️ Main products failed - skipping main products processing');
        }
        
        // Process Featured Products - Independent of main products success
        if (featuredProductsSuccess && featuredData && featuredData.products && featuredData.products.length > 0) {
          setFeaturedProducts(featuredData.products);
          console.log(`✅ Featured products processed: ${featuredData.products.length} products`);
        } else if (featuredProductsSuccess && (!featuredData || !featuredData.products || featuredData.products.length === 0)) {
          console.warn('⚠️ Featured products API succeeded but no products found');
          setFeaturedProductsError('No featured products found in database - Please mark some products as featured');
          setFeaturedProductsErrorType('DATA_ERROR');
          setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
        }
        // Always set featured products loading to false after processing
        setFeaturedProductsLoading(false);
        
        // Process Latest Products - Independent of other APIs with professional error handling
        if (latestProductsSuccess && latestData && latestData.products && latestData.products.length > 0) {
          setLatestProducts(latestData.products);
          console.log(`✅ Latest products processed: ${latestData.products.length} products`);
        } else if (latestProductsSuccess && (!latestData || !latestData.products || latestData.products.length === 0)) {
          console.warn('⚠️ Latest products API succeeded but no products found');
          setLatestProductsError('No latest products found in database - Please mark some products as latest in admin panel');
          setLatestProductsErrorType('DATA_ERROR');
          setLatestProductsErrorTime(new Date().toLocaleTimeString());
        }
        // Always set latest products loading to false after processing
        setLatestProductsLoading(false);
        
        // PERFORMANCE OPTIMIZATION: Professional caching with enhanced error handling [[memory:173771]]
        const cacheData = {
          featuredProducts: (featuredProductsSuccess && featuredData) ? featuredData.products || [] : [],
          latestProducts: (latestProductsSuccess && latestData) ? latestData.products || [] : []
        };
        
        // Only update cache if we have some successful data
        if (mainProductsSuccess || featuredProductsSuccess || latestProductsSuccess) {
          try {
            localStorage.setItem('homepage-products', JSON.stringify(cacheData));
            localStorage.setItem('homepage-products-timestamp', Date.now().toString());
            console.log('💾 Cache updated with successful API results (10min TTL)');
          } catch (storageError) {
            console.warn('⚠️ Failed to update localStorage cache:', storageError);
            // Clear potentially corrupted cache
            try {
              localStorage.removeItem('homepage-products');
              localStorage.removeItem('homepage-products-timestamp');
            } catch (clearError) {
              console.error('❌ Failed to clear corrupted cache:', clearError);
            }
          }
        }
        
        // Reset retry attempts on any successful fetch
        if (featuredProductsSuccess) {
          setRetryAttempts(0);
          console.log('🔄 Featured products retry attempts reset - Featured products loaded successfully');
        }
        if (latestProductsSuccess) {
          setLatestProductsRetryAttempts(0);
          console.log('🔄 Latest products retry attempts reset - Latest products loaded successfully');
        }
        
        // Always set main loading to false after Method #7 completes
        setLoading(false);
        
        // Professional summary logging
        console.log(`📊 API Results Summary: Main(${mainProductsSuccess ? '✅' : '❌'}) Featured(${featuredProductsSuccess ? '✅' : '❌'}) Latest(${latestProductsSuccess ? '✅' : '❌'})`);
        
      } catch (error) {
        // This catch block should rarely execute with Method #7: Concurrent but Independent
        console.error('❌ Unexpected error in Method #7 block:', error);
        const errorInfo = categorizeError(error);
        setFeaturedProductsError(`Unexpected system error: ${errorInfo.message}`);
        setFeaturedProductsErrorType('SYSTEM_ERROR');
        setFeaturedProductsErrorTime(new Date().toLocaleTimeString());
        setLoading(false);
        setFeaturedProductsLoading(false);
        setLatestProductsLoading(false);
      }
    };

    if (isMounted) {
      fetchProducts();
    }
  }, [isMounted]);

  // Initialize Living Gem button effects
  useEffect(() => {
    if (!isMounted) return;

    // Import and initialize the Living Gem buttons script
    const initializeLivingGemButtons = async () => {
      try {
        const { initializeLivingGemButtons } = await import('@/scripts/living-gem-buttons.js');
        initializeLivingGemButtons();
      } catch (error) {
        console.error('Failed to load Living Gem buttons script:', error);
      }
    };

    initializeLivingGemButtons();
  }, [isMounted]);
  

  


  

  
  // Return early to prevent hydration errors - but still render hero section
  if (!isMounted) {
    return (
      <div className="homepage-container">
        {/* Hero Section - critical content that should be visible even before full mounting */}
        <div className="hero-container">
          {/* Current Image - Lowest z-index (0) */}
          <div className="hero-image-container">
            <HLSVideoPlayer 
              src={isMobile ? heroVideos.mobile : heroVideos.desktop}
              chunkedSrc={isMobile ? heroVideos.mobileChunked : heroVideos.desktopChunked}
              className="hero-video"
              autoPlay
              loop
              muted
              playsInline
              preload="auto"
            />
            <div className="hero-overlay"></div>
          </div>
          
          {/* Main heading and content - z-index: 20 */}
          <div className="hero-content">
            <div className="hero-text-content">
              <h1 className="hero-title">
                {translations.hero_title}
              </h1>
              <p className="hero-subtitle">
                {translations.hero_subtitle_1}
              </p>
            </div>
            
            {/* Buttons container - both buttons together */}
            <div className="hero-buttons-container">
              {/* View All Products Button */}
              <div className="hero-button-left">
                <div className="hero-button-wrapper">
                  <button
                    className="hero-button dark-style"
                    aria-label="Browse all products in our shop"
                  >
                    <div className="crack-overlay"></div>
                    <span>{translations.view_all_products}</span>
                  </button>
                </div>
              </div>

              {/* View Jewelry Button */}
              <div className="hero-button-right">
                <div className="hero-button-wrapper">
                  <button
                    className="hero-button-secondary sapphire-style"
                    aria-label="View jewelry collection"
                  >
                    <div className="crack-overlay"></div>
                    <span>{translations.login_register}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`homepage-container`}>
      {/* Hero Section with Product Image Carousel */}
      <div className="hero-container">
        {/* Current Image - Lowest z-index (0) */}
        <div className="hero-image-container">
          {currentImageIndex === 0 ? (
            <>
              <HLSVideoPlayer 
                src={isMobile ? heroVideos.mobile : heroVideos.desktop}
                chunkedSrc={isMobile ? heroVideos.mobileChunked : heroVideos.desktopChunked}
                className="hero-video"
                autoPlay
                loop
                muted
                playsInline
                preload="auto"
              />
              {/* Dark overlay */}
              <div className="hero-overlay"></div>
            </>
          ) : (
            <>
              <Image 
                key={imageProducts[currentImageIndex - 1]._id}
                src={`${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${imageProducts[currentImageIndex - 1].imageUrl}`}
                alt={imageProducts[currentImageIndex - 1].name}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
                className="hero-image"
                priority
                fetchPriority="high"
                loading="eager"
              />
              {/* Dark overlay for product images */}
              <div className="hero-overlay"></div>
            </>
          )}
        </div>
        

        
        {/* Main heading and content - z-index: 20 */}
        <div className="hero-content">
          <div className="hero-text-content">
            <h1 className="hero-title">
              {translations.hero_title}
            </h1>
            {currentImageIndex === 0 && (
              <p className="hero-subtitle">
                {translations.hero_subtitle_1}
              </p>
            )}
            {currentImageIndex === 1 && (
              <p className="hero-subtitle">
                {translations.hero_subtitle_2}
              </p>
            )}
            {currentImageIndex === 2 && (
              <p className="hero-subtitle">
                {translations.hero_subtitle_3}
              </p>
            )}
            {currentImageIndex === 3 && (
              <p className="hero-subtitle">
                {translations.hero_subtitle_4}
              </p>
            )}
          </div>
          
          {/* Buttons container - both buttons together */}
          <div className="hero-buttons-container">
            {/* View Faceted Gems Button */}
            <div className="hero-button-left">
              <div className="hero-button-wrapper">
                <Link href="/shop?category=Faceted%20Gems" legacyBehavior>
                  <button
                    className="hero-button dark-style"
                    aria-label="Browse faceted gems in our shop"
                    disabled={heroBtnLoading}
                    onClick={e => {
                      setHeroBtnLoading(true);
                    }}
                  >
                    <div className="crack-overlay"></div>
                    <span>{heroBtnLoading ? translations.products_loading : translations.view_all_products}</span>
                  </button>
                </Link>
              </div>
            </div>

            {/* View Jewelry Button */}
            <div className="hero-button-right">
              <div className="hero-button-wrapper">
                <Link href="/shop?category=LapisLazuli" legacyBehavior>
                  <button
                    className="hero-button-secondary sapphire-style"
                    aria-label="View LapisLazuli collection"
                  >
                    <div className="crack-overlay"></div>
                    <span>{translations.view_lapisLazuli}</span>
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        

      </div>

      {/* Featured Products Section */}
      <section className="featured-products-section">
        <div className="featured-products-container">
          {/* Heading with decorative line */}
          <div className="featured-products-heading-container">
            <h2 className="featured-products-heading">
              {translations.featured_products}
            </h2>
          </div>
          
          <div className="featured-products-grid">
            {featuredProductsLoading ? (
              // Show skeleton loading cards while fetching featured products
              <>
                {Array.from({ length: 4 }, (_, index) => (
                  <ProductCardSkeleton 
                    key={`featured-skeleton-${index}`}
                    className="featured-products-card"
                  />
                ))}
              </>
            ) : featuredProducts.length > 0 ? (
              // Render dynamically fetched featured products with optimized loading
              featuredProducts.slice(0, 4).map((product, index) => (
                <ProductCard
                  key={product._id}
                  className="featured-products-card"
                  imageSrc={product.imageUrl ? getCloudFrontImageUrl(product.imageUrl) : "/images/placeholder.png"}
                  productName={product.name}
                  regularPrice={product.price}
                  salePrice={product.price * 0.95} // 5% discount as example
                  weight={product.weight || 0}
                  alt={product.name}
                  productId={product._id} // Pass real product ID
                  href={`/product/${product._id}`}
                  // PERFORMANCE OPTIMIZATION: First 2 products get priority loading
                  priority={index < 2}
                  loading={index < 2 ? 'eager' : 'lazy'}
                  fetchPriority={index < 2 ? 'high' : 'auto'}
                />
              ))
            ) : (
              // Show error message if there's an error, otherwise show empty state
              <div className="col-span-full grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 py-8 text-center">
                {featuredProductsError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center mb-3">
                      <span className="text-red-500 text-2xl mr-2">🔴</span>
                      <span className="font-semibold text-red-700">[{featuredProductsErrorType}]</span>
                      <span className="text-sm text-red-600 ml-2">{featuredProductsErrorTime}</span>
                    </div>
                    <p className="text-red-700 text-sm mb-4 font-dosis leading-relaxed">
                      {featuredProductsError}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-2 justify-center">
                      <button
                        onClick={() => (window as any).retryFeaturedProducts?.()}
                        disabled={retryAttempts >= 3}
                        className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                          retryAttempts >= 3 
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                            : 'bg-red-600 text-white hover:bg-red-700'
                        }`}
                      >
                        {retryAttempts >= 3 ? 'Max Retries Reached' : `Retry (${retryAttempts}/3)`}
                      </button>
                      <button
                        onClick={() => navigator.clipboard.writeText(`${featuredProductsErrorType}: ${featuredProductsError} at ${featuredProductsErrorTime}`)}
                        className="px-4 py-2 text-sm font-medium bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                      >
                        Copy Error
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 font-dosis">{translations.no_featured_products_available}</p>
                )}
              </div>
            )}
          </div>
        </div>
        
      </section>

      {/* Section Spacer - Standard spacing between major sections */}
      <div className="h-16 sm:h-20 md:h-24"></div>

      {/* Why Us Section - Responsive layout */}
      <section className="why-us-section">
        <div className="why-us-container">
          {/* Mobile Layout - Only visible on mobile */}
          <div className="why-us-mobile-layout">
            {/* Two-column grid for heading on mobile/sm only */}
            <div className="why-us-mobile-heading-grid">
              {/* Heading Column - Left Aligned - Only visible on mobile/sm */}
              <div className="why-us-mobile-heading-container">
                <h2 className="why-us-mobile-heading">
                  {translations.why_us}
                </h2>
              </div>
              
              {/* Image Column - Mobile */}
              <div className="why-us-image-wrapper">
                <Image 
                  src="/images/why-us/why-us-bg.jpg" 
                  alt="Why Choose Us"
                  fill
                  sizes="(max-width: 768px) 50vw, 100vw"
                  className="why-us-image"
                  loading="lazy"
                />
              </div>
            </div>

            {/* Mobile Description */}
            <p className="why-us-mobile-description">
              {translations.why_us_description}
            </p>
            
            {/* Mobile Button */}
            <Link href="/about">
              <button className="why-us-mobile-button">
                {translations.read_more}
              </button>
            </Link>
          </div>

          {/* Desktop Layout - Only visible on desktop */}
          <div className="why-us-desktop-layout">
            {/* Content Column - Left side */}
            <div className="why-us-content">
              {/* Heading */}
              <h2 className="why-us-heading">
                {translations.why_us}
              </h2>
              
              {/* Description */}
              <p className="why-us-description">
                {translations.why_us_description}
              </p>
              
              {/* Call to Action Button */}
              <Link href="/about">
                <button className="why-us-button">
                  {translations.read_more}
                </button>
              </Link>
            </div>
            
            {/* Image Column - Right side */}
            <div className="why-us-desktop-image-container">
              <div className="why-us-desktop-image-wrapper">
                <Image 
                  src="/images/why-us/why-us-bg.jpg" 
                  alt="Why Choose Us"
                  fill
                  sizes="(min-width: 768px) 50vw, 100vw"
                  className="why-us-image"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
          
          {/* Category Images Section - Below main content on both mobile and desktop */}
          
          {/* Desktop Category Grid */}
          <div className="why-us-desktop-image-grid">
            {/* Image 1 */}
            <div className="why-us-category-link" onClick={navigateToFacetedGems}>
              <div className="why-us-category-image-container">
                <Image 
                  src="/images/why-us/faceted.png" 
                  alt="Faceted Gems"
                  fill
                  sizes="(min-width: 768px) 25vw, 33vw"
                  className="why-us-category-image"
                  loading="lazy"
                />
              </div>
              <div className="why-us-category-label">
                <span className="why-us-category-text">
                  {translations.faceted_gems}
                </span>
              </div>
            </div>
            
            {/* Image 2 */}
            <div className="why-us-category-link" onClick={navigateToRoughGems}>
              <div className="why-us-category-image-container">
                <Image 
                  src="/images/why-us/rough.png" 
                  alt="Rough Gems"
                  fill
                  sizes="(min-width: 768px) 25vw, 33vw"
                  className="why-us-category-image"
                  loading="lazy"
                />
              </div>
              <div className="why-us-category-label">
                <span className="why-us-category-text">
                  {translations.rough_gems}
                </span>
              </div>
            </div>
            
            {/* Image 3 */}
            <div className="why-us-category-link" onClick={navigateToJewelry}>
              <div className="why-us-category-image-container">
                <Image 
                  src="/images/why-us/handmade-jems.png" 
                  alt="Handmade Jewelry"
                  fill
                  sizes="(min-width: 768px) 25vw, 33vw"
                  className="why-us-category-image"
                  loading="lazy"
                />
              </div>
              <div className="why-us-category-label">
                <span className="why-us-category-text">
                  {translations.handmade_jewelry}
                </span>
              </div>
            </div>
          </div>
          
          {/* Mobile Category Grid */}
          <div className="why-us-mobile-image-grid">
            {/* Image 1 */}
            <div className="why-us-mobile-category-link" onClick={navigateToFacetedGems}>
              <div className="why-us-mobile-category-image-container">
                <Image 
                  src="/images/why-us/faceted.png" 
                  alt="Faceted Gems"
                  fill
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw"
                  className="why-us-mobile-category-image"
                />
              </div>
              <div className="why-us-mobile-category-label">
                <span className="why-us-mobile-category-text">
                  {translations.faceted_gems}
                </span>
              </div>
            </div>
            
            {/* Image 2 */}
            <div className="why-us-mobile-category-link" onClick={navigateToRoughGems}>
              <div className="why-us-mobile-category-image-container">
                <Image 
                  src="/images/why-us/rough.png" 
                  alt="Rough Gems"
                  fill
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw"
                  className="why-us-mobile-category-image"
                />
              </div>
              <div className="why-us-mobile-category-label">
                <span className="why-us-mobile-category-text">
                  {translations.rough_gems}
                </span>
              </div>
            </div>
            
            {/* Image 3 */}
            <div className="why-us-mobile-category-link" onClick={() => router.push('/shop?category=LapisLazuli')}>
              <div className="why-us-mobile-category-image-container">
                <Image 
                  src="/images/why-us/handmade-jems.png" 
                  alt="Handmade Jewelry"
                  fill
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw"
                  className="why-us-mobile-category-image"
                />
              </div>
              <div className="why-us-mobile-category-label">
                <span className="why-us-mobile-category-text">
                  {translations.handmade_jewelry}
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Spacer - Standard spacing between major sections */}
      <div className="h-16 sm:h-20 md:h-24"></div>

      {/* Our Gems Section */}
      <section className="py-8 sm:py-12 md:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Heading with decorative line */}
          <div className="flex flex-col items-center mb-8 sm:mb-12">
            <h2 className="our-gems-heading">
              {translations.our_gems_heading}
            </h2>
          </div>
          
          {/* Featured Gems - 2-column grid (2-column on all screens) */}
          <div className="our-gems-container">
            {/* Collectors Choice */}
            <div className="our-gems-card">
              <div className="our-gems-image-container">
                <div className="our-gems-image-wrapper">
                  <Image 
                    src="/images/our-gems/collectors-choice2.png"
                    alt="Collectors Choice Gems"
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 40vw, 25vw"
                    quality={85}
                    className="our-gems-image"
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="our-gems-text-container">
                <h3 className="our-gems-title">
                  {translations.collectors_choice}
                </h3>
              </div>
            </div>
            
            {/* Designers Product */}
            <div className="our-gems-card">
              <div className="our-gems-image-container">
                <div className="our-gems-image-wrapper">
                  <Image 
                    src="/images/our-gems/designers-product.png"
                    alt="Designers Product"
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 40vw, 25vw"
                    quality={85}
                    className="our-gems-image"
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="our-gems-text-container">
                <h3 className="our-gems-title">
                  {translations.designers_product}
                </h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Spacer - Standard spacing between major sections */}
      <div className="h-16 sm:h-20 md:h-24"></div>

      {/* Latest Products Section */}
      <section className="latest-products-section">
        <div className="latest-products-container">
          {/* Heading with decorative line */}
          <div className="latest-products-heading-container">
            <h2 className="latest-products-heading">
              {translations.latest_products}
            </h2>
          </div>
          
          {/* Product Cards Grid - Matching Featured Products layout */}
          <div className="latest-products-grid">
            {latestProductsLoading ? (
              // Show skeleton loading cards while fetching latest products
              <>
                {Array.from({ length: 8 }, (_, index) => (
                  <ProductCardSkeleton 
                    key={`latest-skeleton-${index}`}
                    className="latest-products-card"
                  />
                ))}
              </>
            ) : latestProducts.length > 0 ? (
              // Dynamic latest products display with optimized loading - using only ProductCard for all products
              latestProducts.slice(0, 8).map((product, index) => (
                <ProductCard
                  key={product._id}
                  className="latest-products-card"
                  imageSrc={product.imageUrl ? getCloudFrontImageUrl(product.imageUrl) : "/images/placeholder.png"}
                  productName={product.name}
                  regularPrice={product.price}
                  salePrice={product.price}
                  weight={product.weight || 0}
                  alt={product.name}
                  productId={product._id} // Pass real product ID
                  href={`/product/${product._id}`}
                  // PERFORMANCE OPTIMIZATION: First 2 latest products load eagerly, rest lazy
                  loading={index < 2 ? 'eager' : 'lazy'}
                />
              ))
            ) : latestProductsError ? (
              // Professional Error Display for Latest Products - Matches Featured Products styling
              <div className="col-span-full py-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 sm:p-6">
                  <div className="flex items-start">
                    {/* Error Icon */}
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    
                    <div className="ml-3 flex-1">
                      {/* Error Title */}
                      <h3 className="text-sm font-medium text-red-800">
                        Latest Products Loading Error
                      </h3>
                      
                      {/* Error Details */}
                      <div className="mt-2 text-sm text-red-700">
                        <p><strong>Error:</strong> {latestProductsError}</p>
                        <p><strong>Type:</strong> {latestProductsErrorType}</p>
                        <p><strong>Time:</strong> {latestProductsErrorTime}</p>
                        <p><strong>Retry Attempts:</strong> {latestProductsRetryAttempts}/3</p>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="mt-4 flex flex-wrap gap-2">
                        {/* Retry Button */}
                        <button
                          onClick={() => (window as any).retryLatestProducts?.()}
                          disabled={latestProductsRetryAttempts >= 3}
                          className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                            latestProductsRetryAttempts >= 3
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-red-100 text-red-700 hover:bg-red-200'
                          }`}
                        >
                          {latestProductsRetryAttempts >= 3 ? 'Max Retries Reached' : 'Retry Loading'}
                        </button>
                        
                        {/* Copy Error Button */}
                        <button
                          onClick={() => {
                            const errorDetails = `Latest Products Error:
Error: ${latestProductsError}
Type: ${latestProductsErrorType}
Time: ${latestProductsErrorTime}
Retry Attempts: ${latestProductsRetryAttempts}/3
User Agent: ${navigator.userAgent}
URL: ${window.location.href}`;
                            navigator.clipboard.writeText(errorDetails);
                            alert('Error details copied to clipboard');
                          }}
                          className="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                        >
                          Copy Error
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // Show empty state message when no latest products exist (only after loading is complete)
              <div className="col-span-full grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 py-8 text-center">
                <p className="text-gray-500 font-dosis">{translations.no_latest_products_available}</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Section Spacer - Standard spacing between major sections */}
      <div className="h-16 sm:h-20 md:h-24"></div>

      {/* Latest Articles Section */}
      <section className="latest-articles-section">
        <div className="latest-articles-container">
          {/* Heading with decorative line */}
          <div className="latest-articles-heading-container">
            <h2 className="latest-articles-heading">
              {translations.latest_articles}
            </h2>
          </div>
          
          {/* Articles Grid - Responsive for mobile and tablet */}
          <div className="latest-articles-grid">
            {/* Article Card 1 */}
            <div className="latest-articles-card">
              <div className="latest-articles-image-container">
                {/* Article Image */}
                <div className="latest-articles-image-wrapper">
                  <Image 
                    src="/images/articles/article1.jpg"
                    alt="Trust in Online Gemstone Market"
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 50vw, 33vw"
                    quality={85}
                    className="latest-articles-image"
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="latest-articles-content">
                <h3 className="latest-articles-title">
                  {translations.article1_title}
                </h3>
                <p className="latest-articles-description">
                  {translations.article1_description}
                </p>
                <Link href="/blog/trust-in-gemstone-market" className="latest-articles-read-more">
                  {translations.read_more_arrow}
                </Link>
              </div>
            </div>
            
            {/* Article Card 2 */}
            <div className="latest-articles-card">
              <div className="latest-articles-image-container">
                {/* Article Image */}
                <div className="latest-articles-image-wrapper">
                  <Image 
                    src="/images/articles/article2.jpg"
                    alt="Ethical Sourcing in Gem Industry"
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 50vw, 33vw"
                    quality={85}
                    className="latest-articles-image"
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="latest-articles-content">
                <h3 className="latest-articles-title">
                  {translations.article2_title}
                </h3>
                <p className="latest-articles-description">
                  {translations.article2_description}
                </p>
                <Link href="/blog/ethical-sourcing" className="latest-articles-read-more">
                  {translations.read_more_arrow}
                </Link>
              </div>
            </div>
            
            {/* Article Card 3 */}
            <div className="latest-articles-card">
              <div className="latest-articles-image-container">
                {/* Article Image */}
                <div className="latest-articles-image-wrapper">
                  <Image 
                    src="/images/articles/article3.jpg"
                    alt="Identifying Quality Gemstones"
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 50vw, 33vw"
                    quality={85}
                    className="latest-articles-image"
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="latest-articles-content">
                <h3 className="latest-articles-title">
                  {translations.article3_title}
                </h3>
                <p className="latest-articles-description">
                  {translations.article3_description}
                </p>
                <Link href="/blog/identifying-quality-gemstones" className="latest-articles-read-more">
                  {translations.read_more_arrow}
                </Link>
              </div>
            </div>
          </div>
          
          {/* View All Articles Button */}
          <div className="latest-articles-button-container">
            <Link href="/blog">
              <button className="latest-articles-button">
                {translations.view_all_articles}
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Section Spacer - Standard spacing between major sections */}
      <div className="h-16 sm:h-20 md:h-24"></div>

     
    </div>
  );
}