import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Settings } from '@/models/Settings';

// GET /api/admin/settings - Get all settings
export async function GET() {
  try {
    await dbConnect();
    
    // Get settings singleton
    const settings = await Settings.getSingleton();
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings - Update settings
export async function PUT(req: Request) {
  try {
    await dbConnect();
    
    // Parse request body
    const updateData = await req.json();
    
    // Validate the update data (basic validation only)
    if (!updateData) {
      return NextResponse.json(
        { error: 'No settings data provided' },
        { status: 400 }
      );
    }
    
    // Get settings document
    const settings = await Settings.getSingleton();
    
    // Update settings
    if (updateData.store) {
      settings.store = { ...settings.store, ...updateData.store };
    }
    
    if (updateData.payment) {
      settings.payment = { ...settings.payment, ...updateData.payment };
    }
    
    if (updateData.shipping) {
      settings.shipping = { ...settings.shipping, ...updateData.shipping };
    }
    
    // Save changes
    await settings.save();
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
} 