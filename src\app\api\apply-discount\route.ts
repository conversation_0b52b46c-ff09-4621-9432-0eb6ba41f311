import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Discount from '@/models/Discount';

export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    
    const data = await req.json();
    const { discountId } = data;
    
    // Validate input
    if (!discountId) {
      return NextResponse.json(
        { error: 'Discount ID is required' },
        { status: 400 }
      );
    }
    
    // Find the discount and increment the usage counter
    const discount = await Discount.findByIdAndUpdate(
      discountId,
      { $inc: { usedCount: 1 } },
      { new: true }
    );
    
    // If discount not found
    if (!discount) {
      return NextResponse.json(
        { error: 'Discount not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Discount usage recorded successfully',
      discount: {
        _id: discount._id,
        code: discount.code,
        usedCount: discount.usedCount
      }
    });
    
  } catch (error) {
    console.error('Error recording discount usage:', error);
    return NextResponse.json(
      { error: 'Failed to record discount usage' },
      { status: 500 }
    );
  }
} 