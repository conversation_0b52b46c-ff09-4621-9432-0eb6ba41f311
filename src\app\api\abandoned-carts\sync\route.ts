import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import AbandonedCart from '@/models/AbandonedCart';

/**
 * Sync cart data with MongoDB
 * This endpoint receives cart data from the frontend and stores it as abandoned cart
 * 
 * POST /api/abandoned-carts/sync
 */
export async function POST(request: Request) {
  try {
    console.log('[API] Abandoned cart sync request received');
    
    // Parse the request body
    const data = await request.json();
    console.log('[API] Request data:', { 
      sessionId: data.sessionId,
      items: data.items?.length || 0,
      customerInfo: data.customerInfo
    });
    
    // Validate required fields
    if (!data.sessionId || !data.items || data.items.length === 0) {
      console.log('[API] Validation failed: Missing required fields');
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: sessionId and at least one item'
      }, { status: 400 });
    }
    
    // Connect to database
    console.log('[API] Connecting to database...');
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      console.error('[API] Database connection error:', dbConnection.error);
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    console.log('[API] Connected to database, checking for existing cart');
    
    // Check if a cart with this session ID already exists
    let cart = await AbandonedCart.findOne({ sessionId: data.sessionId });
    
    if (cart) {
      console.log('[API] Existing cart found, updating...');
      // Update existing cart
      cart.items = data.items;
      cart.subtotal = data.subtotal;
      cart.lastActivity = new Date();
      
      // If customer info was added that wasn't there before, update it
      if (data.customerInfo && data.customerInfo.email && !cart.customerInfo.email) {
        cart.customerInfo = {
          ...cart.customerInfo,
          ...data.customerInfo
        };
      }
      
      await cart.save();
      console.log('[API] Cart updated successfully');
    } else {
      console.log('[API] No existing cart found, creating new cart');
      // Create new cart
      cart = new AbandonedCart({
        sessionId: data.sessionId,
        customerInfo: data.customerInfo || {},
        items: data.items,
        subtotal: data.subtotal,
        lastActivity: new Date(),
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: data.userAgent || 'unknown',
        referrer: data.referrer || null
      });
      
      await cart.save();
      console.log('[API] New cart created successfully');
    }
    
    return NextResponse.json({
      success: true,
      message: 'Cart synced successfully'
    });
  } catch (error: any) {
    console.error('[API] Error syncing cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to sync cart'
    }, { status: 500 });
  }
} 