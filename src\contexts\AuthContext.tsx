'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface Customer {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  address: {
    street: string;
    country: string;
    province: string;
    city: string;
    postalCode: string;
  };
  currentRewards?: Array<{
    id: number;
    name: string;
    issuedDate: string;
  }>;
  spentRewards?: Array<{
    id: number;
    name: string;
    spentDate: string;
    amount: number;
  }>;
}

interface AuthContextType {
  customer: Customer | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  loginWithCheckoutInfo: (customerInfo: Omit<Customer, '_id'>) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in by retrieving customer info from localStorage
    const checkAuthStatus = async () => {
      setIsLoading(true);
      
      try {
        const storedCustomer = localStorage.getItem('customer');
        
        if (storedCustomer) {
          const parsedCustomer = JSON.parse(storedCustomer);
          
          // Verify the stored customer with the backend
          const response = await fetch('/api/auth/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ customerId: parsedCustomer._id }),
          });
          
          if (response.ok) {
            setCustomer(parsedCustomer);
          } else {
            // If verification fails, clear localStorage
            localStorage.removeItem('customer');
            setCustomer(null);
          }
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        localStorage.removeItem('customer');
        setCustomer(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        return false;
      }
      
      const data = await response.json();
      setCustomer(data.customer);
      
      // Store customer info in localStorage
      localStorage.setItem('customer', JSON.stringify(data.customer));
      
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithCheckoutInfo = async (customerInfo: Omit<Customer, '_id'>): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // Register or login customer using checkout information
      const response = await fetch('/api/auth/checkout-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerInfo),
      });
      
      if (!response.ok) {
        return false;
      }
      
      const data = await response.json();
      setCustomer(data.customer);
      
      // Store customer info in localStorage
      localStorage.setItem('customer', JSON.stringify(data.customer));
      
      return true;
    } catch (error) {
      console.error('Checkout login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const logout = async (): Promise<void> => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      // Clear localStorage and state
      localStorage.removeItem('customer');
      setCustomer(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        customer,
        isLoading,
        isAuthenticated: !!customer,
        login,
        logout,
        loginWithCheckoutInfo,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext; 