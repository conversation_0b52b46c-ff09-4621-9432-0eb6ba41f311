'use client';

import { useState, useEffect } from 'react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { CreditCard, Save, Plus, Edit, Trash2, AlertCircle, Percent, DollarSign, RefreshCw } from 'lucide-react';

// Define tax rule interface
interface TaxRule {
  _id: string;
  name: string;
  rate: number;
  country: string;
  state?: string;
  applyToShipping: boolean;
  isDefault: boolean;
}

// Define payment gateway interface
interface PaymentGateway {
  _id: string;
  name: string;
  provider: string;
  isActive: boolean;
  testMode: boolean;
  supportedCurrencies: string[];
}

// New interface for tax rule form
interface TaxRuleForm {
  name: string;
  rate: number;
  country: string;
  state: string;
  applyToShipping: boolean;
  isDefault: boolean;
}

// New interface for payment gateway form
interface PaymentGatewayForm {
  name: string;
  provider: string;
  customProvider?: string;
  isActive: boolean;
  testMode: boolean;
  supportedCurrencies: string;
}

export default function TaxPaymentSettingsPage() {
  // State for tax rules data
  const [taxRules, setTaxRules] = useState<TaxRule[]>([]);
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([]);
  
  // State for loading and error
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State for selected tabs
  const [activeTab, setActiveTab] = useState<'tax' | 'payment'>('tax');
  
  // State for form
  const [isEditingTax, setIsEditingTax] = useState(false);
  const [isEditingPayment, setIsEditingPayment] = useState(false);
  const [editingTaxRuleId, setEditingTaxRuleId] = useState<string | null>(null);
  const [editingPaymentGatewayId, setEditingPaymentGatewayId] = useState<string | null>(null);
  
  // State for form data
  const [taxRuleForm, setTaxRuleForm] = useState<TaxRuleForm>({
    name: '',
    rate: 0,
    country: '',
    state: '',
    applyToShipping: false,
    isDefault: false
  });
  
  const [paymentGatewayForm, setPaymentGatewayForm] = useState<PaymentGatewayForm>({
    name: '',
    provider: '',
    customProvider: '',
    isActive: true,
    testMode: false,
    supportedCurrencies: ''
  });
  
  // Get current admin user
  const { user, hasPermission } = useAdminAuth();

  // Load data when component mounts and tab changes
  useEffect(() => {
    if (activeTab === 'tax') {
      fetchTaxRules();
    } else {
      fetchPaymentGateways();
    }
  }, [activeTab]);

  // Fetch tax rules from API
  const fetchTaxRules = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/settings/tax-rules');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch tax rules: ${response.statusText}`);
      }
      
      const data = await response.json();
      setTaxRules(data.taxRules || []);
    } catch (err) {
      console.error('Error fetching tax rules:', err);
      setError('Failed to load tax rules. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch payment gateways from API
  const fetchPaymentGateways = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/settings/payment-gateways');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch payment gateways: ${response.statusText}`);
      }
      
      const data = await response.json();
      setPaymentGateways(data.paymentGateways || []);
    } catch (err) {
      console.error('Error fetching payment gateways:', err);
      setError('Failed to load payment gateways. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle tax rule form changes
  const handleTaxRuleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setTaxRuleForm(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else {
      setTaxRuleForm(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) : value
      }));
    }
  };
  
  // Handle payment gateway form changes
  const handlePaymentGatewayFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setPaymentGatewayForm(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else {
      setPaymentGatewayForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle tax rule form submission
  const handleTaxRuleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      // If editing an existing rule, use PUT, otherwise use POST
      const method = editingTaxRuleId ? 'PUT' : 'POST';
      const url = editingTaxRuleId 
        ? `/api/admin/settings/tax-rules?id=${editingTaxRuleId}` 
        : '/api/admin/settings/tax-rules';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taxRuleForm),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${editingTaxRuleId ? 'update' : 'create'} tax rule`);
      }
      
      // Reset form, clear editing state and close edit mode
      setTaxRuleForm({
        name: '',
        rate: 0,
        country: '',
        state: '',
        applyToShipping: false,
        isDefault: false
      });
      
      setEditingTaxRuleId(null);
      setIsEditingTax(false);
      
      // Refresh the tax rules list
      fetchTaxRules();
      
    } catch (err) {
      console.error(`Error ${editingTaxRuleId ? 'updating' : 'creating'} tax rule:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${editingTaxRuleId ? 'update' : 'create'} tax rule`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle payment gateway form submission
  const handlePaymentGatewaySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Convert comma separated string to array
      const supportedCurrencies = paymentGatewayForm.supportedCurrencies
        .split(',')
        .map(currency => currency.trim())
        .filter(currency => currency); // Remove empty strings
      
      // Use the custom provider value if provider is "Other"
      const finalProvider = paymentGatewayForm.provider === 'Other' 
        ? paymentGatewayForm.customProvider 
        : paymentGatewayForm.provider;
      
      // If editing an existing gateway, use PUT, otherwise use POST
      const method = editingPaymentGatewayId ? 'PUT' : 'POST';
      const url = editingPaymentGatewayId 
        ? `/api/admin/settings/payment-gateways?id=${editingPaymentGatewayId}` 
        : '/api/admin/settings/payment-gateways';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...paymentGatewayForm,
          provider: finalProvider,
          supportedCurrencies,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${editingPaymentGatewayId ? 'update' : 'create'} payment gateway`);
      }
      
      // Reset form, clear editing state and close edit mode
      setPaymentGatewayForm({
        name: '',
        provider: '',
        customProvider: '',
        isActive: true,
        testMode: false,
        supportedCurrencies: ''
      });
      
      setEditingPaymentGatewayId(null);
      setIsEditingPayment(false);
      
      // Refresh the payment gateways list
      fetchPaymentGateways();
      
    } catch (err) {
      console.error(`Error ${editingPaymentGatewayId ? 'updating' : 'creating'} payment gateway:`, err);
      setError(err instanceof Error ? err.message : `Failed to ${editingPaymentGatewayId ? 'update' : 'create'} payment gateway`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete tax rule
  const deleteTaxRule = async (id: string) => {
    if (!confirm('Are you sure you want to delete this tax rule?')) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/settings/tax-rules?id=${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete tax rule');
      }
      
      // Refresh the tax rules list
      fetchTaxRules();
      
    } catch (err) {
      console.error('Error deleting tax rule:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete tax rule');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete payment gateway
  const deletePaymentGateway = async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment gateway?')) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/settings/payment-gateways?id=${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete payment gateway');
      }
      
      // Refresh the payment gateways list
      fetchPaymentGateways();
      
    } catch (err) {
      console.error('Error deleting payment gateway:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete payment gateway');
    } finally {
      setIsLoading(false);
    }
  };

  // Edit tax rule
  const editTaxRule = (rule: TaxRule) => {
    setTaxRuleForm({
      name: rule.name,
      rate: rule.rate,
      country: rule.country,
      state: rule.state || '',
      applyToShipping: rule.applyToShipping,
      isDefault: rule.isDefault
    });
    setEditingTaxRuleId(rule._id);
    setIsEditingTax(true);
  };

  // Edit payment gateway
  const editPaymentGateway = (gateway: PaymentGateway) => {
    // Determine if this is a custom provider
    const isCustomProvider = !['Stripe', 'PayPal', 'Manual'].includes(gateway.provider);
    
    setPaymentGatewayForm({
      name: gateway.name,
      provider: isCustomProvider ? 'Other' : gateway.provider,
      customProvider: isCustomProvider ? gateway.provider : '',
      isActive: gateway.isActive,
      testMode: gateway.testMode,
      supportedCurrencies: gateway.supportedCurrencies.join(', ')
    });
    
    setEditingPaymentGatewayId(gateway._id);
    setIsEditingPayment(true);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Tax & Payment Settings</h1>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <AlertCircle size={18} className="mr-2" />
          {error}
        </div>
      )}
      
      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('tax')}
              className={`mr-8 py-4 px-1 inline-flex items-center ${
                activeTab === 'tax'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Percent size={18} className="mr-2" />
              <span>Tax Rules</span>
            </button>
            <button
              onClick={() => setActiveTab('payment')}
              className={`py-4 px-1 inline-flex items-center ${
                activeTab === 'payment'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CreditCard size={18} className="mr-2" />
              <span>Payment Gateways</span>
            </button>
          </nav>
        </div>
      </div>
      
      {/* Tax Rules Tab */}
      {activeTab === 'tax' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Configure Tax Rules</h2>
            <button
              onClick={() => fetchTaxRules()}
              className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              <RefreshCw size={16} className={`mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
          
          {/* Tax Rules List */}
          <div className="bg-white rounded-md shadow overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b">
              <p className="text-sm text-gray-600">
                Tax rules determine how taxes are calculated for orders based on location and other factors.
              </p>
              <button 
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => setIsEditingTax(true)}
              >
                <Plus size={16} className="mr-2" />
                <span>Add Tax Rule</span>
              </button>
            </div>
            
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Apply to Shipping
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      <div className="flex justify-center items-center">
                        <RefreshCw size={20} className="animate-spin mr-2" />
                        Loading tax rules...
                      </div>
                    </td>
                  </tr>
                ) : taxRules.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No tax rules configured
                    </td>
                  </tr>
                ) : (
                  taxRules.map(rule => (
                    <tr key={rule._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {rule.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {rule.rate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {rule.country}{rule.state ? `, ${rule.state}` : ''}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {rule.applyToShipping ? 'Yes' : 'No'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {rule.isDefault ? (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Default
                          </span>
                        ) : (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                            Active
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900 mr-3" onClick={() => editTaxRule(rule)}>
                          <Edit size={16} />
                        </button>
                        <button 
                          className="text-red-600 hover:text-red-900"
                          onClick={() => deleteTaxRule(rule._id)}
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {/* Tax Rule Form */}
          {isEditingTax && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">{editingTaxRuleId ? 'Edit Tax Rule' : 'Add New Tax Rule'}</h3>
                  <button 
                    onClick={() => {
                      setIsEditingTax(false);
                      setEditingTaxRuleId(null);
                      setTaxRuleForm({
                        name: '',
                        rate: 0,
                        country: '',
                        state: '',
                        applyToShipping: false,
                        isDefault: false
                      });
                    }}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <form onSubmit={handleTaxRuleSubmit}>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">Rule Name</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        placeholder="e.g. Standard VAT, Reduced Rate, Import Tax"
                        value={taxRuleForm.name}
                        onChange={handleTaxRuleFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="rate" className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                      <input
                        type="number"
                        id="rate"
                        name="rate"
                        min="0"
                        step="0.01"
                        required
                        placeholder="e.g. 10 for 10% tax"
                        value={taxRuleForm.rate}
                        onChange={handleTaxRuleFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="country" className="block text-sm font-medium text-gray-700">Country</label>
                      <input
                        type="text"
                        id="country"
                        name="country"
                        required
                        placeholder="e.g. Afghanistan"
                        value={taxRuleForm.country}
                        onChange={handleTaxRuleFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="state" className="block text-sm font-medium text-gray-700">State/Province (Optional)</label>
                      <input
                        type="text"
                        id="state"
                        name="state"
                        placeholder="e.g. Kabul (leave empty for country-wide rule)"
                        value={taxRuleForm.state}
                        onChange={handleTaxRuleFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          id="applyToShipping"
                          name="applyToShipping"
                          checked={taxRuleForm.applyToShipping}
                          onChange={handleTaxRuleFormChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="applyToShipping" className="font-medium text-gray-700">Apply to Shipping</label>
                        <p className="text-gray-500">When enabled, this tax will also be applied to shipping costs</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          id="isDefault"
                          name="isDefault"
                          checked={taxRuleForm.isDefault}
                          onChange={handleTaxRuleFormChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="isDefault" className="font-medium text-gray-700">Set as Default Tax Rule</label>
                        <p className="text-gray-500">This rule will be applied when no other specific rules match</p>
                        <p className="text-amber-600 mt-1">Note: If this is the very first tax rule you're creating in the system, it will automatically be set as the default rule regardless of whether you check this box or not. This is because the system needs at least one default rule to function properly.</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-5 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => {
                        setIsEditingTax(false);
                        setEditingTaxRuleId(null);
                        setTaxRuleForm({
                          name: '',
                          rate: 0,
                          country: '',
                          state: '',
                          applyToShipping: false,
                          isDefault: false
                        });
                      }}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      {editingTaxRuleId ? 'Update Tax Rule' : 'Save Tax Rule'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Payment Gateways Tab */}
      {activeTab === 'payment' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Configure Payment Gateways</h2>
            <button
              onClick={() => fetchPaymentGateways()}
              className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              <RefreshCw size={16} className={`mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
          
          {/* Payment Gateways List */}
          <div className="bg-white rounded-md shadow overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b">
              <p className="text-sm text-gray-600">
                Configure payment gateways to accept payments from customers.
              </p>
              <button 
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => setIsEditingPayment(true)}
              >
                <Plus size={16} className="mr-2" />
                <span>Add Payment Gateway</span>
              </button>
            </div>
            
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gateway
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Supported Currencies
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mode
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      <div className="flex justify-center items-center">
                        <RefreshCw size={20} className="animate-spin mr-2" />
                        Loading payment gateways...
                      </div>
                    </td>
                  </tr>
                ) : paymentGateways.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No payment gateways configured
                    </td>
                  </tr>
                ) : (
                  paymentGateways.map(gateway => (
                    <tr key={gateway._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {gateway.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {gateway.provider}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {gateway.supportedCurrencies.join(', ')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {gateway.testMode ? (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Test
                          </span>
                        ) : (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            Live
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {gateway.isActive ? (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900 mr-3" onClick={() => editPaymentGateway(gateway)}>
                          <Edit size={16} />
                        </button>
                        <button 
                          className="text-red-600 hover:text-red-900"
                          onClick={() => deletePaymentGateway(gateway._id)}
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {/* Payment Gateway Form */}
          {isEditingPayment && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">{editingPaymentGatewayId ? 'Edit Payment Gateway' : 'Add New Payment Gateway'}</h3>
                  <button 
                    onClick={() => {
                      setIsEditingPayment(false);
                      setEditingPaymentGatewayId(null);
                      setPaymentGatewayForm({
                        name: '',
                        provider: '',
                        customProvider: '',
                        isActive: true,
                        testMode: false,
                        supportedCurrencies: ''
                      });
                    }}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <form onSubmit={handlePaymentGatewaySubmit}>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">Gateway Name</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        placeholder="e.g. Credit/Debit Cards, PayPal, Bank Transfer"
                        value={paymentGatewayForm.name}
                        onChange={handlePaymentGatewayFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">This is the name that will be shown to customers during checkout</p>
                    </div>
                    
                    <div>
                      <label htmlFor="provider" className="block text-sm font-medium text-gray-700">Provider</label>
                      <select
                        id="provider"
                        name="provider"
                        required
                        value={paymentGatewayForm.provider}
                        onChange={handlePaymentGatewayFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="">Select a payment provider</option>
                        <option value="Stripe">Stripe</option>
                        <option value="PayPal">PayPal</option>
                        <option value="Manual">Manual / Bank Transfer</option>
                        <option value="Other">Other</option>
                      </select>
                      <p className="mt-1 text-xs text-gray-500">The payment processor that will handle the transactions</p>
                    </div>
                    
                    {paymentGatewayForm.provider === 'Other' && (
                      <div>
                        <label htmlFor="customProvider" className="block text-sm font-medium text-gray-700">Custom Provider Name</label>
                        <input
                          type="text"
                          id="customProvider"
                          name="customProvider"
                          required
                          placeholder="e.g. Bitcoin, Cash on Delivery, Wire Transfer"
                          value={paymentGatewayForm.customProvider}
                          onChange={handlePaymentGatewayFormChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                        <p className="mt-1 text-xs text-gray-500">Specify the name of your custom payment provider</p>
                      </div>
                    )}
                    
                    <div>
                      <label htmlFor="supportedCurrencies" className="block text-sm font-medium text-gray-700">Supported Currencies</label>
                      <input
                        type="text"
                        id="supportedCurrencies"
                        name="supportedCurrencies"
                        required
                        placeholder="USD, EUR, AFN (comma-separated list)"
                        value={paymentGatewayForm.supportedCurrencies}
                        onChange={handlePaymentGatewayFormChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">List all currencies this payment method can process</p>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          id="testMode"
                          name="testMode"
                          checked={paymentGatewayForm.testMode}
                          onChange={handlePaymentGatewayFormChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="testMode" className="font-medium text-gray-700">Test Mode</label>
                        <p className="text-gray-500">Enable for testing without processing real payments. No actual charges will occur.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          id="isActive"
                          name="isActive"
                          checked={paymentGatewayForm.isActive}
                          onChange={handlePaymentGatewayFormChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="isActive" className="font-medium text-gray-700">Active</label>
                        <p className="text-gray-500">When enabled, this payment method will be available to customers during checkout</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-5 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => {
                        setIsEditingPayment(false);
                        setEditingPaymentGatewayId(null);
                        setPaymentGatewayForm({
                          name: '',
                          provider: '',
                          customProvider: '',
                          isActive: true,
                          testMode: false,
                          supportedCurrencies: ''
                        });
                      }}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      {editingPaymentGatewayId ? 'Update Gateway' : 'Save Gateway'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 