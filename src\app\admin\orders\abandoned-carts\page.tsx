'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, RefreshCw, ArrowLeft, Filter, Mail, CheckCircle, Clock, AlertTriangle, Trash } from 'lucide-react';

type AbandonedCart = {
  _id: string;
  sessionId: string;
  customerInfo: {
    email?: string;
    name?: string;
    phone?: string;
  };
  items: Array<{
    productId: string;
    name: string;
    price: number;
    quantity: number;
    imageUrl?: string;
  }>;
  subtotal: number;
  lastActivity: string;
  createdAt: string;
  recoveryEmailSent: boolean;
  recoveryEmailDate?: string;
  recovered: boolean;
  convertedToOrderId?: string;
};

type RecoveryStatus = 'all' | 'abandoned' | 'emailSent' | 'recovered';

export default function AbandonedCartsPage() {
  const [loading, setLoading] = useState(true);
  const [carts, setCarts] = useState<AbandonedCart[]>([]);
  const [filteredCarts, setFilteredCarts] = useState<AbandonedCart[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<RecoveryStatus>('all');
  const [timeFilter, setTimeFilter] = useState('30'); // Default to last 30 days
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Fetch abandoned carts
  useEffect(() => {
    fetchCarts();
    
    // Set up automatic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      console.log('[Admin] Auto-refreshing abandoned carts');
      fetchCarts();
    }, 30000);
    
    return () => clearInterval(refreshInterval);
  }, []);
  
  // Apply filters when data or filter values change
  useEffect(() => {
    applyFilters();
  }, [carts, searchQuery, statusFilter]);
  
  const fetchCarts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query parameters for API request
      const params = new URLSearchParams();
      
      if (timeFilter) {
        params.append('days', timeFilter);
      }
      
      // Fetch abandoned carts from API
      const response = await fetch(`/api/abandoned-carts?${params.toString()}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch abandoned carts');
      }
      
      setCarts(data.carts);
      setFilteredCarts(data.carts);
    } catch (err) {
      setError('Failed to fetch abandoned carts. Please try again later.');
      console.error('Error fetching abandoned carts:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const applyFilters = () => {
    // Local filtering for UI response
    let filtered = [...carts];
    
    // Filter by recovery status
    if (statusFilter === 'abandoned') {
      filtered = filtered.filter(cart => !cart.recovered && !cart.recoveryEmailSent);
    } else if (statusFilter === 'emailSent') {
      filtered = filtered.filter(cart => !cart.recovered && cart.recoveryEmailSent);
    } else if (statusFilter === 'recovered') {
      filtered = filtered.filter(cart => cart.recovered);
    }
    
    // Filter by search query (email)
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(cart => 
        cart.customerInfo.email?.toLowerCase().includes(query) ||
        cart.customerInfo.name?.toLowerCase().includes(query) ||
        cart.sessionId.toLowerCase().includes(query)
      );
    }
    
    setFilteredCarts(filtered);
  };
  
  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchCarts(); // Refetch with new filter
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  const getTimePassed = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHrs < 24) {
      return `${diffHrs} hours ago`;
    } else {
      const diffDays = Math.floor(diffHrs / 24);
      return `${diffDays} days ago`;
    }
  };
  
  const handleSendRecoveryEmail = async (id: string) => {
    setActionLoading(id);
    
    try {
      const response = await fetch(`/api/abandoned-carts`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, action: 'sendRecoveryEmail' })
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to send recovery email');
      }
      
      // Update cart in state
      setCarts(prev => 
        prev.map(cart => 
          cart._id === id
            ? { ...cart, recoveryEmailSent: true, recoveryEmailDate: new Date().toISOString() }
            : cart
        )
      );
      
      // Also update filtered carts
      setFilteredCarts(prev => 
        prev.map(cart => 
          cart._id === id
            ? { ...cart, recoveryEmailSent: true, recoveryEmailDate: new Date().toISOString() }
            : cart
        )
      );
    } catch (error: any) {
      console.error('Error sending recovery email:', error);
      alert('Failed to send recovery email: ' + (error.message || 'Unknown error'));
    } finally {
      setActionLoading(null);
    }
  };
  
  const handleMarkAsRecovered = async (id: string) => {
    setActionLoading(id);
    
    try {
      const response = await fetch(`/api/abandoned-carts`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, action: 'markRecovered' })
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to mark cart as recovered');
      }
      
      // Update cart in state
      setCarts(prev => 
        prev.map(cart => 
          cart._id === id
            ? { ...cart, recovered: true }
            : cart
        )
      );
      
      // Also update filtered carts
      setFilteredCarts(prev => 
        prev.map(cart => 
          cart._id === id
            ? { ...cart, recovered: true }
            : cart
        )
      );
    } catch (error: any) {
      console.error('Error marking cart as recovered:', error);
      alert('Failed to mark cart as recovered: ' + (error.message || 'Unknown error'));
    } finally {
      setActionLoading(null);
    }
  };
  
  const handleDeleteCart = async (id: string) => {
    if (!confirm('Are you sure you want to delete this abandoned cart?')) {
      return;
    }
    
    setActionLoading(id);
    
    try {
      const response = await fetch(`/api/abandoned-carts/${id}`, {
        method: 'DELETE'
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete cart');
      }
      
      // Remove cart from state
      setCarts(prev => prev.filter(cart => cart._id !== id));
      setFilteredCarts(prev => prev.filter(cart => cart._id !== id));
    } catch (error: any) {
      console.error('Error deleting cart:', error);
      alert('Failed to delete cart: ' + (error.message || 'Unknown error'));
    } finally {
      setActionLoading(null);
    }
  };
  
  const getCartStatus = (cart: AbandonedCart) => {
    if (cart.recovered) {
      return { 
        label: 'Recovered', 
        className: 'bg-green-100 text-green-800',
        icon: <CheckCircle size={14} className="mr-1" />
      };
    } else if (cart.recoveryEmailSent) {
      return { 
        label: 'Email Sent', 
        className: 'bg-blue-100 text-blue-800',
        icon: <Mail size={14} className="mr-1" />
      };
    } else {
      return { 
        label: 'Abandoned', 
        className: 'bg-amber-100 text-amber-800', 
        icon: <AlertTriangle size={14} className="mr-1" />
      };
    }
  };
  
  // Function to create a test cart for debugging
  const createTestCart = async () => {
    try {
      setActionLoading('test-cart');
      const testCartData = {
        sessionId: 'test_session_' + Date.now(),
        customerInfo: {
          email: '<EMAIL>',
          name: 'Test User'
        },
        items: [
          {
            productId: 'test-product-1',
            name: 'Test Product 1',
            price: 99.99,
            quantity: 1,
            imageUrl: 'test-image.jpg'
          }
        ],
        subtotal: 99.99,
        userAgent: navigator.userAgent,
        referrer: document.referrer || null
      };
      
      const response = await fetch('/api/abandoned-carts/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCartData)
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert('Test cart created successfully. Refreshing list...');
        fetchCarts();
      } else {
        throw new Error(result.error || 'Failed to create test cart');
      }
    } catch (error) {
      console.error('Error creating test cart:', error);
      alert('Failed to create test cart: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(null);
    }
  };
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link href="/admin/orders" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-semibold">Abandoned Carts</h1>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={createTestCart}
            disabled={actionLoading === 'test-cart'}
            className="flex items-center py-2 px-4 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            {actionLoading === 'test-cart' ? (
              <span className="flex items-center">
                <span className="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></span>
                Creating...
              </span>
            ) : (
              <span>Create Test Cart</span>
            )}
          </button>
          <button 
            onClick={fetchCarts} 
            className="flex items-center py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
        </div>
      </div>
      
      {/* Filters */}
      <form onSubmit={handleFilterSubmit} className="bg-white rounded-md shadow-sm p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by email or customer name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          
          {/* Status Filter */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as RecoveryStatus)}
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
            >
              <option value="all">All Statuses</option>
              <option value="abandoned">Abandoned</option>
              <option value="emailSent">Email Sent</option>
              <option value="recovered">Recovered</option>
            </select>
          </div>
          
          {/* Time Filter */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Clock size={18} className="text-gray-400" />
            </div>
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
            >
              <option value="1">Last 24 Hours</option>
              <option value="7">Last 7 Days</option>
              <option value="30">Last 30 Days</option>
              <option value="90">Last 90 Days</option>
            </select>
          </div>
          
          <div>
            <button 
              type="submit"
              className="h-full w-full px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Apply
            </button>
          </div>
        </div>
      </form>
      
      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">
          {error}
        </div>
      )}
      
      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm text-gray-500 mb-1">Total Abandoned Carts</div>
          <div className="text-2xl font-bold">
            {loading ? '...' : carts.filter(cart => !cart.recovered).length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm text-gray-500 mb-1">Potential Revenue</div>
          <div className="text-2xl font-bold">
            {loading ? '...' : `$${carts
              .filter(cart => !cart.recovered)
              .reduce((sum, cart) => sum + cart.subtotal, 0)
              .toFixed(2)}`}
          </div>
        </div>
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm text-gray-500 mb-1">Recovery Rate</div>
          <div className="text-2xl font-bold">
            {loading ? '...' : 
              (carts.length === 0 ? '0%' : 
                `${Math.round(carts.filter(cart => cart.recovered).length / carts.length * 100)}%`
              )
            }
          </div>
        </div>
      </div>
      
      {/* Carts Table */}
      <div className="bg-white rounded-md shadow overflow-hidden">
        {loading ? (
          <div className="p-8 flex justify-center">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
          </div>
        ) : filteredCarts.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p className="mb-2 text-lg font-medium">No abandoned carts found</p>
            <p>Try adjusting your filters or check back later</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Abandoned On</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredCarts.map((cart) => {
                  const status = getCartStatus(cart);
                  
                  return (
                    <tr key={cart._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {cart.customerInfo.name || 'Anonymous User'}
                        </div>
                        {cart.customerInfo.email && (
                          <div className="text-sm text-gray-500">{cart.customerInfo.email}</div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 font-medium">
                          {cart.items.length} {cart.items.length === 1 ? 'item' : 'items'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {cart.items.slice(0, 2).map(item => 
                            <div key={item.productId} className="truncate max-w-xs">
                              {item.quantity}x {item.name}
                            </div>
                          )}
                          {cart.items.length > 2 && (
                            <div className="text-xs text-gray-400">
                              +{cart.items.length - 2} more items
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatDate(cart.createdAt)}</div>
                        <div className="text-xs text-gray-500">{getTimePassed(cart.createdAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        ${cart.subtotal.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${status.className}`}>
                          {status.icon}
                          {status.label}
                        </span>
                        {cart.recoveryEmailSent && cart.recoveryEmailDate && (
                          <div className="text-xs text-gray-500 mt-1">
                            Email sent {getTimePassed(cart.recoveryEmailDate)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right space-x-2">
                        {!cart.recovered && (
                          <>
                            {!cart.recoveryEmailSent && (
                              <button
                                onClick={() => handleSendRecoveryEmail(cart._id)}
                                disabled={actionLoading === cart._id}
                                className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                              >
                                <Mail size={16} className="mr-1" />
                                {actionLoading === cart._id ? 'Sending...' : 'Send Email'}
                              </button>
                            )}
                            <button
                              onClick={() => handleMarkAsRecovered(cart._id)}
                              disabled={actionLoading === cart._id}
                              className="text-green-600 hover:text-green-900 inline-flex items-center ml-2"
                            >
                              <CheckCircle size={16} className="mr-1" />
                              {actionLoading === cart._id ? 'Updating...' : 'Mark Recovered'}
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => handleDeleteCart(cart._id)}
                          disabled={actionLoading === cart._id}
                          className="text-red-600 hover:text-red-900 inline-flex items-center ml-2"
                        >
                          <Trash size={16} className="mr-1" />
                          {actionLoading === cart._id ? 'Deleting...' : 'Delete'}
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
} 