'use client';

import { useState } from 'react';

/**
 * Component for database operations like creating collections
 */
export default function DatabaseTools() {
  const [collectionName, setCollectionName] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  /**
   * Function to create a new collection in the database
   */
  const createCollection = async () => {
    // Validate collection name
    if (!collectionName || collectionName.trim() === '') {
      setResult({ 
        success: false, 
        message: 'Please enter a valid collection name' 
      });
      return;
    }

    try {
      setIsCreating(true);
      setResult(null);

      // Call API to create collection
      const response = await fetch('/api/create-collection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: collectionName }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult({ 
          success: true, 
          message: `Collection '${collectionName}' created successfully!` 
        });
        // Clear input field after successful creation
        setCollectionName('');
      } else {
        setResult({ 
          success: false, 
          message: data.error || 'Failed to create collection' 
        });
      }
    } catch (error) {
      console.error('Error creating collection:', error);
      setResult({ 
        success: false, 
        message: 'An error occurred while creating the collection' 
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 mt-6">
      <h2 className="text-xl font-bold mb-4">Database Tools</h2>
      
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
        <h3 className="text-lg font-semibold text-indigo-700 mb-3">Create a New Collection</h3>
        
        <div className="mb-4">
          <label htmlFor="collection-name" className="block text-sm font-medium text-gray-700 mb-1">
            Collection Name:
          </label>
          <input
            type="text"
            id="collection-name"
            value={collectionName}
            onChange={(e) => setCollectionName(e.target.value)}
            placeholder="Enter collection name"
            className="block w-full rounded-md border border-gray-300 py-2 px-3 text-gray-700 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          <p className="mt-1 text-sm text-gray-500">
            Collection names should be meaningful and descriptive of the data they will contain.
          </p>
        </div>
        
        <button
          onClick={createCollection}
          disabled={isCreating || !collectionName}
          className={`px-4 py-2 rounded-md text-white font-medium ${
            isCreating || !collectionName 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-indigo-600 hover:bg-indigo-700'
          } transition-colors`}
        >
          {isCreating ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating...
            </span>
          ) : (
            'Create Collection'
          )}
        </button>
        
        {/* Result message */}
        {result && (
          <div className={`mt-4 p-3 rounded-md ${
            result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-sm ${
              result.success ? 'text-green-700' : 'text-red-700'
            }`}>
              {result.message}
            </p>
          </div>
        )}
      </div>
      
      <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">About MongoDB Collections</h4>
        <p className="text-gray-700 text-sm">
          In MongoDB, collections are equivalent to tables in relational databases. 
          They store documents (JSON-like data) and can have different schema for each document.
        </p>
        <p className="text-gray-700 text-sm mt-2">
          When you create a new collection, it will initially be empty. You'll need to 
          add documents to it separately using your application logic or MongoDB tools.
        </p>
      </div>
    </div>
  );
} 