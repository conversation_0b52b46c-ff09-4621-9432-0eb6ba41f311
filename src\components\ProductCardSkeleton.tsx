'use client';

interface ProductCardSkeletonProps {
  className?: string;
}

const ProductCardSkeleton = ({ className = "" }: ProductCardSkeletonProps) => {
  return (
    <div className={`${className} animate-pulse`}>
      {/* Image skeleton */}
      <div className="aspect-square bg-gray-200 rounded-lg mb-3"></div>
      
      {/* Product name skeleton */}
      <div className="h-4 bg-gray-200 rounded mb-2"></div>
      
      {/* Price skeleton */}
      <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
      
      {/* Weight skeleton */}
      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
    </div>
  );
};

export default ProductCardSkeleton; 