import mongoose, { Schema } from 'mongoose';

export interface SystemLogDocument extends mongoose.Document {
  userId: string;
  userName: string;
  action: string;
  resource: string;
  details: any;
  ipAddress: string;
  userAgent: string;
  status: 'success' | 'failure';
  createdAt: Date;
}

// Define system log schema for tracking admin activities
const SystemLogSchema = new Schema(
  {
    userId: { 
      type: String, 
      required: true,
      index: true 
    },
    userName: { 
      type: String, 
      required: true 
    },
    action: { 
      type: String, 
      required: true,
      enum: ['create', 'update', 'delete', 'view', 'login', 'logout', 'other'],
      index: true
    },
    resource: { 
      type: String, 
      required: true,
      index: true
    },
    details: { 
      type: Schema.Types.Mixed,
      default: {}
    },
    ipAddress: { 
      type: String 
    },
    userAgent: { 
      type: String 
    },
    status: { 
      type: String,
      enum: ['success', 'failure'],
      default: 'success',
      index: true
    }
  },
  { 
    timestamps: true 
  }
);

// Add indexes for common queries
SystemLogSchema.index({ createdAt: -1 });
SystemLogSchema.index({ userId: 1, createdAt: -1 });
SystemLogSchema.index({ resource: 1, action: 1 });

// Static method to log an activity
SystemLogSchema.statics.logActivity = async function(logData: Partial<SystemLogDocument>) {
  return this.create(logData);
};

// Static method to get logs for a specific user
SystemLogSchema.statics.getUserLogs = async function(userId: string, limit = 100) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Check if model exists before creating to prevent model overwrite during hot reloading
export const SystemLog = mongoose.models.SystemLog || mongoose.model<SystemLogDocument>('SystemLog', SystemLogSchema); 