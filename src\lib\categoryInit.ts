import { connectToDatabase } from './mongodb';
import { Category } from '@/models/Category';

export async function initializeCategories() {
  try {
    await connectToDatabase();
    
    // Check if categories already exist
    const count = await Category.countDocuments();
    
    // If categories already exist, don't initialize
    if (count > 0) {
      console.log('Categories already initialized, skipping...');
      return;
    }
    
    console.log('Initializing categories...');
    
    // Define initial categories
    const initialCategories = [
      {
        name: 'Faceted Gems',
        subcategories: [
          { name: 'Tourmaline' },
          { name: 'Emerald' },
          { name: 'Kunzite' },
          { name: 'Perido<PERSON>' },
          { name: 'Gar<PERSON>' },
          { name: '<PERSON><PERSON>' },
          { name: '<PERSON><PERSON>' },
          { name: '<PERSON>' },
          { name: 'Sapphire' },
          { name: 'Amethyst' },
          { name: 'Ametrine' },
          { name: 'Aquamarine' },
          { name: 'Tanzanite' },
        ]
      },
      {
        name: 'Rough Gems',
        subcategories: [
          { name: 'Specimens' },
          { name: 'Faceted Rough' },
          { name: '<PERSON>' },
        ]
      },
      {
        name: 'Jewelry',
        subcategories: [
          { name: 'Bracelet' },
          { name: 'Ear<PERSON>' },
          { name: 'Necklace' },
          { name: 'Custom' },
        ]
      }
    ];
    
    // Insert all categories
    await Category.insertMany(initialCategories);
    
    console.log('Categories initialized successfully!');
  } catch (error) {
    console.error('Error initializing categories:', error);
  }
} 