import mongoose from 'mongoose';

/**
 * Global variable to cache the database connection
 * This prevents multiple connections in development with hot reload
 */
interface ConnectionCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// Initialize the connection cache
const globalForMongoose = global as unknown as {
  mongoose: ConnectionCache;
};

if (!globalForMongoose.mongoose) {
  globalForMongoose.mongoose = { conn: null, promise: null };
}

/**
 * MongoDB connection string - should be stored in environment variables for security
 */
const MONGODB_URI = process.env.MONGODB_URI || '';

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}

/**
 * Function to connect to MongoDB
 */
export default async function connectDB() {
  if (globalForMongoose.mongoose.conn) {
    return globalForMongoose.mongoose.conn;
  }

  if (!globalForMongoose.mongoose.promise) {
    const opts = {
      bufferCommands: false,
    };

    globalForMongoose.mongoose.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }

  try {
    globalForMongoose.mongoose.conn = await globalForMongoose.mongoose.promise;
    return globalForMongoose.mongoose.conn;
  } catch (e) {
    globalForMongoose.mongoose.promise = null;
    throw e;
  }
} 