import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';
import { getCloudFrontChunkedVideoUrl } from '@/lib/cloudfront';
import { 
  S3Client, 
  ListObjectsV2Command,
} from "@aws-sdk/client-s3";

// Configure AWS S3
const region = process.env.AWS_S3_REGION || 'us-east-1';
const chunkedVideosBucketName = process.env.S3_CHUNKED_VIDEOS_BUCKET || 'videosbucket2025';

// Get S3 client
const getS3Client = () => {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
    }
  });
};

/**
 * Synchronize S3 files with database URLs
 * This function checks if the chunked video URL in the database matches actual files in S3
 * If it doesn't, it will find the matching file in S3 and update the database
 */
async function syncChunkedVideoUrl(videoId: string) {
  try {
    await connectToDatabase();
    
    // Get video details
    const video = await HeroVideo.findById(videoId);
    if (!video || !video.isChunked) return null;
    
    const s3Client = getS3Client();
    
    // Get the potential folder paths from the video type and timestamp
    // Extract timestamp from current URL if possible
    let timestamp = "";
    const currentUrl = video.chunkedVideoUrl || "";
    
    // Try to extract timestamp from URL using regex
    const timestampMatch = currentUrl.match(/mobile-(\d+)/);
    if (timestampMatch && timestampMatch[1]) {
      timestamp = timestampMatch[1];
    }
    
    // Form potential path prefixes
    const prefixOptions = [
      `chunked-videos/${video.type}-${timestamp}`,
      `chunked-videos/${video.type}-hero-${timestamp}`,
      `chunked-videos`
    ];
    
    // Try to find files in S3 that match our video
    let matchingFile = null;
    
    // Try each prefix option
    for (const prefix of prefixOptions) {
      const listCommand = new ListObjectsV2Command({
        Bucket: chunkedVideosBucketName,
        Prefix: prefix,
        MaxKeys: 100
      });
      
      try {
        const response = await s3Client.send(listCommand);
        if (response.Contents && response.Contents.length > 0) {
          // Look for m3u8 files that contain our video type
          const m3u8Files = response.Contents.filter(obj => 
            obj.Key && obj.Key.endsWith('.m3u8') && obj.Key.includes(video.type)
          );
          
          if (m3u8Files.length > 0) {
            // We found potential matching files
            // Prioritize _hls.m3u8 files as they're the main manifest
            const hlsManifests = m3u8Files.filter(obj => obj.Key && obj.Key.includes('_hls.m3u8'));
            
            if (hlsManifests.length > 0) {
              matchingFile = hlsManifests[0].Key;
              break;
            } else if (m3u8Files.length > 0) {
              matchingFile = m3u8Files[0].Key;
              break;
            }
          }
        }
      } catch (err) {
        console.error(`Error listing objects with prefix ${prefix}:`, err);
        continue;
      }
    }
    
    // If we found a matching file and it's different from the current URL
    if (matchingFile && !currentUrl.includes(matchingFile)) {
      console.log(`Fixing URL mismatch for video ${videoId}. 
        Current: ${currentUrl}
        New: ${chunkedVideosBucketName}/${matchingFile}`);
      
      // Update the database with the correct URL using direct CloudFront (with proxy fallback)
      const directCloudFrontUrl = getCloudFrontChunkedVideoUrl(matchingFile);
      video.chunkedVideoUrl = directCloudFrontUrl || `/api/hls-proxy/${matchingFile}`;
      await video.save();
      
      return {
        fixed: true,
        oldUrl: currentUrl,
        newUrl: video.chunkedVideoUrl
      };
    }
    
    return { fixed: false };
  } catch (error: any) {
    console.error('Error syncing chunked video URL:', error);
    return { fixed: false, error: error.message };
  }
}

/**
 * Synchronize all chunked video URLs with S3 files
 * 
 * GET /api/hero-videos/sync
 */
export async function GET(request: Request) {
  try {
    await connectToDatabase();
    
    // Find all chunked videos
    const chunkedVideos = await HeroVideo.find({
      isChunked: true,
      chunkingStatus: 'completed'
    });
    
    if (chunkedVideos.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No chunked videos found to sync',
        fixedCount: 0
      });
    }
    
    // Sync each video URL
    const results = await Promise.all(
      chunkedVideos.map(video => syncChunkedVideoUrl(video._id.toString()))
    );
    
    // Count fixed videos
    const fixedCount = results.filter(result => result && result.fixed).length;
    
    return NextResponse.json({
      success: true,
      message: `Synced ${chunkedVideos.length} videos, fixed ${fixedCount} URL mismatches`,
      fixedCount,
      totalVideos: chunkedVideos.length,
      fixedVideos: results.filter(result => result && result.fixed)
    });
    
  } catch (error: any) {
    console.error('Error syncing video URLs:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to sync video URLs'
    }, { status: 500 });
  }
} 