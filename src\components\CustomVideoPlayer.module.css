/* Custom styles for the range input (scrubber) matching the HTML example */
.customRange {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px;
  background: #d1d5db;
  outline: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  touch-action: none;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.customRange:active {
  cursor: grabbing;
}

/* Thumb style for Webkit (Chrome, Safari) */
.customRange::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  margin-top: -4px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.customRange:active::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.4);
}

/* Thumb style for Firefox */
.customRange::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
  border: none;
  transition: all 0.2s ease;
}

.customRange:active::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.4);
}

/* Track fill for Webkit (Chrome, Safari) */
.customRange::-webkit-slider-runnable-track {
  background: #d1d5db;
  border-radius: 4px;
  height: 8px;
}

/* Track fill for Firefox */
.customRange::-moz-range-track {
  background: #d1d5db;
  border-radius: 4px;
  height: 8px;
}

/* Controls container styling */
.controlsContainer {
  background: #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.controlButton {
  flex-shrink: 0;
  color: #374151;
  transition: color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.controlButton:hover {
  color: #111827;
}

.controlButton:focus {
  outline: none;
}

.progressContainer {
  flex-grow: 1;
}

/* Volume control container */
.volumeContainer {
  position: relative;
  display: flex;
  align-items: center;
}

/* Vertical volume slider container */
.volumeSlider {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 100px;
  background: #e5e7eb;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  margin-bottom: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  touch-action: none;
}

.volumeSlider.visible {
  opacity: 1;
  visibility: visible;
}

/* Custom vertical range input for volume */
.volumeRange {
  -webkit-appearance: none;
  appearance: none;
  width: 100px;
  height: 8px;
  background: #d1d5db;
  outline: none;
  border-radius: 4px;
  cursor: pointer;
  transform: rotate(-90deg);
  transform-origin: center;
  touch-action: none;
}

/* Thumb style for Webkit (Chrome, Safari) */
.volumeRange::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

/* Thumb style for Firefox */
.volumeRange::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
  border: none;
}

/* Volume tooltip */
.volumeTooltip {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
}

.volumeContainer:hover .volumeTooltip {
  opacity: 1;
  visibility: visible;
} 