import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Fetch exchange rates from a free API
    const response = await fetch('https://open.er-api.com/v6/latest/USD');
    
    if (!response.ok) {
      throw new Error('Failed to fetch exchange rates');
    }
    
    const data = await response.json();
    
    // Return only the rates we need
    return NextResponse.json({
      success: true,
      rates: {
        USD: 1, // Base currency
        EUR: data.rates.EUR || 0.93 // Fallback value if API fails
      },
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    
    // Return fallback rates if the API call fails
    return NextResponse.json({
      success: false,
      rates: {
        USD: 1,
        EUR: 0.93 // Default fallback rate
      },
      lastUpdated: new Date().toISOString(),
      error: 'Failed to fetch exchange rates'
    });
  }
} 