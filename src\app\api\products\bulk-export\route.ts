import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Product } from '@/models/Product';
import { parse } from 'json2csv';

// Function to get CSV field names with nested fields
const getFieldNames = () => [
  '_id',
  'name',
  'description',
  'price',
  'category',
  'imageUrl',
  'videoUrl',
  'inventory.inStock',
  'inventory.lowStockThreshold',
  'createdAt'
];

// Function to flatten a nested object structure for CSV
function flattenObject(obj: any, prefix = '') {
  const flattened: any = {};
  
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(flattened, flattenObject(obj[key], `${prefix}${key}.`));
    } else {
      flattened[`${prefix}${key}`] = obj[key];
    }
  }
  
  return flattened;
}

export async function GET() {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Get all products
    const products = await Product.find().sort({ createdAt: -1 });
    
    if (!products || products.length === 0) {
      return NextResponse.json({ success: false, error: 'No products found' }, { status: 404 });
    }
    
    // Flatten nested structure for CSV
    const flattenedProducts = products.map(product => {
      const plainProduct = product.toObject(); // Convert Mongoose document to plain object
      return flattenObject(plainProduct);
    });
    
    try {
      // Define fields to include in the CSV
      const fields = getFieldNames();
      
      // Generate CSV from product data
      const csv = parse(flattenedProducts, { fields });
      
      // Return CSV as response
      return new NextResponse(csv, { 
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="products-export-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    } catch (csvError: any) {
      console.error('Error generating CSV:', csvError);
      return NextResponse.json({
        success: false,
        error: 'Failed to generate CSV file',
        details: csvError.message
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error exporting products:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to export products'
    }, { status: 500 });
  }
} 