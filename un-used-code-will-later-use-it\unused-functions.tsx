// Unused functions from the home page

// Function to toggle video play/pause
export const toggleVideoPlay = (videoId: string) => {
  const videoElement = document.getElementById(`video-${videoId}`) as HTMLVideoElement;
  if (videoElement) {
    if (videoElement.paused) {
      videoElement.play();
    } else {
      videoElement.pause();
    }
  }
};

// Function to toggle play/pause for hardcoded videos
export const toggleHardcodedVideoPlay = (videoId: string, setPlayingState: React.Dispatch<React.SetStateAction<boolean>>, isPlaying: boolean) => {
  const videoElement = document.getElementById(videoId) as HTMLVideoElement;
  if (videoElement) {
    if (videoElement.paused) {
      videoElement.play();
      setPlayingState(true);
    } else {
      videoElement.pause();
      setPlayingState(false);
    }
  }
};

// Retry function with exponential backoff for Featured Products
export const retryFeaturedProducts = async (fetchProducts: () => void, retryAttempts: number, setRetryAttempts: React.Dispatch<React.SetStateAction<number>>, setFeaturedProductsLoading: React.Dispatch<React.SetStateAction<boolean>>) => {
  if (retryAttempts >= 3) return; // Max 3 retries
  
  setRetryAttempts(prev => prev + 1);
  setFeaturedProductsLoading(true);
  
  // Exponential backoff: 1s, 2s, 4s
  const delay = Math.pow(2, retryAttempts) * 1000;
  setTimeout(() => {
    fetchProducts();
  }, delay);
};

// Retry function with exponential backoff for Latest Products
export const retryLatestProducts = async (fetchProducts: () => void, latestProductsRetryAttempts: number, setLatestProductsRetryAttempts: React.Dispatch<React.SetStateAction<number>>, setLatestProductsLoading: React.Dispatch<React.SetStateAction<boolean>>) => {
  if (latestProductsRetryAttempts >= 3) return; // Max 3 retries
  
  setLatestProductsRetryAttempts(prev => prev + 1);
  setLatestProductsLoading(true);
  
  // Exponential backoff: 1s, 2s, 4s
  const delay = Math.pow(2, latestProductsRetryAttempts) * 1000;
  setTimeout(() => {
    fetchProducts();
  }, delay);
}; 