import mongoose, { Schema } from 'mongoose';

// Define the Campaign schema
const campaignSchema = new Schema({
  type: {
    type: String,
    enum: ['email', 'sms'],
    required: true
  },
  subject: {
    type: String,
    required: function() {
      return this.type === 'email';
    }
  },
  content: {
    type: String,
    required: true
  },
  recipientGroup: {
    type: String,
    enum: ['all', 'new', 'active', 'inactive', 'vip', 'manual'],
    required: true
  },
  recipients: [{
    type: String,
    validate: {
      validator: function(v: string) {
        // Simple email regex for validation
        return /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(v);
      },
      message: (props: any) => `${props.value} is not a valid email address!`
    }
  }],
  scheduledAt: {
    type: Date,
    required: function() {
      return this.status === 'scheduled';
    }
  },
  sentAt: {
    type: Date,
    default: null
  },
  status: {
    type: String,
    enum: ['scheduled', 'sent', 'failed', 'cancelled'],
    default: 'scheduled'
  },
  stats: {
    delivered: { type: Number, default: 0 },
    opened: { type: Number, default: 0 },
    clicked: { type: Number, default: 0 },
    bounced: { type: Number, default: 0 },
    complained: { type: Number, default: 0 }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create or get the Campaign model
const Campaign = mongoose.models.Campaign || mongoose.model('Campaign', campaignSchema);

export default Campaign; 