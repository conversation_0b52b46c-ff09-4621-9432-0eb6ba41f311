import { S3Client, ListObjectsV2Command, HeadBucketCommand } from '@aws-sdk/client-s3';

/**
 * Configuration for S3 client
 * We should get these from environment variables for security
 */
const s3Config = {
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

/**
 * S3 client instance for reuse
 */
let s3Client: S3Client | null = null;

/**
 * Function to get or create S3 client
 */
function getS3Client(): S3Client {
  if (!s3Client) {
    // Check if credentials are present
    if (!s3Config.credentials.accessKeyId || !s3Config.credentials.secretAccessKey) {
      console.warn('AWS credentials are missing or empty. S3 operations will fail.');
    }
    
    // Create a new S3 client
    s3Client = new S3Client(s3Config);
  }
  
  return s3Client;
}

/**
 * Type definition for S3 connection status response
 */
export type S3ConnectionStatusResponse = {
  status: 'connected' | 'error';
  bucketName: string;
  error?: {
    message: string;
    code?: string;
    name?: string;
    explanation: string;
  } | null;
  objects?: {
    count: number;
    names: string[];
  } | null;
};

/**
 * Function to check if an S3 bucket exists and is accessible
 */
export async function checkS3BucketConnection(bucketName: string): Promise<S3ConnectionStatusResponse> {
  try {
    // Get S3 client
    const client = getS3Client();
    
    // If bucket name is empty, return an error
    if (!bucketName) {
      return {
        status: 'error',
        bucketName: 'undefined',
        error: {
          message: 'Bucket name is empty',
          explanation: 'No bucket name was provided. Please check your environment variables.'
        }
      };
    }
    
    // First check if bucket exists and is accessible
    await client.send(new HeadBucketCommand({ Bucket: bucketName }));
    
    // If no error thrown, the bucket exists and is accessible
    // Now try to list objects to verify read permissions
    const listObjectsResponse = await client.send(
      new ListObjectsV2Command({ 
        Bucket: bucketName,
        MaxKeys: 10 // Limit to 10 objects for performance
      })
    );
    
    // Extract object keys (filenames)
    const objectKeys = listObjectsResponse.Contents?.map(obj => obj.Key || '') || [];
    
    // Return successful connection with object count
    return {
      status: 'connected',
      bucketName,
      objects: {
        count: listObjectsResponse.KeyCount || 0,
        names: objectKeys
      }
    };
  } catch (error: any) {
    // Handle connection errors
    return {
      status: 'error',
      bucketName,
      error: {
        message: error.message || 'Unknown error',
        code: error.Code || error.code,
        name: error.name,
        explanation: getS3ErrorExplanation(error)
      }
    };
  }
}

/**
 * Function to provide simple explanations for technical S3 error details
 */
function getS3ErrorExplanation(error: any): string {
  // Extract the error code which is often most useful for diagnosis
  const errorCode = error.Code || error.code;
  
  if (error.message?.includes('Missing credentials')) {
    return "AWS credentials are missing. Make sure you've set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your environment variables.";
  }
  
  switch (errorCode) {
    case 'NoSuchBucket':
      return `The bucket doesn't exist or you don't have permission to access it.`;
    case 'AccessDenied':
      return `Access was denied. Your AWS credentials don't have permission to access this bucket.`;
    case 'InvalidAccessKeyId':
      return `The AWS access key ID you provided is invalid.`;
    case 'SignatureDoesNotMatch':
      return `The signature calculated does not match the signature provided. Check your AWS secret access key.`;
    case 'NetworkingError':
      return `Network error occurred. Check your internet connection.`;
    case 'ConnectionError':
      return `Failed to connect to AWS. Check your internet connection and AWS region settings.`;
    case 'NotFound':
      return `The bucket wasn't found. Check the bucket name and region.`;
    case 'Forbidden':
      return `Access to the bucket is forbidden with your current credentials.`;
    default:
      return `There was a problem connecting to the S3 bucket. Technical error: ${error.message || 'Unknown error'}`;
  }
} 