'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '@/contexts/LanguageContext';
import '@/styles/about.css';

export default function AboutPage() {
  const { translations } = useLanguage();
  
  return (
    <div className="min-h-screen bg-gradient-to-r from-[#f8f8f8] via-[#f8f8f8] to-[#f8f8f8]">
      {/* Who We Are */}
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col md:flex-row items-center justify-between gap-10">
          <div className="w-full md:w-1/2 relative h-[50vh] md:h-[450px] rounded-lg overflow-hidden">
            <Image 
              src="/images/about/who-we-are-image.jpg" 
              alt="Who We Are" 
              fill
              className="object-cover"
            />
          </div>
          <div className="w-full md:w-1/2 px-4 text-center">
            <h2 className="about-heading-text">{translations.who_we_are}</h2>
            <p className="about-body-text">
              {translations.who_we_are_text}
            </p>
          </div>
        </div>
      </div>

      {/* Founder */}
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col-reverse md:flex-row items-center justify-between gap-10">
          <div className="w-full md:w-1/2 px-4">
            <h2 className="about-heading-text">{translations.founder}</h2>
            <p className="about-body-text">
              {translations.founder_text_1}
            </p>
            <p className="about-body-text mt-4">
              {translations.founder_text_2}
            </p>
            <p className="about-body-text mt-4">
              {translations.founder_text_3}
            </p>
            <p className="about-body-text mt-4">
              {translations.founder_text_4}
            </p>
          </div>
          <div className="w-full md:w-1/2 relative h-[50vh] md:h-[500px] rounded-lg overflow-hidden">
            <Image 
              src="/images/about/founder-image.jpg" 
              alt="Founder" 
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Co-Founder */}
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col md:flex-row items-center justify-between gap-10">
          <div className="w-full md:w-1/2 relative h-[50vh] md:h-[450px] rounded-lg overflow-hidden">
            <Image 
              src="/images/about/co-founder-image.jpg" 
              alt="Co-Founder" 
              fill
              className="object-cover"
            />
          </div>
          <div className="w-full md:w-1/2 px-4">
            <h2 className="about-heading-text">{translations.co_founder}</h2>
            <p className="about-body-text">
              {translations.co_founder_text_1}
            </p>
            <p className="about-body-text mt-4">
              {translations.co_founder_text_2}
            </p>
          </div>
        </div>
      </div>

      {/* Our History */}
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col-reverse md:flex-row items-center justify-between gap-10">
          <div className="w-full md:w-1/2 px-4">
            <h2 className="about-heading-text">{translations.our_history}</h2>
            <p className="about-body-text">
              {translations.our_history_text_1}
            </p>
            <p className="about-body-text mt-4">
              {translations.our_history_text_2}
            </p>
            <p className="about-body-text mt-4">
              {translations.our_history_text_3}
            </p>
          </div>
          <div className="w-full md:w-1/2 relative h-[50vh] md:h-[500px] rounded-lg overflow-hidden">
            <Image 
              src="/images/about/our-history-image.jpg" 
              alt="Our History" 
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Company Experience */}
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col md:flex-row items-center justify-between gap-10">
          <div className="w-full md:w-1/2 relative h-[50vh] md:h-[450px] rounded-lg overflow-hidden">
            <Image 
              src="/images/about/company-image.jpg" 
              alt="Company" 
              fill
              className="object-cover"
            />
          </div>
          <div className="w-full md:w-1/2 px-4">
            <p className="about-body-text">
              {translations.company_experience_text_1}
            </p>
            <p className="about-body-text mt-4">
              {translations.company_experience_text_2}
            </p>
            <p className="about-body-text mt-4">
              {translations.company_experience_text_3}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 