import nodemailer from 'nodemailer';

// Email interface
interface EmailContent {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

// Send email function using Gmail SMTP
export const sendEmail = async (content: EmailContent): Promise<boolean> => {
  try {
    // Create a transporter with Gmail SMTP settings
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    });
    
    // Prepare email options
    const mailOptions = {
      from: content.from || process.env.EMAIL_FROM || `"Afghan Int'l Gems" <${process.env.GMAIL_USER}>`,
      to: content.to,
      subject: content.subject,
      html: content.html,
    };
    
    // Send the email
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    return !!info.messageId;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}; 