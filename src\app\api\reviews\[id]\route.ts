import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Review } from '@/models/Review';

// Get a single review by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Get review by ID
    const review = await Review.findById(params.id).populate('productId');
    
    if (!review) {
      return NextResponse.json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      review
    });
  } catch (error: any) {
    console.error('Error fetching review:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch review'
    }, { status: 500 });
  }
}

// Update a review (moderate)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate status
    const { status, moderatedBy, moderationNotes } = body;
    
    if (!status || !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid status value. Must be pending, approved, or rejected.'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Find review
    const review = await Review.findById(params.id);
    
    if (!review) {
      return NextResponse.json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }
    
    // Update review status and moderation info
    review.status = status;
    if (moderatedBy) review.moderatedBy = moderatedBy;
    if (moderationNotes) review.moderationNotes = moderationNotes;
    review.moderationDate = new Date();
    
    await review.save();
    
    return NextResponse.json({
      success: true,
      message: `Review has been ${status}`,
      review
    });
  } catch (error: any) {
    console.error('Error updating review:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update review'
    }, { status: 500 });
  }
}

// Delete a review
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Delete review
    const deletedReview = await Review.findByIdAndDelete(params.id);
    
    if (!deletedReview) {
      return NextResponse.json({
        success: false,
        error: 'Review not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting review:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete review'
    }, { status: 500 });
  }
} 