'use client';

import Link from 'next/link';
import { useState, useEffect, useRef, Suspense } from 'react';
import { ShoppingCart, Search, Heart, Menu, X, User, LogOut, MessageSquare, ChevronDown } from 'lucide-react';
import { useCartStore } from '@/store/useCartStore';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Image from 'next/image';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import CurrencySwitcher from '@/components/CurrencySwitcher';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import '@/components/footer/Footer.css';
import './header-desktop-layout.css';

// Define cart item type
type CartItem = {
  product: {
    _id: string;
    name: string;
    price: number;
    [key: string]: any;
  };
  quantity: number;
};

// Define category types
type Subcategory = {
  _id: string;
  name: string;
};

type Category = {
  _id: string;
  name: string;
  subcategories: Subcategory[];
};

// Define search result type
type SearchResult = {
  _id: string;
  name: string;
  price: number;
  images?: string[];
  imageUrl?: string;
  category?: string;
};

function HeaderContent() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const initialDataLoadedRef = useRef(false);
  const [categories, setCategories] = useState<Category[]>([
    { 
      _id: 'faceted-gems-temp', 
      name: 'Faceted Gems', 
      subcategories: [
        { _id: 'tourmaline-temp', name: 'Tourmaline' },
        { _id: 'emerald-temp', name: 'Emerald' },
        { _id: 'kunzite-temp', name: 'Kunzite' },
        { _id: 'peridot-temp', name: 'Peridot' },
        { _id: 'garnet-temp', name: 'Garnet' },
        { _id: 'topaz-temp', name: 'Topaz' },
        { _id: 'spinel-temp', name: 'Spinel' },
        { _id: 'ruby-temp', name: 'Ruby' },
        { _id: 'sapphire-temp', name: 'Sapphire' },
        { _id: 'amethyst-temp', name: 'Amethyst' },
        { _id: 'ametrine-temp', name: 'Ametrine' },
        { _id: 'aquamarine-temp', name: 'Aquamarine' },
        { _id: 'tanzanite-temp', name: 'Tanzanite' }
      ] 
    },
    { 
      _id: 'jewelry-temp', 
      name: 'LapisLazuli', 
      subcategories: [
        { _id: 'bracelet-temp', name: 'Bracelet' },
        { _id: 'earings-temp', name: 'Earings' },
        { _id: 'necklace-temp', name: 'Necklace' },
        { _id: 'custom-temp', name: 'Custom' }
      ] 
    },
    { 
      _id: 'rough-gems-temp', 
      name: 'Rough Gems', 
      subcategories: [
        { _id: 'specimens-temp', name: 'Specimens' },
        { _id: 'faceted-rough-temp', name: 'Faceted Rough' },
        { _id: 'crystal-temp', name: 'Crystal' }
      ] 
    }
  ]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    'faceted-gems-temp': false,
    'jewelry-temp': false,
    'rough-gems-temp': false
  });
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  
  // Use the language context
  const { translations, translateCategoryName, translateSubcategoryName } = useLanguage();
  
  // Use the cart store
  const { openCart, getItemsCount } = useCartStore();
  
  // Use the auth contexts
  const { customer, isAuthenticated, logout } = useAuth();
  const { user, isAuthenticated: isAdminAuthenticated } = useAdminAuth();
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);

  // Refs for dropdown handling
  const dropdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Close mobile menu when pathname changes (navigation happens)
  useEffect(() => {
    setMobileMenuOpen(false);
    setIsNavigating(false); // Reset navigation state when route changes
  }, [pathname, searchParams]);

  // Disable body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      // Disable scroll
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    } else {
      // Re-enable scroll
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }

    // Cleanup function to ensure scroll is re-enabled
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    };
  }, [mobileMenuOpen]);

  // Check if we're on the home page to determine whether to use translations
  const isHomePage = pathname === '/' || pathname === '';


  // Set mounted state to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    if (!initialDataLoadedRef.current) {
      fetchCategories();
    }
  }, []);

  // Handle scroll detection for homepage header styling
  useEffect(() => {
    if (!isHomePage) {
      // On other pages, we can consider it "scrolled" for styling purposes
      // to ensure the solid background is always shown.
      setIsScrolled(true);
      return;
    }

    // This function will be used for both the initial check and the scroll event
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 500); // Change header after 500px scroll
    };

    // Perform an initial check as soon as we are on the home page
    handleScroll();

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isHomePage]);

  // Focus search input when search opens
  useEffect(() => {
    if (searchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [searchOpen]);

  // Handle search query changes
  useEffect(() => {
    const searchProducts = async () => {
      if (searchQuery.trim().length > 2) {
        setIsSearching(true);
        try {
          const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}&limit=5`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && Array.isArray(data.results)) {
              setSearchResults(data.results);
            } else {
              console.error('Search API error:', data.error || 'Unknown error');
              setSearchResults([]);
            }
          } else {
            console.error('Search API response not ok:', response.status, response.statusText);
            setSearchResults([]);
          }
        } catch (error) {
          console.error('Error searching products:', error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
      }
    };

    const debounceSearch = setTimeout(() => {
      searchProducts();
    }, 300);

    return () => clearTimeout(debounceSearch);
  }, [searchQuery]);
  
  const fetchCategories = async () => {
    try {
      if (initialDataLoadedRef.current) return; // Prevent duplicate fetches
      
      setLoading(true);
      const response = await fetch('/api/categories');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const apiData = await response.json();
      initialDataLoadedRef.current = true;
      
      // Merge API data with existing hardcoded data instead of replacing
      const mergedCategories = [...categories]; // Start with current hardcoded data
      
      // Create a map of existing categories by name for faster lookup
      const existingCategoriesMap = new Map(
        categories.map(cat => [cat.name.toLowerCase(), cat])
      );
      
      // For each category from the API
      apiData.forEach((apiCategory: Category) => {
        const lowerName = apiCategory.name.toLowerCase();
        const existingCategory = existingCategoriesMap.get(lowerName);
        
        if (existingCategory) {
          // Category exists - merge subcategories efficiently
          const existingSubcategoryNames = new Set(
            existingCategory.subcategories.map(sub => sub.name.toLowerCase())
          );
          
          // Add only new subcategories that don't exist in hardcoded data
          const newSubcategories = apiCategory.subcategories.filter(
            apiSub => !existingSubcategoryNames.has(apiSub.name.toLowerCase())
          );
          
          if (newSubcategories.length > 0) {
            // Find this category in our array to update
            const idx = mergedCategories.findIndex(c => c.name.toLowerCase() === lowerName);
            if (idx >= 0) {
              mergedCategories[idx] = {
                ...mergedCategories[idx],
                _id: apiCategory._id, // Keep the real API ID
                subcategories: [
                  ...mergedCategories[idx].subcategories,
                  ...newSubcategories
                ]
              };
            }
          }
        } else {
          // This is a completely new category not in our hardcoded data
          mergedCategories.push(apiCategory);
        }
      });
      
      // Update in a single batch to reduce renders
      setCategories(mergedCategories);
      
      // Update expanded state for all categories
      const expanded: Record<string, boolean> = {...expandedCategories};
      mergedCategories.forEach((category: Category) => {
        // Only add new ones, don't update existing ones
        if (!(category._id in expanded)) {
          expanded[category._id] = false;
        }
      });
      setExpandedCategories(expanded);
    } catch (err) {
      console.error('Error fetching categories:', err);
      initialDataLoadedRef.current = true; // Mark as loaded even on error
      // Keep the static data if fetch fails
    } finally {
      setLoading(false);
    }
  };
  
  const handleCategoryMouseEnter = (categoryId: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current);
    }
    setActiveCategory(categoryId);
  };
  
  const handleCategoryMouseLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
    }, 150);
  };
  
  const handleDropdownMouseEnter = () => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current);
    }
  };
  
  const handleDropdownMouseLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
    }, 150);
  };
  
  const toggleMobileCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };
  
  const handleLogout = async () => {
    await logout();
    setShowAccountDropdown(false);
  };

  const handleToggleSearch = () => {
    setSearchOpen(!searchOpen);
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchOpen(false);
      router.push(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleSearchResultClick = (productId: string) => {
    setSearchOpen(false);
    router.push(`/product/${productId}`);
  };

  // Mobile menu navigation handlers with proper animation sequence
  const handleMobileNavigation = (href: string) => {
    // Prevent multiple navigation calls
    if (isNavigating) {
      return;
    }

    // Check if we're already on the target route
    const currentPath = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    if (currentPath === href) {
      // Just close the menu if already on the target route
      setMobileMenuOpen(false);
      return;
    }

    // Smart navigation for shop page category filtering
    const isOnShopPage = pathname === '/shop';
    const isShopCategoryUrl = href.startsWith('/shop?');
    const isShopBaseUrl = href === '/shop';

    if (isOnShopPage && (isShopCategoryUrl || isShopBaseUrl)) {
      // We're on shop page and navigating within shop (category filter or clearing filters)
      // This prevents blank page and maintains shop page state
      setMobileMenuOpen(false);

      if (isShopBaseUrl) {
        // Clicking "Shop" to clear all filters - go to clean shop page
        router.replace('/shop');
      } else {
        // Clicking a category - update URL params for filtering
        const url = new URL(href, window.location.origin);
        const searchParams = url.searchParams;
        const newUrl = `${pathname}?${searchParams.toString()}`;
        router.replace(newUrl);
      }
      return;
    }

    // Check if this is a category navigation (optimize for speed)
    const isCategoryNavigation = isShopCategoryUrl || (href.includes('/shop?category='));

    if (isCategoryNavigation) {
      // For category navigation: immediate navigation with parallel menu close animation
      setIsNavigating(true);
      setMobileMenuOpen(false);

      // Navigate immediately without waiting for animation
      router.push(href);

      // Reset navigating state after navigation
      setTimeout(() => {
        setIsNavigating(false);
      }, 100);
      return;
    }

    // For non-category navigation: preserve original animation sequence
    // Set navigating state to prevent duplicate calls
    setIsNavigating(true);

    // Close the mobile menu first
    setMobileMenuOpen(false);

    // Wait for the menu animation to complete, then navigate
    setTimeout(() => {
      router.push(href);
      // Reset navigating state after a short delay to allow navigation to complete
      setTimeout(() => {
        setIsNavigating(false);
      }, 100);
    }, 300); // Match menu animation duration (300ms)
  };

  // Get the correct image URL for search results
  const getSearchResultImageUrl = (result: SearchResult): string => {
    if (result.imageUrl) {
      // If using CloudFront CDN
      if (process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN) {
        return `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${result.imageUrl}`;
      }
      // If imageUrl is a full path
      else if (result.imageUrl.startsWith('http') || result.imageUrl.startsWith('/')) {
        return result.imageUrl;
      }
    }
    
    // Fallback to images array if available
    if (result.images && result.images.length > 0) {
      return result.images[0];
    }
    
    // Fallback to a default gem image
    return '/images/sphene.jpg'; // Using an existing gem image as placeholder
  };

  // Determine if admin user or customer is logged in for display
  const displayName = user?.firstName || customer?.firstName;
  const displayLastName = user?.lastName || customer?.lastName;
  const displayEmail = user?.email || customer?.email;
  const isUserAuthenticated = isAdminAuthenticated || isAuthenticated;

  return (
    <header
      id="main-header"
      className={`${isHomePage ? 'fixed' : 'sticky'} top-0 left-0 w-full z-40 ${
        isHomePage && !isScrolled ? 'home-hero-bg-transparent' : 'scrolled-bg'
      } transition-all duration-300 ${
        mobileMenuOpen && isHomePage && !isScrolled ? 'backdrop-blur-sm bg-black/10' : ''
      }`}
    >
      {/* Search Overlay - shows when search is open */}
      {searchOpen && (
        <div id="header-search-overlay" className="absolute top-0 left-0 right-0 bg-[#f8f8f8] shadow-md z-50 h-full">
          <div id="header-search-container" className="max-w-[540px] sm:max-w-[720px] md:max-w-[960px] lg:max-w-[1140px] xl:max-w-[1320px] mx-auto px-4 h-full flex items-center">
            <form id="header-search-form" onSubmit={handleSearchSubmit} className="flex items-center w-full">
              <div id="header-search-input-wrapper" className="relative flex-1">
                <div id="header-search-input-container" className="flex items-center bg-white rounded-lg border border-gray-300 px-4 py-3">
                  <Search size={20} className="text-gray-400 mr-3" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search for products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 outline-none text-gray-700 text-base"
                  />
                  <button
                    type="button"
                    onClick={handleToggleSearch}
                    className="ml-3 text-gray-400 hover:text-gray-600"
                  >
                    <X size={20} />
                  </button>
                </div>
                
                {/* Search results dropdown */}
                {searchQuery.trim().length > 2 && (
                  <div id="header-search-results-dropdown" className="absolute left-0 right-0 mt-1 bg-[#f8f8f8] rounded-lg shadow-md max-h-[300px] overflow-y-auto z-50 border border-gray-200">
                    {isSearching ? (
                      <div className="p-4 text-center text-gray-500">Searching...</div>
                    ) : searchResults.length > 0 ? (
                      <div id="header-search-results-list">
                        {searchResults.map(result => (
                          <div 
                            key={result._id}
                            id={`header-search-result-${result._id}`}
                            className="p-3 hover:bg-gray-200 cursor-pointer border-b border-gray-200 last:border-0"
                            onClick={() => handleSearchResultClick(result._id)}
                          >
                            <div className="flex items-center">
                              <Image 
                                src={getSearchResultImageUrl(result)} 
                                alt={result.name}
                                width={40}
                                height={40}
                                className="rounded-md mr-3"
                                onError={(e) => {
                                  e.currentTarget.src = '/images/sphene.jpg';
                                }}
                              />
                              <div>
                                <p className="font-medium">{result.name}</p>
                                <p className="text-gray-500">${result.price.toFixed(2)}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div id="header-search-results-view-all" className="p-3 text-center">
                          <button 
                            className="text-blue-600 hover:text-blue-800 bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md transition-colors"
                            onClick={() => {
                              router.push(`/shop?search=${encodeURIComponent(searchQuery)}`);
                              setSearchOpen(false);
                            }}
                          >
                            View all results
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-700 font-medium">No results found</div>
                    )}
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      )}

      <div
        id="header-main-wrapper"
        className={`w-full px-4 sm:px-6 md:px-8 lg:px-10 py-3 sm:py-4 md:py-5`}
      >
        <div id="header-content-container" className="max-w-[540px] sm:max-w-[720px] md:max-w-[960px] lg:max-w-[1140px] xl:max-w-[1320px] mx-auto">
          {/* Mobile Layout (default & sm) */}
          <div id="header-mobile-layout" className="md:hidden grid grid-cols-12 items-center">
            <div id="header-mobile-logo" className="col-span-6 sm:col-span-4 justify-self-start">
              <Link href="/">
                <Image 
                  src="/images/main-logo.png" 
                  alt="Afghan International Gems Logo" 
                  width={80}
                  height={32}
                  className="w-[70px] sm:w-[80px] h-auto"
                  priority
                />
              </Link>
            </div>
            
            <div id="header-mobile-actions" className="col-span-6 sm:col-span-8 justify-self-end grid grid-cols-3 gap-8 items-center">
              <button
                className={`justify-self-center ${isHomePage && !isScrolled ? 'text-white hover:text-blue-300' : 'text-black hover:text-blue-600'} transition-colors`}
                onClick={handleToggleSearch}
              >
                <Search size={20} className="w-5 h-5" />
              </button>
              <button
                className={`relative justify-self-center ${isHomePage && !isScrolled ? 'text-white hover:text-blue-300' : 'text-black hover:text-blue-600'} transition-colors`}
                onClick={openCart}
                aria-label="Open cart"
              >
                <ShoppingCart size={20} className="w-5 h-5" />
                {mounted && getItemsCount() > 0 && (
                  <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full w-5 h-5 grid place-items-center">
                    {getItemsCount()}
                  </span>
                )}
              </button>
              <button
                className={`justify-self-center ${isHomePage && !isScrolled ? 'text-white hover:text-blue-300' : 'text-black hover:text-blue-600'} transition-colors`}
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <X size={20} className="w-5 h-5" />
                ) : (
                  <Menu size={20} className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>
          
          {/* Desktop Layout (md & lg) */}
          <div 
            id="header-desktop-layout"
          >
            {/* Logo Section */}
            <section 
              id="header-desktop-logo"
            >
              <Link href="/">
                <Image 
                  src="/images/main-logo.png" 
                  alt="Afghan International Gems Logo" 
                  width={100}
                  height={40}
                  priority
                />
              </Link>
            </section>
            
            {/* Navigation Section */}
            <nav 
              id="header-desktop-navigation"
            >
              <div 
                id="header-desktop-nav-links"
              >
                <Link
                  href="/"
                  className={`nav-link ${
                    isHomePage && !isScrolled 
                      ? 'home-hero-text' 
                      : 'scrolled-text'
                  } ${
                    pathname === '/' ? 'active' : ''
                  }`}
                >
                  <span className="relative">
                    {translations.home}
                    <span
                      className={`nav-link-underline ${
                        isHomePage && !isScrolled 
                          ? 'bg-white' 
                          : 'bg-black'
                      }`}
                    ></span>
                  </span>
                </Link>
                <Link
                  href="/about"
                  className={`nav-link ${
                    isHomePage && !isScrolled 
                      ? 'home-hero-text' 
                      : 'scrolled-text'
                  } ${
                    pathname === '/about' ? 'active' : ''
                  }`}
                >
                  <span className="relative">
                    {translations.about_us}
                    <span
                      className={`nav-link-underline ${
                        isHomePage && !isScrolled 
                          ? 'bg-white' 
                          : 'bg-black'
                      }`}
                    ></span>
                  </span>
                </Link>
                <Link
                  href="/shop"
                  className={`nav-link ${
                    isHomePage && !isScrolled 
                      ? 'home-hero-text' 
                      : 'scrolled-text'
                  } ${
                    pathname === '/shop' && !searchParams.get('category') ? 'active' : ''
                  }`}
                >
                  <span className="relative">
                    {translations.shop}
                    <span
                      className={`nav-link-underline ${
                        isHomePage && !isScrolled 
                          ? 'bg-white' 
                          : 'bg-black'
                      }`}
                    ></span>
                  </span>
                </Link>
                
                {/* Dynamic Category Links */}
                {categories.map(category => {
                  const categoryPath = `/shop?category=${encodeURIComponent(category.name)}`;
                  const isCategoryActive = pathname === '/shop' && searchParams?.get('category') === category.name;

                  return (
                    <div
                      key={category._id}
                      className="relative"
                      onMouseEnter={() => handleCategoryMouseEnter(category._id)}
                      onMouseLeave={handleCategoryMouseLeave}
                    >
                      <Link
                        href={categoryPath}
                        className={`nav-link ${
                          isHomePage && !isScrolled 
                            ? 'home-hero-text' 
                            : 'scrolled-text'
                        } ${isCategoryActive ? 'active' : ''}`}
                      >
                        <span className="relative flex items-center">
                          {translateCategoryName(category.name)}
                          {category.subcategories.length > 0 && (
                            <ChevronDown 
                              size={14} 
                              className={`ml-1 ${
                                isHomePage && !isScrolled 
                                  ? 'text-white' 
                                  : 'text-gray-700'
                              }`} 
                            />
                          )}
                          <span
                            className={`nav-link-underline ${
                              isHomePage && !isScrolled 
                                ? 'bg-white' 
                                : 'bg-black'
                            }`}
                          ></span>
                        </span>
                      </Link>
                      
                      {/* Dropdown Menu */}
                      {activeCategory === category._id && category.subcategories.length > 0 && (
                        <div 
                          className="absolute left-0 top-full mt-1 bg-gradient-to-b from-white to-[#f8f8f8] shadow-lg rounded-md min-w-[200px] z-50 border border-[#D1C29B] transition-opacity duration-150 ease-in-out"
                          style={{ backgroundColor: '#f8f8f8' }}
                          onMouseEnter={handleDropdownMouseEnter}
                          onMouseLeave={handleDropdownMouseLeave}
                        >
                          <div className="py-2">
                            {category.subcategories.map(sub => {
                              const subPath = `/shop?category=${encodeURIComponent(category.name)}&subcategory=${encodeURIComponent(sub.name)}`;
                              const isSubActive = pathname === '/shop' &&
                                searchParams?.get('category') === category.name &&
                                searchParams?.get('subcategory') === sub.name;

                              return (
                                <Link
                                  key={sub._id}
                                  href={subPath}
                                  className={`block px-4 py-2 text-sm hover:bg-[#e5e5e5] active:scale-95 active:bg-[#d0d0d0] active:text-black transition-all duration-150 ease-in-out ${
                                    isSubActive ? 'bg-[#d0d0d0]' : 'text-gray-800'
                                  }`}
                                  onClick={() => setActiveCategory(null)}
                                >
                                  {translateSubcategoryName(category.name, sub.name)}
                                </Link>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
                
                <Link
                  href="/blog"
                  className={`nav-link ${
                    isHomePage && !isScrolled 
                      ? 'home-hero-text' 
                      : 'scrolled-text'
                  } ${
                    pathname === '/blog' ? 'active' : ''
                  }`}
                >
                  <span className="relative">
                    {translations.blog}
                    <span
                      className={`nav-link-underline ${
                        isHomePage && !isScrolled 
                          ? 'bg-white' 
                          : 'bg-black'
                      }`}
                    ></span>
                  </span>
                </Link>
                <Link
                  href="/contact"
                  className={`nav-link ${
                    isHomePage && !isScrolled 
                      ? 'home-hero-text' 
                      : 'scrolled-text'
                  } ${
                    pathname === '/contact' ? 'active' : ''
                  }`}
                >
                  <span className="relative">
                    {translations.contact_us}
                    <span
                      className={`nav-link-underline ${
                        isHomePage && !isScrolled 
                          ? 'bg-white' 
                          : 'bg-black'
                      }`}
                    ></span>
                  </span>
                </Link>
              </div>
            </nav>
            
            {/* Actions Section */}
            <section 
              id="header-desktop-actions"
            >
              <div 
                id="header-desktop-action-buttons"
              >
                <div className="header-action-buttons">
                  <button
                    className={`
                      ${isHomePage && !isScrolled 
                        ? 'home-hero-button hover:home-hero-button' 
                        : 'scrolled-button hover:scrolled-button'
                      }`}
                    onClick={handleToggleSearch}
                  >
                    <Search size={20} className="w-5 h-5" />
                  </button>
                  <button
                    className={`relative
                      ${isHomePage && !isScrolled 
                        ? 'home-hero-button hover:home-hero-button' 
                        : 'scrolled-button hover:scrolled-button'
                      }`}
                    onClick={openCart}
                    aria-label="Open cart"
                  >
                    <ShoppingCart size={20} className="w-5 h-5" />
                    {mounted && getItemsCount() > 0 && (
                      <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full w-5 h-5 grid place-items-center">
                        {getItemsCount()}
                      </span>
                    )}
                  </button>
                </div>
                <div className="header-switchers">
                  <LanguageSwitcher
                    textClassName={`text-xs font-medium ${isHomePage && !isScrolled ? 'home-hero-text' : 'scrolled-text'}`}
                    dropdownClassName={`${isHomePage && !isScrolled ? 'bg-transparent backdrop-blur-sm border-white/20' : 'bg-white'} pr-0`}
                    iconColor={isHomePage && !isScrolled ? 'white' : 'currentColor'}
                    isHeroSection={isHomePage && !isScrolled}
                    useAbbreviation={true}
                  />
                  <CurrencySwitcher
                    textClassName={`text-xs font-medium ${isHomePage && !isScrolled ? 'home-hero-text' : 'scrolled-text'}`}
                    dropdownClassName={`${isHomePage && !isScrolled ? 'bg-transparent backdrop-blur-sm border-white/20' : 'bg-white'} pl-0 pr-0`}
                    iconColor={isHomePage && !isScrolled ? 'white' : 'currentColor'}
                    isHeroSection={isHomePage && !isScrolled}
                  />
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {/* Apply transition classes and conditional styles for open/close animation */}
      <div
        id="mobile-menu-container"
        className={`grid grid-cols-1 md:hidden transition-all duration-300 ease-in-out ${
          mobileMenuOpen ? 'max-h-screen opacity-100 overflow-y-auto pb-48' : 'max-h-0 opacity-0 overflow-hidden'
        }`}
        style={{
          backgroundColor: isHomePage && !isScrolled ? 'transparent' : '#f8f8f8'
        }}
      >
        <button
          onClick={() => handleMobileNavigation('/')}
          disabled={isNavigating}
          className={`block text-center transition-all duration-150 ease-in-out py-4 border-b px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
            isHomePage && !isScrolled
              ? 'text-white border-white/20'
              : 'text-gray-700 border-gray-200'
          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="relative inline-block">
            {translations.home}
            <span
              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
              } ${
                pathname === '/' ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
              }`}
            ></span>
          </span>
        </button>
        <button
          onClick={() => handleMobileNavigation('/about')}
          disabled={isNavigating}
          className={`block text-center transition-all duration-150 ease-in-out py-4 border-b px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
            isHomePage && !isScrolled
              ? 'text-white border-white/20'
              : 'text-gray-700 border-gray-200'
          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="relative inline-block">
            {translations.about_us}
            <span
              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
              } ${
                pathname === '/about' ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
              }`}
            ></span>
          </span>
        </button>
        <button
          onClick={() => handleMobileNavigation('/shop')}
          disabled={isNavigating}
          className={`block text-center transition-all duration-150 ease-in-out py-4 border-b px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
            isHomePage && !isScrolled
              ? 'text-white border-white/20'
              : 'text-gray-700 border-gray-200'
          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="relative inline-block">
            {translations.shop}
            <span
              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
              } ${
                pathname === '/shop' && !searchParams.get('category') ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
              }`}
            ></span>
          </span>
        </button>
        
        {/* Dynamic Categories in Mobile Menu */}
        {categories.map(category => {
          const categoryPath = `/shop?category=${encodeURIComponent(category.name)}`;
          const isCategoryActive = pathname === '/shop' && searchParams?.get('category') === category.name;
          const hasSubcategories = category.subcategories && category.subcategories.length > 0;
          const isExpanded = expandedCategories[category._id];

          return (
            <div
              key={category._id}
              id={`mobile-menu-category-${category._id}`}
              className={`text-center border-b last:border-b-0 ${
                isHomePage && !isScrolled ? 'border-white/20' : 'border-gray-200'
              }`}
            >
              <div className="relative">
                <button
                  onClick={() => handleMobileNavigation(categoryPath)}
                  disabled={isNavigating}
                  className={`block cursor-pointer transition-all duration-150 ease-in-out py-4 px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
                    isHomePage && !isScrolled ? 'text-white' : 'text-gray-700'
                  } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span className="relative inline-block">
                    {translateCategoryName(category.name)}
                    <span
                      className={`absolute bottom-[-4px] left-0 w-full h-[1px] rounded-full transform transition-transform duration-300 ease-out ${
                        isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
                      } ${
                        isCategoryActive ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
                      }`}
                    ></span>
                  </span>
                </button>
                
                {hasSubcategories && (
                  <button
                    onClick={() => toggleMobileCategory(category._id)}
                    className={`absolute right-0 top-0 bottom-0 px-4 flex items-center justify-center transition-all duration-150 ease-in-out ${
                      isHomePage && !isScrolled ? 'text-white' : 'text-gray-700'
                    }`}
                  >
                    <ChevronDown
                      size={16}
                      className={`transform transition-transform duration-200 ${
                        isExpanded ? 'rotate-180' : 'rotate-0'
                      }`}
                    />
                  </button>
                )}
              </div>
              
              {/* Subcategories List */}
              {hasSubcategories && (
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isExpanded ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className={`py-2 ${
                    isHomePage && !isScrolled ? 'border-white/10' : 'border-gray-100'
                  } border-t`}>
                    {category.subcategories.map(subcategory => {
                      const subPath = `/shop?category=${encodeURIComponent(category.name)}&subcategory=${encodeURIComponent(subcategory.name)}`;
                      const isSubActive = pathname === '/shop' &&
                        searchParams?.get('category') === category.name &&
                        searchParams?.get('subcategory') === subcategory.name;

                      return (
                        <button
                          key={subcategory._id}
                          onClick={() => handleMobileNavigation(subPath)}
                          disabled={isNavigating}
                          className={`block text-center transition-all duration-150 ease-in-out py-2 px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
                            isHomePage && !isScrolled ? 'text-white/80' : 'text-gray-600'
                          } ${
                            isSubActive ? (isHomePage && !isScrolled ? 'text-white' : 'text-black') : ''
                          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span className="relative inline-block">
                            {translateSubcategoryName(category.name, subcategory.name)}
                            <span
                              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
                              } ${
                                isSubActive ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
                              }`}
                            ></span>
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          );
        })}
        
        <button
          onClick={() => handleMobileNavigation('/blog')}
          disabled={isNavigating}
          className={`block text-center transition-all duration-150 ease-in-out py-4 border-b px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
            isHomePage && !isScrolled
              ? 'text-white border-white/20'
              : 'text-gray-700 border-gray-200'
          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="relative inline-block">
            {translations.blog}
            <span
              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
              } ${
                pathname === '/blog' ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
              }`}
            ></span>
          </span>
        </button>
        <button
          onClick={() => handleMobileNavigation('/contact')}
          disabled={isNavigating}
          className={`block text-center transition-all duration-150 ease-in-out py-4 px-4 sm:px-6 group active:scale-95 active:text-black w-full ${
            isHomePage && !isScrolled
              ? 'text-white border-white/20'
              : 'text-gray-700 border-gray-200'
          } ${isNavigating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="relative inline-block">
            {translations.contact_us}
            <span
              className={`absolute bottom-[-4px] left-0 w-full h-[1px] mx-auto rounded-full transform transition-transform duration-300 ease-out ${
                isHomePage && !isScrolled ? 'bg-white' : 'bg-black'
              } ${
                pathname === '/contact' ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100 group-active:scale-x-100'
              }`}
            ></span>
          </span>
        </button>

        {/* Currency and Language Switchers Container - using exact footer styling */}
        <div id="mobile-menu-switchers-container" className="footer-switchers-container">
          {/* Currency Switcher */}
          <div id="mobile-menu-currency-switcher" className="footer-currency-switcher">
            <CurrencySwitcher 
              textClassName={isHomePage && !isScrolled ? "text-white" : "switcher-text-style"}
              dropdownClassName={isHomePage && !isScrolled ? 'bg-transparent backdrop-blur-sm border-white/20' : 'bg-white'}
              iconColor={isHomePage && !isScrolled ? 'white' : 'currentColor'}
              isHeroSection={isHomePage && !isScrolled}
            />
          </div>

          {/* Language Switcher */}
          <div id="mobile-menu-language-switcher" className="footer-language-switcher">
            <LanguageSwitcher 
              textClassName={isHomePage && !isScrolled ? "text-white" : "switcher-text-style"}
              dropdownClassName={isHomePage && !isScrolled ? 'bg-transparent backdrop-blur-sm border-white/20' : 'bg-white'}
              iconColor={isHomePage && !isScrolled ? 'white' : 'currentColor'}
              isHeroSection={isHomePage && !isScrolled}
            />
          </div>
        </div>
      </div>
    </header>
  );
}

// Main Header component with Suspense boundary
export default function Header() {
  return (
    <Suspense fallback={
      <header className="sticky top-0 left-0 w-full z-40" style={{ backgroundColor: '#f8f8f8' }}>
        <div className="w-full px-4 sm:px-6 md:px-8 lg:px-10 py-3 sm:py-4 md:py-5">
          <div className="max-w-[540px] sm:max-w-[720px] md:max-w-[960px] lg:max-w-[1140px] xl:max-w-[1320px] mx-auto">
            <div className="flex items-center justify-between">
              <Link href="/">
                <Image
                  src="/images/main-logo.png"
                  alt="Afghan International Gems Logo"
                  width={100}
                  height={40}
                  className="w-[90px] lg:w-[100px] h-auto"
                  priority
                />
              </Link>
              <div className="text-gray-500">Loading...</div>
            </div>
          </div>
        </div>
      </header>
    }>
      <HeaderContent />
    </Suspense>
  );
}