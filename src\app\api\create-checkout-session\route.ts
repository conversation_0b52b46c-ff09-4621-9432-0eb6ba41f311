import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-08-16' as any, // Force the type to avoid TypeScript errors
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { items } = body;
    
    console.log('Received checkout request with items:', JSON.stringify(items));
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Items are required.' },
        { status: 400 }
      );
    }
    
    // Format line items for Stripe
    const lineItems = items.map(item => ({
      price_data: {
        currency: 'usd',
        product_data: {
          name: item.product.name,
          description: item.product.description.substring(0, 255), // Ensure description isn't too long
          images: item.product.imageUrl ? 
            [`${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${item.product.imageUrl}`] : 
            undefined,
        },
        unit_amount: Math.round(item.product.price * 100), // Convert to cents
      },
      quantity: item.quantity,
    }));
    
    console.log('Creating Stripe session with line items:', JSON.stringify(lineItems));
    
    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout?canceled=true`,
      billing_address_collection: 'required',
      shipping_address_collection: {
        allowed_countries: ['US', 'CA', 'GB', 'AF'], // Including Afghanistan
      },
    });
    
    console.log('Session created successfully:', session.id);
    return NextResponse.json({ sessionId: session.id, url: session.url });
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    
    // Provide more detailed error information for debugging
    const errorMessage = error.message || 'An error occurred creating the checkout session.';
    const errorDetails = error.stack || '';
    
    console.error('Error details:', errorMessage, errorDetails);
    
    return NextResponse.json(
      { 
        error: 'An error occurred creating the checkout session.', 
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
} 