'use client';

import { useState, useEffect } from 'react';

/**
 * Props for the DeveloperToggle component
 */
type DeveloperToggleProps = {
  onToggle: (isEnabled: boolean) => void;
  isEnabled: boolean;
};

/**
 * A toggle button for showing/hiding developer features
 */
export default function DeveloperToggle({ onToggle, isEnabled: externalIsEnabled }: DeveloperToggleProps) {
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  
  // Sync with external state
  useEffect(() => {
    setIsEnabled(externalIsEnabled);
  }, [externalIsEnabled]);
  
  // Load preference from localStorage on component mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('developerModeEnabled');
    const initialState = savedPreference === 'true';
    setIsEnabled(initialState);
    
    // Call the onToggle callback with the initial state
    onToggle(initialState);
  }, [onToggle]);
  
  // Handle toggle change
  const handleToggle = () => {
    const newState = !isEnabled;
    setIsEnabled(newState);
    
    // Save preference to localStorage
    localStorage.setItem('developerModeEnabled', String(newState));
    
    // Call the onToggle callback
    onToggle(newState);

    // If we're enabling developer mode, pre-fetch API endpoints
    // This warms up the connection status endpoints
    if (newState) {
      // Pre-fetch connection status endpoints in the background
      Promise.all([
        fetch('/api/check-connection').catch(() => {}),
        fetch('/api/check-s3-connections').catch(() => {})
      ]).catch(err => {
        console.error('Failed to pre-fetch connection status:', err);
      });
    }
  };
  
  return (
    <div className="flex items-center justify-between w-full bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
      <div>
        <h3 className="font-medium text-gray-800">Developer Mode</h3>
        <p className="text-sm text-gray-500">
          Show database tools and access the connection status page
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Keyboard shortcut: <kbd className="px-1 py-0.5 bg-gray-200 rounded text-gray-700 font-mono">Ctrl+Shift+D</kbd>
        </p>
      </div>
      
      <button
        onClick={handleToggle}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
          isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
        }`}
        role="switch"
        aria-checked={isEnabled}
      >
        <span className="sr-only">Enable developer mode</span>
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            isEnabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );
} 