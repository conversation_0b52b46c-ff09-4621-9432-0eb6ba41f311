/**
 * Notification Scheduler Script
 * This script processes scheduled notifications and sends them at the specified time.
 * It should be run as a cron job or scheduled task.
 */

require('dotenv').config();
const { connectToDatabase } = require('../src/lib/mongodb');
const admin = require('firebase-admin');
const { MongoClient, ObjectId } = require('mongodb');

// Initialize Firebase Admin SDK (only initialize once)
if (!admin.apps.length) {
  try {
    // Make sure to trim any whitespace from credentials
    const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY
      ? process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n').trim()
      : undefined;

    const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID?.trim();
    const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL?.trim();

    console.log('Firebase Admin SDK initializing with project ID:', projectId);
    
    if (!projectId || !clientEmail || !privateKey) {
      throw new Error(
        `Missing Firebase Admin credentials: 
        ProjectID: ${projectId ? 'OK' : 'MISSING'}, 
        ClientEmail: ${clientEmail ? 'OK' : 'MISSING'}, 
        PrivateKey: ${privateKey ? 'OK' : 'MISSING'}`
      );
    }

    admin.initializeApp({
      credential: admin.credential.cert({
        projectId,
        clientEmail,
        privateKey,
      }),
    });
    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error);
  }
}

async function processScheduledNotifications() {
  let client;
  
  try {
    // Connect to the database
    client = await MongoClient.connect(process.env.MONGODB_URI);
    const db = client.db(process.env.MONGODB_DB);
    
    console.log('Connected to MongoDB');
    
    // Get current time
    const now = new Date();
    
    // Find notifications that are scheduled to be sent now or in the past
    const scheduledNotifications = await db.collection('notifications').find({
      status: 'scheduled',
      scheduledAt: { $lte: now }
    }).toArray();
    
    console.log(`Found ${scheduledNotifications.length} scheduled notifications to process`);
    
    // Process each notification
    for (const notification of scheduledNotifications) {
      console.log(`Processing notification: ${notification._id}`);
      
      try {
        // Update notification status to sending
        await db.collection('notifications').updateOne(
          { _id: notification._id },
          { $set: { status: 'sending' } }
        );
        
        // Send the notification
        const results = await sendPushNotification(notification);
        
        // Update notification stats
        await db.collection('notifications').updateOne(
          { _id: notification._id },
          { 
            $set: {
              status: results.successCount > 0 ? 'sent' : 'failed',
              sentAt: new Date(),
              'stats.sent': results.successCount,
              'stats.failed': results.failureCount,
            } 
          }
        );
        
        // Update individual recipient statuses
        if (results.responses && results.responses.length > 0) {
          for (let i = 0; i < results.responses.length; i++) {
            const response = results.responses[i];
            const updatePath = `recipients.${i}`;
            
            await db.collection('notifications').updateOne(
              { _id: notification._id },
              { 
                $set: {
                  [`${updatePath}.status`]: response.success ? 'sent' : 'failed',
                  [`${updatePath}.sentAt`]: new Date(),
                  ...(response.error && { [`${updatePath}.error`]: response.error.message }),
                } 
              }
            );
          }
        }
        
        console.log(`Successfully processed notification: ${notification._id}`);
      } catch (error) {
        console.error(`Error processing notification ${notification._id}:`, error);
        
        // Update notification status to failed
        await db.collection('notifications').updateOne(
          { _id: notification._id },
          { $set: { status: 'failed' } }
        );
      }
    }
  } catch (error) {
    console.error('Error processing scheduled notifications:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('Disconnected from MongoDB');
    }
  }
}

// Helper function to send push notification using Firebase Cloud Messaging
async function sendPushNotification(notification) {
  try {
    // Verify Firebase Admin is initialized
    if (!admin.apps.length) {
      throw new Error('Firebase Admin SDK is not initialized');
    }
    
    // Get FCM tokens from recipients
    const tokens = notification.recipients.map(recipient => recipient.fcmToken);
    
    // Skip if no tokens
    if (tokens.length === 0) {
      return {
        successCount: 0,
        failureCount: 0,
        responses: [],
      };
    }
    
    let successCount = 0;
    let failureCount = 0;
    let responses = [];
    
    // Handle messages in chunks to avoid limitations
    if (tokens.length > 1) {
      // Process in batches of up to 500 tokens (FCM limit)
      const chunkSize = 500;
      for (let i = 0; i < tokens.length; i += chunkSize) {
        const tokensBatch = tokens.slice(i, i + chunkSize);
        
        try {
          // Prepare multicast message
          const message = {
            notification: {
              title: notification.title,
              body: notification.body,
              ...(notification.image && { imageUrl: notification.image }),
            },
            data: {
              ...notification.data,
              notificationId: notification._id.toString(),
              type: notification.type,
              subType: notification.subType,
              click_action: notification.clickAction || '/',
            },
            tokens: tokensBatch,
          };
          
          // Send the message
          const batchResponse = await admin.messaging().sendEachForMulticast(message);
          
          successCount += batchResponse.successCount;
          failureCount += batchResponse.failureCount;
          responses = [...responses, ...batchResponse.responses];
        } catch (batchError) {
          console.error('Error sending batch:', batchError);
          failureCount += tokensBatch.length;
          
          // Add failure responses for this batch
          responses = [
            ...responses, 
            ...tokensBatch.map(() => ({ 
              success: false, 
              error: { message: String(batchError) } 
            }))
          ];
        }
      }
      
      return { successCount, failureCount, responses };
    } 
    // For single recipient
    else {
      try {
        const message = {
          notification: {
            title: notification.title,
            body: notification.body,
            ...(notification.image && { imageUrl: notification.image }),
          },
          data: {
            ...notification.data,
            notificationId: notification._id.toString(), 
            type: notification.type,
            subType: notification.subType,
            click_action: notification.clickAction || '/',
          },
          token: tokens[0],
        };
        
        // Send the message to a single device
        const messageId = await admin.messaging().send(message);
        
        return {
          successCount: 1,
          failureCount: 0,
          responses: [{ success: true, messageId }],
        };
      } catch (error) {
        console.error('Error sending to single device:', error);
        return {
          successCount: 0,
          failureCount: 1,
          responses: [{ 
            success: false, 
            error: { 
              code: error.code || 'unknown', 
              message: error.message || String(error) 
            } 
          }],
        };
      }
    }
  } catch (error) {
    console.error('Error in push notification function:', error);
    throw error;
  }
}

// Run the script
processScheduledNotifications()
  .then(() => {
    console.log('Notification scheduler completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Fatal error in notification scheduler:', error);
    process.exit(1);
  }); 