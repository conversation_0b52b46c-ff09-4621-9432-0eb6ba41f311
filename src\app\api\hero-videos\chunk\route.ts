import { NextResponse } from 'next/server';
import { MediaConvertClient, Create<PERSON>obCommand, GetJobCommand } from '@aws-sdk/client-mediaconvert';
import { S3Client, ListObjectsV2Command, DeleteObjectsCommand } from '@aws-sdk/client-s3';
import { connectToDatabase } from '@/lib/mongodb';
import HeroVideo from '@/models/HeroVideo';
import { getCloudFrontChunkedVideoUrl } from '@/lib/cloudfront';

// MediaConvert configuration
const mediaConvertConfig = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
  endpoint: process.env.AWS_MEDIACONVERT_ENDPOINT || `https://mediaconvert.${process.env.AWS_REGION || 'eu-north-1'}.amazonaws.com`
};

// S3 configuration
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get clients
const getMediaConvertClient = () => {
  return new MediaConvertClient(mediaConvertConfig);
};

const getS3Client = () => {
  return new S3Client(s3Config);
};

// Bucket configuration
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';
const chunkedVideosBucketName = process.env.S3_CHUNKED_VIDEOS_BUCKET || 'videosbucket2025';
const mediaConvertRoleArn = process.env.AWS_MEDIACONVERT_ROLE_ARN || '';

/**
 * Start video chunking process using MediaConvert
 * 
 * POST /api/hero-videos/chunk
 */
export async function POST(request: Request) {
  try {
    const { videoId } = await request.json();
    
    if (!videoId) {
      return NextResponse.json({
        success: false,
        error: 'Video ID is required'
      }, { status: 400 });
    }

    if (!mediaConvertRoleArn) {
      return NextResponse.json({
        success: false,
        error: 'MediaConvert service role not configured'
      }, { status: 500 });
    }

    await connectToDatabase();
    
    // Get video details
    const video = await HeroVideo.findById(videoId);
    if (!video) {
      return NextResponse.json({
        success: false,
        error: 'Video not found'
      }, { status: 404 });
    }

    // Check if already chunked
    if (video.isChunked && video.chunkingStatus === 'completed') {
      return NextResponse.json({
        success: false,
        error: 'Video is already chunked'
      }, { status: 400 });
    }

    // Extract S3 key from video URL
    const s3Key = extractS3KeyFromUrl(video.videoUrl);
    if (!s3Key) {
      return NextResponse.json({
        success: false,
        error: 'Cannot extract S3 key from video URL'
      }, { status: 400 });
    }

    // Create MediaConvert client
    const client = getMediaConvertClient();

    // Generate output path
    const outputPath = `chunked-videos/${video.type}-${Date.now()}`;
    const inputUri = `s3://${videosBucketName}/${s3Key}`;
    const outputUri = `s3://${chunkedVideosBucketName}/${outputPath}/`;
    
    // Get base filename for better naming
    const originalName = video.fileName.substring(0, video.fileName.lastIndexOf('.'));
    const baseFileName = `${video.type}-hero-${Date.now()}-${originalName.substring(0, 20).replace(/[^a-z0-9-]/gi, '-').toLowerCase()}`;

    // MediaConvert job settings for HLS with small segments for fast loading
    const jobSettings = {
      Role: mediaConvertRoleArn,
      Settings: {
        Inputs: [
          {
            FileInput: inputUri,
            VideoSelector: {},
            AudioSelectors: {
              "Audio Selector 1": {
                DefaultSelection: "DEFAULT" as any
              }
            }
          }
        ],
        OutputGroups: [
          {
            OutputGroupSettings: {
              Type: "HLS_GROUP_SETTINGS",
              HlsGroupSettings: {
                SegmentLength: 1, // 1 second (MediaConvert minimum - will create 1 part per second of video)
                MinSegmentLength: 0,
                Destination: outputUri,
                DirectoryStructure: "SINGLE_DIRECTORY",
                ManifestDurationFormat: "INTEGER",
                SegmentControl: "SEGMENTED_FILES",
                OutputSelection: "MANIFESTS_AND_SEGMENTS",
                ManifestCompression: "NONE",
                ClientCache: "ENABLED",
                ManifestName: baseFileName
              }
            },
            Outputs: [
              {
                NameModifier: "_hls",
                ContainerSettings: {
                  Container: "M3U8",
                  M3u8Settings: {
                    AudioFramesPerPes: 4,
                    PcrControl: "PCR_EVERY_PES_PACKET",
                    PmtPid: 480,
                    PrivateMetadataPid: 503,
                    ProgramNumber: 1,
                    PatInterval: 0,
                    PmtInterval: 0,
                    VideoPid: 481,
                    AudioPids: [482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492]
                  }
                },
                OutputSettings: {
                  HlsSettings: {
                    SegmentModifier: "_seg"
                  }
                },
                // 🎬 HIGH-QUALITY VIDEO SETTINGS - Maintains Original Quality
                // Updated settings to preserve source video quality during chunking
                VideoDescription: {
                  CodecSettings: {
                    Codec: "H_264",
                    H264Settings: {
                      // 🏆 MAXIMUM QUALITY SETTINGS - Preserves Original Quality
                      RateControlMode: "QVBR", // Quality-based Variable Bitrate
                      MaxBitrate: video.type === 'mobile' ? 25000000 : 50000000, // 🚀 VERY HIGH: 25Mbps mobile, 50Mbps desktop
                      QvbrSettings: {
                        QvbrQualityLevel: 10 // 🥇 MAXIMUM QUALITY LEVEL (10/10)
                      },
                      FramerateControl: "INITIALIZE_FROM_SOURCE", // Preserve original framerate
                      GopClosedCadence: 1,
                      GopSize: 30, // ✅ RESTORED original GOP value 
                      GopSizeUnits: "FRAMES",
                      SlowPal: "DISABLED",
                      AdaptiveQuantization: "HIGH", // Maximum adaptive quantization
                      EntropyEncoding: "CABAC", // Best compression efficiency
                      // 🎯 ADDITIONAL QUALITY PRESERVATION SETTINGS
                      MinIInterval: 0, // Allow maximum flexibility
                      NumberBFramesBetweenReferenceFrames: 3, // Better motion prediction
                      InterlaceMode: "PROGRESSIVE", // Force progressive scan
                      ParControl: "INITIALIZE_FROM_SOURCE" // Preserve pixel aspect ratio
                    }
                  },
                  // 🎯 PRESERVE ORIGINAL DIMENSIONS & QUALITY
                  RespondToAfd: "NONE",
                  ScalingBehavior: "DEFAULT",
                  AntiAlias: "ENABLED", // Better quality scaling if needed
                  FilterEnable: "AUTO", // Smart filtering
                  FilterStrength: 0 // Minimal filtering to preserve detail
                },
                AudioDescriptions: [
                  {
                    AudioSourceName: "Audio Selector 1",
                    CodecSettings: {
                      Codec: "AAC",
                      AacSettings: {
                        Bitrate: 64000, // 🔇 Minimal audio quality since not used
                        CodingMode: "CODING_MODE_2_0",
                        SampleRate: 44100, // Standard rate
                        Specification: "MPEG4"
                      }
                    },
                    AudioTypeControl: "FOLLOW_INPUT",
                    LanguageCodeControl: "FOLLOW_INPUT"
                  }
                ],
              }
            ]
          }
        ]
      }
    };

    // Create MediaConvert job
    const createJobCommand = new CreateJobCommand(jobSettings as any);
    const jobResponse = await client.send(createJobCommand);

    // Update video status in database
    video.chunkingStatus = 'processing';
    video.chunkingProgress = 0;
    video.mediaConvertJobId = jobResponse.Job?.Id;
    video.chunkingError = undefined;
    await video.save();

    return NextResponse.json({
      success: true,
      message: 'Video chunking started successfully',
      jobId: jobResponse.Job?.Id,
      video: {
        _id: video._id,
        chunkingStatus: video.chunkingStatus,
        chunkingProgress: video.chunkingProgress
      }
    });

  } catch (error: any) {
    console.error('Error starting video chunking:', error);
    
    // Enhanced error reporting with specific AWS error details
    let errorMessage = 'Failed to start video chunking';
    let errorDetails = '';
    
    if (error.name === 'AccessDenied') {
      errorMessage = 'AWS Access Denied';
      errorDetails = 'Check IAM permissions for MediaConvert and S3 access';
    } else if (error.name === 'InvalidRole') {
      errorMessage = 'Invalid MediaConvert Role';
      errorDetails = 'Verify AWS_MEDIACONVERT_ROLE_ARN is correct';
    } else if (error.name === 'NoSuchBucket') {
      errorMessage = 'S3 Bucket Not Found';
      errorDetails = 'Check if S3_VIDEOS_BUCKET exists and is accessible';
    } else if (error.name === 'CredentialsError' || error.message?.includes('credentials')) {
      errorMessage = 'AWS Credentials Error';
      errorDetails = 'Check AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY';
    } else if (error.message?.includes('MediaConvert')) {
      errorMessage = 'MediaConvert Service Error';
      errorDetails = error.message;
    } else if (error.message?.includes('Role')) {
      errorMessage = 'Service Role Configuration Error';
      errorDetails = 'MediaConvert role ARN missing or invalid';
    } else if (error.message?.includes('bucket')) {
      errorMessage = 'S3 Bucket Access Error';
      errorDetails = error.message;
    } else {
      errorMessage = error.message || 'Unknown error occurred';
      errorDetails = `Error type: ${error.name || 'Unknown'} | Code: ${error.code || 'N/A'}`;
    }

    // Save error to database for debugging
    try {
      const { videoId } = await request.json();
      if (videoId) {
        await connectToDatabase();
        const video = await HeroVideo.findById(videoId);
        if (video) {
          video.chunkingStatus = 'failed';
          video.chunkingError = `${errorMessage}: ${errorDetails}`;
          await video.save();
        }
      }
    } catch (dbError) {
      console.error('Error updating video with error details:', dbError);
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      errorDetails: errorDetails,
      errorCode: error.code || error.name || 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}

/**
 * Get chunking status for a video
 * 
 * GET /api/hero-videos/chunk?videoId=xxx
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('videoId');

    if (!videoId) {
      return NextResponse.json({
        success: false,
        error: 'Video ID is required'
      }, { status: 400 });
    }

    await connectToDatabase();
    
    const video = await HeroVideo.findById(videoId);
    if (!video) {
      return NextResponse.json({
        success: false,
        error: 'Video not found'
      }, { status: 404 });
    }

    // If video is chunked but status is completed, check for URL sync issues
    if (video.isChunked && video.chunkingStatus === 'completed' && video.chunkedVideoUrl) {
      // Check if URL exists in S3 by syncing
      const syncResult = await syncChunkedVideoUrl(videoId);
      if (syncResult && syncResult.fixed) {
        console.log(`Automatically fixed URL mismatch for video ${videoId}`);
      }
    }
    // If processing, check MediaConvert job status
    if (video.chunkingStatus === 'processing' && video.mediaConvertJobId) {
      try {
        const client = getMediaConvertClient();
        const getJobCommand = new GetJobCommand({ Id: video.mediaConvertJobId });
        const jobResponse = await client.send(getJobCommand);
        
        const jobStatus = jobResponse.Job?.Status;
        const jobProgress = jobResponse.Job?.JobPercentComplete || 0;

        // Update video based on job status
        if (jobStatus === 'COMPLETE') {
          video.chunkingStatus = 'completed';
          video.chunkingProgress = 100;
          video.isChunked = true;
          
          // NEW URL format that matches MediaConvert output:
          // Check if we can get the output details from the job
          const outputDetails = jobResponse.Job?.Settings?.OutputGroups?.[0]?.Outputs?.[0];
          const outputPath = jobResponse.Job?.Settings?.OutputGroups?.[0]?.OutputGroupSettings?.HlsGroupSettings?.Destination || '';
          
          if (outputPath) {
            // Extract the actual output path from S3 URI (s3://bucket/path/ -> path/)
            const s3UriParts = outputPath.match(/s3:\/\/[^/]+\/(.+)/);
            const basePath = s3UriParts ? s3UriParts[1] : '';
            
            // Base filename will be the original filename
            const baseFileName = video.fileName.substring(0, video.fileName.lastIndexOf('.')) || `${video.type}-hero-${Date.now()}`;
            const nameModifier = outputDetails?.NameModifier || '_hls';
            
            // Construct the chunked video URL matching MediaConvert's output pattern
            const outputFileName = `${baseFileName}${nameModifier}.m3u8`;
            const s3Key = `${basePath}${outputFileName}`;
            
            // Use direct CloudFront URL for faster loading (bypassing proxy)
            const directCloudFrontUrl = getCloudFrontChunkedVideoUrl(s3Key);
            video.chunkedVideoUrl = directCloudFrontUrl || `/api/hls-proxy/${s3Key}`;
          } else {
            // Fallback to old pattern if we can't determine the output path
            const fallbackKey = `chunked-videos/${video.type}-${video.mediaConvertJobId}/index.m3u8`;
            const directCloudFrontUrl = getCloudFrontChunkedVideoUrl(fallbackKey);
            video.chunkedVideoUrl = directCloudFrontUrl || `/api/hls-proxy/${fallbackKey}`;
          }
          
          await video.save();
        } else if (jobStatus === 'ERROR') {
          video.chunkingStatus = 'failed';
          const jobErrorDetail = jobResponse.Job?.ErrorMessage || 'MediaConvert job failed without specific error';
          video.chunkingError = `MediaConvert Job Failed: ${jobErrorDetail}`;
          await video.save();
        } else {
          video.chunkingProgress = jobProgress;
          await video.save();
        }
      } catch (jobError) {
        console.error('Error checking MediaConvert job:', jobError);
      }
    }

    return NextResponse.json({
      success: true,
      chunkingStatus: video.chunkingStatus,
      chunkingProgress: video.chunkingProgress,
      isChunked: video.isChunked,
      chunkedVideoUrl: video.chunkedVideoUrl,
      chunkingError: video.chunkingError
    });

  } catch (error: any) {
    console.error('Error getting chunking status:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to get chunking status'
    }, { status: 500 });
  }
}

/**
 * Count chunked video segments for a video
 * 
 * PATCH /api/hero-videos/chunk?videoId=xxx
 */
export async function PATCH(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('videoId');

    if (!videoId) {
      return NextResponse.json({
        success: false,
        error: 'Video ID is required'
      }, { status: 400 });
    }

    await connectToDatabase();
    
    const video = await HeroVideo.findById(videoId);
    if (!video) {
      return NextResponse.json({
        success: false,
        error: 'Video not found'
      }, { status: 404 });
    }

    if (!video.isChunked || !video.chunkedVideoUrl) {
      return NextResponse.json({
        success: false,
        error: 'Video is not chunked',
        segmentCount: 0
      }, { status: 400 });
    }

    // First check if we need to sync the URL to match actual S3 files
    const syncResult = await syncChunkedVideoUrl(videoId);
    if (syncResult && syncResult.fixed) {
      console.log(`Automatically fixed URL mismatch for video ${videoId} during segment count`);
      // Reload video with updated URL
      const updatedVideo = await HeroVideo.findById(videoId);
      if (updatedVideo) {
        video.chunkedVideoUrl = updatedVideo.chunkedVideoUrl;
      }
    }

    try {
      const s3Client = getS3Client();
      
      // Extract the chunked video folder path from the URL
      // Possible formats:
      // 1. videosbucket2025/chunked-videos/mobile-timestamp/index.m3u8
      // 2. videosbucket2025/chunked-videos/mobile-hero-timestamp-name_hls.m3u8
      let folderPath = '';
      const url = video.chunkedVideoUrl;
      
      if (url.includes('/api/hls-proxy/')) {
        const urlPath = url.replace('/api/hls-proxy/', '');
        folderPath = urlPath.substring(0, urlPath.lastIndexOf('/'));
      } else if (url.includes('_hls.m3u8')) {
        // Format 2: Handle direct file reference without folder
        folderPath = url.substring(0, url.lastIndexOf('/'));
        if (!folderPath) {
          // If no folder structure, use the whole path minus filename
          folderPath = url.substring(0, url.lastIndexOf('_hls.m3u8'));
        }
      } else {
        // Format 1 or other: Try to find the last directory
        folderPath = url.substring(0, url.lastIndexOf('/'));
      }

      // List all objects in the chunked video folder or with matching prefix
      const listCommand = new ListObjectsV2Command({
        Bucket: chunkedVideosBucketName,
        Prefix: folderPath,
      });

      const response = await s3Client.send(listCommand);
      
      // Count .ts segment files (exclude .m3u8 manifest files)
      const segmentFiles = response.Contents?.filter(obj => 
        obj.Key && obj.Key.endsWith('.ts')
      ) || [];

      return NextResponse.json({
        success: true,
        segmentCount: segmentFiles.length,
        totalFiles: response.Contents?.length || 0,
        folderPath,
        segments: segmentFiles.map(file => file.Key).slice(0, 5) // Show first 5 for debugging
      });

    } catch (s3Error: any) {
      console.error('Error counting S3 segments:', s3Error);
      return NextResponse.json({
        success: false,
        error: 'Failed to count video segments',
        errorDetails: s3Error.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error counting chunked segments:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to count chunked segments'
    }, { status: 500 });
  }
}

/**
 * Delete chunked video parts (revert to original video)
 * 
 * DELETE /api/hero-videos/chunk?videoId=xxx
 */
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('videoId');

    if (!videoId) {
      return NextResponse.json({
        success: false,
        error: 'Video ID is required'
      }, { status: 400 });
    }

    await connectToDatabase();
    
    const video = await HeroVideo.findById(videoId);
    if (!video) {
      return NextResponse.json({
        success: false,
        error: 'Video not found'
      }, { status: 404 });
    }

    if (!video.isChunked || !video.chunkedVideoUrl) {
      return NextResponse.json({
        success: false,
        error: 'Video is not chunked - nothing to delete'
      }, { status: 400 });
    }

    try {
      const s3Client = getS3Client();
      
      // Extract the chunked video folder path from the URL
      // Possible formats:
      // 1. videosbucket2025/chunked-videos/mobile-timestamp/index.m3u8
      // 2. videosbucket2025/chunked-videos/mobile-hero-timestamp-name_hls.m3u8
      let folderPath = '';
      const url = video.chunkedVideoUrl;
      
      if (url.includes('/api/hls-proxy/')) {
        const urlPath = url.replace('/api/hls-proxy/', '');
        folderPath = urlPath.substring(0, urlPath.lastIndexOf('/'));
      } else if (url.includes('_hls.m3u8')) {
        // Format 2: Handle direct file reference without folder
        folderPath = url.substring(0, url.lastIndexOf('/'));
        if (!folderPath) {
          // If no folder structure, use the whole path minus filename
          folderPath = url.substring(0, url.lastIndexOf('_hls.m3u8'));
        }
      } else {
        // Format 1 or other: Try to find the last directory
        folderPath = url.substring(0, url.lastIndexOf('/'));
      }

      // List all objects in the chunked video folder or with matching prefix
      const listCommand = new ListObjectsV2Command({
        Bucket: chunkedVideosBucketName,
        Prefix: folderPath,
      });

      const response = await s3Client.send(listCommand);
      
      if (response.Contents && response.Contents.length > 0) {
        // Delete all files in the chunked video folder
        const objectsToDelete = response.Contents.map(obj => ({ Key: obj.Key! }));
        
        const deleteCommand = new DeleteObjectsCommand({
          Bucket: chunkedVideosBucketName,
          Delete: {
            Objects: objectsToDelete,
            Quiet: false
          }
        });

        const deleteResponse = await s3Client.send(deleteCommand);
        
        console.log(`Deleted ${deleteResponse.Deleted?.length || 0} chunked video files for ${video.type} video`);
      }

      // Reset video chunking fields in database (revert to original video)
      video.isChunked = false;
      video.chunkingStatus = undefined;
      video.chunkingProgress = undefined;
      video.chunkedVideoUrl = undefined;
      video.mediaConvertJobId = undefined;
      video.chunkingError = undefined;
      
      await video.save();

      return NextResponse.json({
        success: true,
        message: 'Chunked video parts deleted successfully',
        deletedFiles: response.Contents?.length || 0,
        videoReverted: true
      });

    } catch (s3Error: any) {
      console.error('Error deleting S3 chunked files:', s3Error);
      return NextResponse.json({
        success: false,
        error: 'Failed to delete chunked video files',
        errorDetails: s3Error.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error deleting chunked video parts:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete chunked video parts'
    }, { status: 500 });
  }
}

/**
 * Synchronize S3 files with database URLs
 * This function checks if the chunked video URL in the database matches actual files in S3
 * If it doesn't, it will find the matching file in S3 and update the database
 */
async function syncChunkedVideoUrl(videoId: string) {
  try {
    await connectToDatabase();
    
    // Get video details
    const video = await HeroVideo.findById(videoId);
    if (!video || !video.isChunked) return null;
    
    const s3Client = getS3Client();
    
    // Get the potential folder paths from the video type and timestamp
    // Extract timestamp from current URL if possible
    let timestamp = "";
    const currentUrl = video.chunkedVideoUrl || "";
    
    // Try to extract timestamp from URL using regex
    const timestampMatch = currentUrl.match(/mobile-(\d+)/);
    if (timestampMatch && timestampMatch[1]) {
      timestamp = timestampMatch[1];
    }
    
    // Form potential path prefixes
    const prefixOptions = [
      `chunked-videos/${video.type}-${timestamp}`,
      `chunked-videos/${video.type}-hero-${timestamp}`,
      `chunked-videos`
    ];
    
    // Try to find files in S3 that match our video
    let matchingFile = null;
    let matchingPrefix = null;
    
    // Try each prefix option
    for (const prefix of prefixOptions) {
      const listCommand = new ListObjectsV2Command({
        Bucket: chunkedVideosBucketName,
        Prefix: prefix,
        MaxKeys: 100
      });
      
      try {
        const response = await s3Client.send(listCommand);
        if (response.Contents && response.Contents.length > 0) {
          // Look for m3u8 files that contain our video type
          const m3u8Files = response.Contents.filter(obj => 
            obj.Key && obj.Key.endsWith('.m3u8') && obj.Key.includes(video.type)
          );
          
          if (m3u8Files.length > 0) {
            // We found potential matching files
            // Prioritize _hls.m3u8 files as they're the main manifest
            const hlsManifests = m3u8Files.filter(obj => obj.Key && obj.Key.includes('_hls.m3u8'));
            
            if (hlsManifests.length > 0) {
              matchingFile = hlsManifests[0].Key;
              break;
            } else if (m3u8Files.length > 0) {
              matchingFile = m3u8Files[0].Key;
              break;
            }
          }
        }
      } catch (err) {
        console.error(`Error listing objects with prefix ${prefix}:`, err);
        continue;
      }
    }
    
    // If we found a matching file and it's different from the current URL
    if (matchingFile && !currentUrl.includes(matchingFile)) {
      console.log(`Fixing URL mismatch for video ${videoId}. 
        Current: ${currentUrl}
        New: ${chunkedVideosBucketName}/${matchingFile}`);
      
      // Update the database with the correct URL using direct CloudFront (with proxy fallback)
      const directCloudFrontUrl = getCloudFrontChunkedVideoUrl(matchingFile);
      video.chunkedVideoUrl = directCloudFrontUrl || `/api/hls-proxy/${matchingFile}`;
      await video.save();
      
      return {
        fixed: true,
        oldUrl: currentUrl,
        newUrl: video.chunkedVideoUrl
      };
    }
    
    return { fixed: false };
  } catch (error) {
    console.error('Error syncing chunked video URL:', error);
    return { fixed: false, error: error.message };
  }
}

// Helper function to extract S3 key from URL
function extractS3KeyFromUrl(url: string): string | null {
  const cloudfrontDomain = process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN || '';
  if (cloudfrontDomain && url.includes(cloudfrontDomain)) {
    const urlObj = new URL(url);
    return urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
  }
  
  if (url.includes('/api/test-s3-video/')) {
    return url.split('/api/test-s3-video/')[1];
  }
  
  return null;
} 