import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Category } from '@/models/Category';
import mongoose from 'mongoose';

// POST - Add a subcategory to a category
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { name } = await request.json();
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }
    
    if (!name || typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json(
        { error: 'Subcategory name is required' },
        { status: 400 }
      );
    }
    
    await connectToDatabase();
    
    // Check if category exists
    const category = await Category.findById(id);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }
    
    // Check if subcategory with same name already exists
    const subcategoryExists = category.subcategories.some(
      (sub: any) => sub.name.toLowerCase() === name.toLowerCase()
    );
    
    if (subcategoryExists) {
      return NextResponse.json(
        { error: 'A subcategory with this name already exists in this category' },
        { status: 400 }
      );
    }
    
    // Create a new subcategory
    const subcategoryId = new mongoose.Types.ObjectId();
    
    // Add the subcategory to the category
    const updatedCategory = await Category.findByIdAndUpdate(
      id,
      { 
        $push: { 
          subcategories: { 
            _id: subcategoryId,
            name 
          } 
        } 
      },
      { new: true, runValidators: true }
    );
    
    return NextResponse.json(updatedCategory, { status: 201 });
  } catch (error) {
    console.error('Error adding subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to add subcategory' },
      { status: 500 }
    );
  }
} 