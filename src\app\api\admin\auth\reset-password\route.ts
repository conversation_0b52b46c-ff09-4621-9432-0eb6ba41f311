import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/models/User';

export async function POST(req: Request) {
  try {
    await dbConnect();
    
    const { token, newPassword } = await req.json();
    
    // Debug: Log received token and request body
    console.log('Reset password request received');
    console.log('Token received:', token.substring(0, 10) + '...');
    console.log('Current time:', new Date(Date.now()).toISOString());
    
    if (!token || !newPassword) {
      console.log('Missing required fields');
      return NextResponse.json(
        { error: 'Token and new password are required' },
        { status: 400 }
      );
    }
    
    // First check if any user has this exact token, regardless of expiration
    const userWithToken = await User.findOne({ resetPasswordToken: token });
    console.log('User with exact token (ignoring expiration):', userWithToken ? userWithToken.email : 'None found');
    
    // Check for expired tokens
    const expiredUser = await User.findOne({ 
      resetPasswordToken: token,
      resetPasswordExpires: { $lte: Date.now() } 
    });
    if (expiredUser) {
      console.log('Found user with EXPIRED token:', expiredUser.email);
      console.log('Token expired at:', expiredUser.resetPasswordExpires);
      console.log('Current time:', new Date());
    }
    
    // List all active tokens in database
    const allTokenUsers = await User.find(
      { resetPasswordToken: { $exists: true, $ne: null } },
      { email: 1, resetPasswordToken: 1, resetPasswordExpires: 1 }
    );
    console.log('ALL ACTIVE TOKENS IN DATABASE:', 
      allTokenUsers.map(u => ({
        email: u.email,
        tokenPreview: u.resetPasswordToken ? u.resetPasswordToken.substring(0, 5) + '...' + u.resetPasswordToken.substring(u.resetPasswordToken.length - 5) : 'none',
        tokenLength: u.resetPasswordToken ? u.resetPasswordToken.length : 0,
        expires: u.resetPasswordExpires,
        isExpired: u.resetPasswordExpires < new Date()
      }))
    );
    
    // Find user with the token that hasn't expired
    console.log('Searching for user with valid non-expired token...');
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() } // Token must not be expired
    });
    
    // Debug: Check if user was found and token status
    if (!user) {
      console.log('No user found with valid non-expired token');
      
      let errorDetail = 'Unknown token issue';
      
      if (expiredUser) {
        const expireDate = new Date(expiredUser.resetPasswordExpires);
        const currentTime = new Date();
        const diffMs = currentTime.getTime() - expireDate.getTime();
        const diffMins = Math.floor(diffMs / 60000);
        
        console.log('User found but token expired. Expiry time:', expiredUser.resetPasswordExpires);
        console.log('Current time:', currentTime);
        console.log('Difference (ms):', diffMs);
        
        errorDetail = `Token expired ${diffMins} minutes ago. Tokens are valid for 60 minutes.`;
      } else if (userWithToken) {
        errorDetail = 'Token found but has a validity issue. Try requesting a new reset link.';
      } else {
        console.log('No user found with this token at all');
        // Include the token in the error message for debugging
        const tokenPreview = token.length > 10 ? `${token.substring(0, 5)}...${token.substring(token.length - 5)}` : token;
        errorDetail = `Token "${tokenPreview}" (${token.length} chars) not found in database. Check for typos or if it was already used.`;
      }
      
      return NextResponse.json(
        { 
          error: 'Password reset token is invalid or has expired',
          detail: errorDetail,
          token_preview: token.substring(0, 5) + '...' + token.substring(token.length - 5),
          token_length: token.length,
          debug: true
        },
        { status: 400 }
      );
    }
    
    console.log('User found:', user.email);
    console.log('Token expiry:', user.resetPasswordExpires);
    console.log('Setting new password and clearing token...');

    // Set the new password
    user.password = newPassword;

    // Clear the reset token fields
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;

    // Save the user with the new password
    await user.save();
    console.log('Password successfully reset for user:', user.email);

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully'
    });
  } catch (error) {
    console.error('Password reset error:', error);
    return NextResponse.json(
      { error: 'Password reset failed' },
      { status: 500 }
    );
  }
} 