'use client';

import Image from 'next/image';
import React, { useState, useRef } from 'react';
import Link from 'next/link';
import { ShoppingCart, Play } from 'lucide-react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCartStore } from '@/store/useCartStore';
import { toast } from 'sonner';
import './ProductCarde.css';

interface ProductCardProps {
  imageSrc: string;
  productName: string;
  regularPrice: number; // Changed to number from string
  salePrice: number; // Changed to number from string
  weight?: number; // Stone weight in carats
  alt?: string;
  onBuy?: () => void;
  className?: string;
  href?: string;
  category?: string; // hidden, for future use
  productId?: string; // Real product ID from database
  // PERFORMANCE OPTIMIZATION: Image loading props
  priority?: boolean;
  loading?: 'eager' | 'lazy';
  fetchPriority?: 'high' | 'low' | 'auto';
}

const ProductCard: React.FC<ProductCardProps> = ({
  imageSrc,
  productName,
  regularPrice,
  salePrice,
  weight,
  alt,
  onBuy,
  className = '',
  href,
  category = '', // Added proper destructuring for category prop
  productId, // Real product ID from database
  // PERFORMANCE OPTIMIZATION: Image loading defaults
  priority = false,
  loading = 'lazy', // Default to lazy loading for better SI
  fetchPriority = 'auto',
}) => {
  // Use our currency context to format prices
  const { formatPrice } = useCurrency();
  // Use the language context for translations
  const { translations } = useLanguage();
  // Use the cart store to add items
  const addItem = useCartStore((state) => state.addItem);
  const getItemsCount = useCartStore((state) => state.getItemsCount);
  // State for mobile cart icon visibility
  const [isCartIconVisible, setIsCartIconVisible] = useState(false);
  // Ref to track if we're processing a click on the cart icon
  const isAddingToCart = useRef(false);
  
  // Handle adding to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent navigation when clicking cart icon
    
    if (isAddingToCart.current) return;
    isAddingToCart.current = true;
    
    // Extract just the filename from the imageSrc URL for consistent storage
    // This ensures the cart drawer can properly construct the full CloudFront URL
    let imageFilename = imageSrc;
    if (imageSrc && process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN) {
      const cloudFrontDomain = process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN;
      if (imageSrc.startsWith(cloudFrontDomain + '/')) {
        imageFilename = imageSrc.replace(cloudFrontDomain + '/', '');
      }
    }
    
    // Create a simplified product object with the required fields
    const product = {
      _id: productId || productName.replace(/\s+/g, '-').toLowerCase(), // Use productId if available
      name: productName,
      description: '',
      price: salePrice,
      category: category, // Now properly using the destructured category
      imageUrl: imageFilename, // Store only the filename, not the full URL
      createdAt: new Date().toISOString()
    };
    
    // Add to cart
    addItem(product);
    
    // Get total items count after adding
    const totalItems = getItemsCount();
    
    // Show confirmation with toast notification
    toast.success(translations.item_added_to_cart || 'Item added to cart', {
      description: (
        <>
          {productName}
          <div className="mt-1">Total Items in Cart: {totalItems}</div>
        </>
      ),
      duration: 3000,
    });

    // Reset flag after a short delay
    setTimeout(() => {
      isAddingToCart.current = false;
    }, 300);
  };

  // Handle card click for mobile cart icon visibility
  const handleCardClick = (e: React.MouseEvent) => {
    // Toggle cart icon visibility on mobile - navigation is handled by Link wrapper
    setIsCartIconVisible(!isCartIconVisible);
  };
  
  // Handle touch start for mobile devices
  const handleTouchStart = () => {
    // This ensures hover states will be triggered on mobile devices
    // We don't need to add any code here, just having the event handler
    // helps with touch devices recognizing hover states
  };
  
  const cardContent = (
    <div 
      id={`product-card-${productName.replace(/\s+/g, '-').toLowerCase()}`} 
      className={`card ${className}`}
      onClick={handleCardClick}
      onTouchStart={handleTouchStart}
    >
      {/* Cart Icon */}
      <div 
        className={`cartIconContainer ${isCartIconVisible ? 'cartIconVisible' : ''}`}
        onClick={handleAddToCart}
      >
        <ShoppingCart size={18} />
      </div>
      
      <div id={`product-image-container-${productName.replace(/\s+/g, '-').toLowerCase()}`} className="imageContainer">
        <div className="aspect">
          <Image 
            src={imageSrc}
            alt={alt || productName}
            fill
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 40vw, 25vw"
            quality={85}
            className="productImage"
            priority={priority}
            loading={loading}
            fetchPriority={fetchPriority}
          />
        </div>
        
        {/* Play Button - Decorative only, navigation handled by card wrapper */}
        <div className="playButtonContainer">
          <Play size={18} className="playIcon" />
        </div>
      </div>
      <div id={`product-details-${productName.replace(/\s+/g, '-').toLowerCase()}`} className="details">
        <h3 className="productName">{productName}</h3>
        {weight !== undefined && (
          <div className="weight">{translations.weight}: {weight} {translations.ct}</div>
        )}
        <div id={`product-price-${productName.replace(/\s+/g, '-').toLowerCase()}`} className="priceRow">
          <span className="priceText">{formatPrice(salePrice)}</span>
        </div>
      </div>
    </div>
  );

  // Wrap the card with Link for navigation if href is provided
  if (href) {
    return (
      <Link href={href} className="block">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

export default ProductCard; 