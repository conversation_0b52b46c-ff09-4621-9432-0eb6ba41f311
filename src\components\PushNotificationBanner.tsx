'use client';

import { useState, useEffect } from 'react';
import { BellOff } from 'lucide-react';
import { requestNotificationPermission } from '@/lib/firebase';

interface PushNotificationBannerProps {
  vapidKey: string;
}

export default function PushNotificationBanner({ vapidKey }: PushNotificationBannerProps) {
  // Only maintain client-side state
  const [mounted, setMounted] = useState(false);
  const [permission, setPermission] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [browserSupported, setBrowserSupported] = useState<boolean | null>(null);
  
  // Initialize on client-side only
  useEffect(() => {
    console.log('PushNotificationBanner mounted');
    setMounted(true);
    
    // Check if Notification API is supported (only on client)
    if (typeof window !== 'undefined') {
      const isNotificationSupported = 'Notification' in window;
      console.log('Notification API supported:', isNotificationSupported);
      setBrowserSupported(isNotificationSupported);
      
      if (isNotificationSupported) {
        // Get current permission status
        const currentPermission = Notification.permission;
        console.log('Current notification permission:', currentPermission);
        setPermission(currentPermission);
      }
    }
  }, []);
  
  // Handle requesting permission
  const handleEnable = async () => {
    console.log('Enable notifications clicked');
    setLoading(true);
    try {
      const result = await requestNotificationPermission();
      console.log('Permission request result:', result);
      
      // Update permission status after request
      if (typeof window !== 'undefined' && 'Notification' in window) {
        setPermission(Notification.permission);
        console.log('New permission status:', Notification.permission);
      }
    } catch (error) {
      console.error('Failed to enable notifications:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Only render on client side
  if (!mounted) {
    console.log('Not mounted yet, returning null');
    return null;
  }
  
  // Check browser support
  if (browserSupported === false) {
    console.log('Browser does not support notifications');
    return (
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="text-sm text-yellow-600">
          <p>Your browser doesn't support push notifications.</p>
        </div>
      </div>
    );
  }
  
  // Already granted, don't show the button
  if (permission === 'granted') {
    console.log('Permission already granted');
    return null;
  }
  
  // Show the notification banner
  console.log('Showing notification banner');
  return (
    <div className="bg-white shadow-md rounded-lg p-4">
      <div className="flex items-center justify-between">
        <p className="text-sm">
          <BellOff className="inline-block h-4 w-4 mr-1 text-gray-500" />
          Enable notifications to stay updated
        </p>
        <button
          onClick={handleEnable}
          disabled={loading || permission === 'denied'}
          className={`ml-3 px-3 py-1.5 text-xs font-medium rounded-md ${
            permission === 'denied'
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? 'Enabling...' : 'Enable'}
        </button>
      </div>
      
      {permission === 'denied' && (
        <p className="mt-2 text-xs text-red-500">
          Notifications are blocked. Please enable them in your browser settings.
        </p>
      )}
    </div>
  );
} 