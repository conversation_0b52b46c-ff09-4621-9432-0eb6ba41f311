import { NextResponse } from 'next/server';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Image bucket name from environment variables
const imagesBucketName = process.env.S3_IMAGES_BUCKET || 'imagesbucket2025';

/**
 * Get an image file from S3
 * 
 * GET /api/test-s3-image/[key]
 */
export async function GET(
  request: Request,
  { params }: { params: { key: string } }
) {
  try {
    const client = getS3Client();
    
    // Get the file from S3
    const command = new GetObjectCommand({
      Bucket: imagesBucketName,
      Key: params.key,
    });
    
    const response = await client.send(command);
    
    // Convert the readable stream to a buffer
    const chunks: Uint8Array[] = [];
    for await (const chunk of response.Body as any) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);
    
    // Return the file with appropriate headers
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': response.ContentType || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
      },
    });
  } catch (error: any) {
    console.error('Error fetching image from S3:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch image from S3'
    }, { status: 500 });
  }
} 