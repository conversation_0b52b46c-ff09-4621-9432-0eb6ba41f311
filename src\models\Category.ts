import mongoose from 'mongoose';

const subcategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  }
});

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  subcategories: [subcategorySchema],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Ensure indexes for faster queries
categorySchema.index({ name: 1 });

export const Category = mongoose.models.Category || mongoose.model('Category', categorySchema); 