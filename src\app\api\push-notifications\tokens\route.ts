import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

// Define the FCM token schema if it doesn't exist elsewhere
const FCMTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
  },
  userId: String,
  device: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Get or create the model
const FCMToken = mongoose.models.FCMToken || mongoose.model('FCMToken', FCMTokenSchema, 'fcmTokens');

export async function GET() {
  try {
    // Connect to the database
    const { db, status, error } = await connectToDatabase();
    
    if (status === 'error' || !db) {
      console.error('Failed to connect to database:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to connect to database' },
        { status: 500 }
      );
    }
    
    // Fetch tokens using the Mongoose model
    const tokens = await FCMToken.find({})
      .sort({ updatedAt: -1 })
      .limit(50)
      .lean();
    
    return NextResponse.json({
      success: true,
      tokens,
    });
  } catch (error) {
    console.error('Error fetching FCM tokens:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch FCM tokens' },
      { status: 500 }
    );
  }
} 