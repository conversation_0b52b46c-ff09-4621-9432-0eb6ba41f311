import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function GET() {
  try {
    await dbConnect();
    
    // Get a sample customer to verify the schema
    const sampleCustomer = await Customer.findOne().lean();
    
    // Get model schema fields
    const schemaFields = Object.keys(Customer.schema.paths);
    
    return NextResponse.json({
      success: true,
      schemaFields,
      sampleCustomer
    });
  } catch (error: any) {
    console.error('Error fetching customer schema:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer schema', details: error.message },
      { status: 500 }
    );
  }
} 