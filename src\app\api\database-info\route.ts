import { NextResponse } from 'next/server';
import { getDatabaseInfo } from '@/lib/mongodb';

/**
 * API route that fetches database information including the database name
 * Returns a JSON response with database details
 * 
 * GET /api/database-info
 */
export async function GET() {
  try {
    // Get database information
    const dbInfoResult = await getDatabaseInfo();
    
    // If successful, return database info
    if (dbInfoResult.status === 'connected' && dbInfoResult.dbInfo) {
      return NextResponse.json({
        status: 'connected',
        error: null,
        dbInfo: dbInfoResult.dbInfo
      });
    } 
    
    // If there was an error, return error details
    return NextResponse.json({
      status: 'error',
      error: dbInfoResult.error,
      dbInfo: null
    });
  } catch (error: any) {
    // Handle any unexpected errors
    console.error('Unexpected error when getting database info:', error);
    
    return NextResponse.json({
      status: 'error',
      error: {
        message: error.message || 'An unexpected error occurred',
        name: error.name || 'Error',
        explanation: 'There was an unexpected error when trying to retrieve database information.'
      },
      dbInfo: null
    }, { status: 500 });
  }
} 