import { NextResponse } from 'next/server';
import { createCloudFrontDistribution } from '@/lib/cloudfront';

// Bucket names from environment variables
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';
const chunkedVideosBucketName = process.env.S3_CHUNKED_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * Set up CloudFront distribution for video content
 * This handles both regular videos and chunked videos (HLS segments)
 * 
 * POST /api/setup-cloudfront
 */
export async function POST(request: Request) {
  try {
    const { bucketType } = await request.json().catch(() => ({ bucketType: 'chunked' }));
    
    const targetBucket = bucketType === 'videos' ? videosBucketName : chunkedVideosBucketName;
    const bucketDescription = bucketType === 'videos' ? 'regular videos' : 'chunked videos (HLS segments)';
    
    console.log(`🚀 Setting up CloudFront for ${bucketDescription} bucket: ${targetBucket}`);
    
    // Create CloudFront distribution
    const distribution = await createCloudFrontDistribution(targetBucket);
    
    // Return success response with distribution details
    return NextResponse.json({
      success: true,
      message: `CloudFront distribution created successfully for ${bucketDescription}`,
      bucketType,
      bucketName: targetBucket,
      distribution: {
        id: distribution?.Id,
        domainName: distribution?.DomainName,
        status: distribution?.Status,
        enabled: distribution?.DistributionConfig?.Enabled,
        comment: distribution?.DistributionConfig?.Comment
      },
      instructions: {
        envVariable: bucketType === 'videos' 
          ? 'NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN' 
          : 'NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN',
        value: distribution?.DomainName,
        note: 'Add this to your environment variables to enable CloudFront acceleration'
      }
    });
  } catch (error: any) {
    console.error('Error setting up CloudFront:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to set up CloudFront distribution',
      code: error.Code || error.code
    }, { status: 500 });
  }
}

/**
 * Get CloudFront setup status and instructions
 * 
 * GET /api/setup-cloudfront
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'CloudFront setup instructions',
    currentConfig: {
      videosCloudFrontDomain: process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN || 'Not configured',
      chunkedVideosCloudFrontDomain: process.env.NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN || 'Not configured',
      videoBucket: videosBucketName,
      chunkedVideosBucket: chunkedVideosBucketName
    },
    instructions: [
      '1. POST to this endpoint to create CloudFront distributions',
      '2. Add the returned domain names to your environment variables',
      '3. Restart your application for faster video loading',
      '4. Test with your hero videos to see the speed improvement'
    ],
    environmentVariables: {
      'NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN': 'For regular videos',
      'NEXT_PUBLIC_CHUNKED_VIDEOS_CLOUDFRONT_DOMAIN': 'For chunked videos (HLS segments) - RECOMMENDED for hero videos'
    }
  });
} 