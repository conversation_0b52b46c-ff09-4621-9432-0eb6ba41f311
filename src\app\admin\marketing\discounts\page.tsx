'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  PlusCircle, 
  Trash2, 
  Edit, 
  Save, 
  X, 
  Search,
  Calendar,
  Percent,
  Tag,
  DollarSign,
  AlertCircle,
} from 'lucide-react';

// Interface for discount data
interface Discount {
  _id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minPurchase: number;
  maxUses: number;
  usedCount: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'inactive' | 'scheduled';
  products: string[];
  categories: string[];
  description: string;
}

export default function DiscountsPage() {
  const router = useRouter();
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [filteredDiscounts, setFilteredDiscounts] = useState<Discount[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state for creating/editing discounts
  const [formData, setFormData] = useState({
    code: '',
    type: 'percentage' as 'percentage' | 'fixed',
    value: 0,
    minPurchase: 0,
    maxUses: 0,
    startDate: '',
    endDate: '',
    status: 'active' as 'active' | 'inactive' | 'scheduled',
    description: ''
  });

  // Fetch discounts from API
  useEffect(() => {
    fetchDiscounts();
  }, []);

  // Filter discounts when searchTerm changes
  useEffect(() => {
    if (searchTerm.trim()) {
      const filtered = discounts.filter(discount => 
        discount.code.toLowerCase().includes(searchTerm.toLowerCase()) || 
        discount.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredDiscounts(filtered);
    } else {
      setFilteredDiscounts(discounts);
    }
  }, [searchTerm, discounts]);

  const fetchDiscounts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const url = '/api/admin/discounts';
      
      const response = await fetch(url);
      
      if (response.status === 401) {
        // If unauthorized, consider redirecting to login
        // router.push('/admin/login');
        throw new Error('You must be logged in to access this feature');
      }
      
      if (!response.ok) {
        throw new Error('Failed to fetch discounts');
      }
      
      const data = await response.json();
      setDiscounts(data);
      setFilteredDiscounts(data);
    } catch (err) {
      console.error('Error fetching discounts:', err);
      setError(err instanceof Error ? err.message : 'Failed to load discounts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'value' || name === 'minPurchase' || name === 'maxUses' 
        ? parseFloat(value) 
        : value
    });
  };

  // Create a new discount
  const handleCreateDiscount = async () => {
    try {
      const response = await fetch('/api/admin/discounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.status === 401) {
        // If unauthorized, consider redirecting to login
        // router.push('/admin/login');
        throw new Error('You must be logged in to create discounts');
      }
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create discount');
      }
      
      // Get the newly created discount
      const newDiscount = await response.json();
      
      // Add the new discount to the state directly instead of fetching the whole list again
      setDiscounts(prevDiscounts => [newDiscount, ...prevDiscounts]);
      
      // Reset form and state
      setIsCreating(false);
      setFormData({
        code: '',
        type: 'percentage',
        value: 0,
        minPurchase: 0,
        maxUses: 0,
        startDate: '',
        endDate: '',
        status: 'active',
        description: ''
      });
    } catch (err: any) {
      console.error('Error creating discount:', err);
      setError(err.message || 'Failed to create discount. Please try again.');
    }
  };

  // Start editing a discount
  const handleStartEdit = (discount: Discount) => {
    setEditingId(discount._id);
    setFormData({
      code: discount.code,
      type: discount.type,
      value: discount.value,
      minPurchase: discount.minPurchase,
      maxUses: discount.maxUses,
      startDate: formatDateForInput(discount.startDate),
      endDate: formatDateForInput(discount.endDate),
      status: discount.status,
      description: discount.description
    });
  };

  // Format date for input field
  const formatDateForInput = (dateString: string) => {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Save edited discount
  const handleSaveEdit = async () => {
    if (!editingId) return;
    
    try {
      const response = await fetch(`/api/admin/discounts/${editingId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.status === 401) {
        // If unauthorized, consider redirecting to login
        // router.push('/admin/login');
        throw new Error('You must be logged in to update discounts');
      }
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update discount');
      }
      
      // Get the updated discount
      const updatedDiscount = await response.json();
      
      // Update the discounts state directly
      setDiscounts(prevDiscounts => 
        prevDiscounts.map(discount => 
          discount._id === editingId ? updatedDiscount : discount
        )
      );
      
      // Reset editing state
      setEditingId(null);
    } catch (err: any) {
      console.error('Error updating discount:', err);
      setError(err.message || 'Failed to update discount. Please try again.');
    }
  };

  // Delete a discount
  const handleDeleteDiscount = async (id: string) => {
    // Confirm before deleting
    if (!window.confirm('Are you sure you want to delete this discount?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/discounts/${id}`, {
        method: 'DELETE',
      });
      
      if (response.status === 401) {
        // If unauthorized, consider redirecting to login
        // router.push('/admin/login');
        throw new Error('You must be logged in to delete discounts');
      }
      
      if (!response.ok) {
        throw new Error('Failed to delete discount');
      }
      
      // Remove the deleted discount from state
      setDiscounts(prevDiscounts => 
        prevDiscounts.filter(discount => discount._id !== id)
      );
      
    } catch (err) {
      console.error('Error deleting discount:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete discount. Please try again.');
    }
  };

  // Cancel edit or create
  const handleCancel = () => {
    setIsCreating(false);
    setEditingId(null);
    setFormData({
      code: '',
      type: 'percentage',
      value: 0,
      minPurchase: 0,
      maxUses: 0,
      startDate: '',
      endDate: '',
      status: 'active',
      description: ''
    });
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Discounts & Coupons</h1>
        <button 
          onClick={() => setIsCreating(true)}
          disabled={isCreating || !!editingId}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center disabled:opacity-50"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Create New Discount
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          {error}
        </div>
      )}

      {/* Search Bar - simplified without form submission */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search discounts by code or description..."
          className="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Create/Edit Form */}
      {(isCreating || editingId) && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              {isCreating ? 'Create New Discount' : 'Edit Discount'}
            </h2>
            <button onClick={handleCancel} className="text-gray-500 hover:text-gray-700">
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 mb-1">Discount Code</label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="e.g. SUMMER2023"
              />
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Discount Type</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="percentage">Percentage (%)</option>
                <option value="fixed">Fixed Amount ($)</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">
                {formData.type === 'percentage' ? 'Discount Percentage' : 'Discount Amount'}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {formData.type === 'percentage' ? 
                    <Percent className="h-5 w-5 text-gray-400" /> : 
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  }
                </div>
                <input
                  type="number"
                  name="value"
                  value={formData.value}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border border-gray-300 rounded-md"
                  min="0"
                  step={formData.type === 'percentage' ? "1" : "0.01"}
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Minimum Purchase Amount</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  name="minPurchase"
                  value={formData.minPurchase}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border border-gray-300 rounded-md"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Maximum Uses (0 for unlimited)</label>
              <input
                type="number"
                name="maxUses"
                value={formData.maxUses}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                min="0"
              />
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Status</label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="scheduled">Scheduled</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Start Date</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">End Date</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <label className="block text-gray-700 mb-1">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
                placeholder="Describe what this discount is for..."
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={handleCancel}
              className="mr-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={isCreating ? handleCreateDiscount : handleSaveEdit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
            >
              <Save className="w-5 h-5 mr-2" />
              {isCreating ? 'Create Discount' : 'Save Changes'}
            </button>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-500">Loading discounts...</p>
        </div>
      )}

      {/* Discounts Table - update to use filteredDiscounts */}
      {!isLoading && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type & Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage / Limits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Validity Period
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDiscounts.length > 0 ? (
                filteredDiscounts.map((discount) => (
                  <tr key={discount._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{discount.code}</div>
                      <div className="text-sm text-gray-500">{discount.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${discount.type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                          {discount.type === 'percentage' ? (
                            <>{discount.value}%</>
                          ) : (
                            <>${discount.value}</>
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        Used: {discount.usedCount} times
                      </div>
                      <div className="text-sm text-gray-500">
                        {discount.maxUses > 0 ? `Max: ${discount.maxUses}` : 'No limit'}
                      </div>
                      {discount.minPurchase > 0 && (
                        <div className="text-sm text-gray-500">
                          Min purchase: ${discount.minPurchase}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(discount.startDate)} - {formatDate(discount.endDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                        ${discount.status === 'active' ? 'bg-green-100 text-green-800' : 
                          discount.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                          'bg-yellow-100 text-yellow-800'}`}>
                        {discount.status.charAt(0).toUpperCase() + discount.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button 
                        onClick={() => handleStartEdit(discount)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        disabled={editingId !== null || isCreating}
                      >
                        <Edit className="w-5 h-5" />
                      </button>
                      <button 
                        onClick={() => handleDeleteDiscount(discount._id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={editingId !== null || isCreating}
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                    {searchTerm ? 'No discounts match your search criteria.' : 'No discounts available. Create your first discount code!'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
} 