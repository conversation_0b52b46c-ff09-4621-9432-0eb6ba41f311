'use client';

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { 
  Upload, 
  Play, 
  Trash2, 
  AlertT<PERSON>gle, 
  Loader2, 
  CheckCircle,
  XCircle,
  Monitor,
  Smartphone,
  Scissors,
  BarChart3
} from 'lucide-react';

type HeroVideo = {
  _id: string;
  videoUrl: string;
  type: 'mobile' | 'desktop';
  isActive: boolean;
  fileName: string;
  fileSize: number;
  fileType: string;
  createdAt: string;
  updatedAt: string;
  // Chunking-related fields
  isChunked?: boolean;
  chunkingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  chunkingProgress?: number;
  chunkedVideoUrl?: string;
  mediaConvertJobId?: string;
  chunkingError?: string;
};

export default function HeroVideosPage() {
  const [heroVideos, setHeroVideos] = useState<HeroVideo[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedType, setSelectedType] = useState<'mobile' | 'desktop'>('mobile');
  const [processingAction, setProcessingAction] = useState(false);
  const [chunkingStates, setChunkingStates] = useState<Record<string, {
    processing: boolean;
    progress: number;
    status: string;
    error?: string;
    errorDetails?: string;
  }>>({});
  
  // Add state for segment counts [[memory:7612604151137385833]]
  const [segmentCounts, setSegmentCounts] = useState<Record<string, number>>({});
  const [loadingSegments, setLoadingSegments] = useState<Record<string, boolean>>({});
  const [deletingChunks, setDeletingChunks] = useState<Record<string, boolean>>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cache for chunking status with localStorage [[memory:7612604151137385833]]
  const getCachedChunkingStatus = (videoId: string) => {
    try {
      const cached = localStorage.getItem(`chunking_${videoId}`);
      if (cached) {
        const data = JSON.parse(cached);
        const age = Date.now() - data.timestamp;
        if (age < 30000) { // 30 seconds cache
          return data.status;
        }
      }
    } catch (e) {
      console.error('Cache read error:', e);
    }
    return null;
  };

  const setCachedChunkingStatus = (videoId: string, status: any) => {
    try {
      localStorage.setItem(`chunking_${videoId}`, JSON.stringify({
        status,
        timestamp: Date.now()
      }));
    } catch (e) {
      console.error('Cache write error:', e);
    }
  };

  // Optimized parallel API calls [[memory:7612604151137385833]]
  const fetchHeroVideosAndStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/hero-videos');
      
      if (!response.ok) {
        throw new Error('Failed to fetch hero videos');
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch hero videos');
      }
      
      setHeroVideos(data.heroVideos);

      // Parallel status checks for all videos [[memory:7612604151137385833]]
      const videosNeedingStatusCheck = data.heroVideos.filter((video: HeroVideo) => 
        video.chunkingStatus === 'processing' || !video.chunkingStatus
      );

      if (videosNeedingStatusCheck.length > 0) {
        const statusPromises = videosNeedingStatusCheck.map(async (video: HeroVideo) => {
          const cached = getCachedChunkingStatus(video._id);
          if (cached) return { videoId: video._id, ...cached };

          try {
            const statusResponse = await fetch(`/api/hero-videos/chunk?videoId=${video._id}`);
            if (statusResponse.ok) {
              const statusData = await statusResponse.json();
              setCachedChunkingStatus(video._id, statusData);
              return { videoId: video._id, ...statusData };
            }
          } catch (e) {
            console.error(`Status check failed for ${video._id}:`, e);
          }
          return null;
        });

        const statuses = await Promise.all(statusPromises);
        
        // Update chunking states
        const newChunkingStates: Record<string, any> = {};
        statuses.forEach(status => {
          if (status) {
            newChunkingStates[status.videoId] = {
              processing: status.chunkingStatus === 'processing',
              progress: status.chunkingProgress || 0,
              status: status.chunkingStatus || 'pending',
              error: status.chunkingError || '',
              errorDetails: status.errorDetails || ''
            };
          }
        });
        setChunkingStates(prev => ({ ...prev, ...newChunkingStates }));
      }
    } catch (err: any) {
      console.error('Error fetching hero videos:', err);
      setError(err.message || 'An error occurred while fetching hero videos');
    } finally {
      setLoading(false);
    }
  };

  // Fetch hero videos on component mount
  useEffect(() => {
    fetchHeroVideosAndStatus();
  }, []);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('video/')) {
        setError('Please select a valid video file');
        return;
      }
      
      // Validate file size (50MB max)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        setError('File size must be less than 50MB');
        return;
      }
      
      setSelectedFile(file);
      setError(null);
    }
  };

  // Handle video upload
  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a video file');
      return;
    }

    // Check if there's already an active video of the selected type
    const existingVideo = heroVideos.find(video => video.type === selectedType && video.isActive);
    if (existingVideo) {
      if (!confirm(`There's already an active ${selectedType} hero video. Uploading a new one will replace it. Continue?`)) {
        return;
      }
    }

    try {
      setUploading(true);
      setError(null);
      setSuccess(null);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('type', selectedType);

      const response = await fetch('/api/hero-videos/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload video');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to upload video');
      }

      setSuccess(`${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} hero video uploaded successfully!`);
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      // Refresh the videos list
      await fetchHeroVideosAndStatus();
    } catch (err: any) {
      console.error('Error uploading video:', err);
      setError(err.message || 'An error occurred while uploading the video');
    } finally {
      setUploading(false);
    }
  };

  // Handle video deletion
  const handleDeleteVideo = async (videoId: string, videoType: string) => {
    if (!confirm(`Are you sure you want to delete this ${videoType} hero video? This action cannot be undone.`)) {
      return;
    }

    try {
      setProcessingAction(true);
      setError(null);

      const response = await fetch(`/api/hero-videos/${videoId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete video');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to delete video');
      }

      setSuccess(`${videoType.charAt(0).toUpperCase() + videoType.slice(1)} hero video deleted successfully!`);
      
      // Refresh the videos list
      await fetchHeroVideosAndStatus();
    } catch (err: any) {
      console.error('Error deleting video:', err);
      setError(err.message || 'An error occurred while deleting the video');
    } finally {
      setProcessingAction(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get videos by type
  const mobileVideo = heroVideos.find(video => video.type === 'mobile' && video.isActive);
  const desktopVideo = heroVideos.find(video => video.type === 'desktop' && video.isActive);

  // Debounced chunking status check [[memory:7612604151137385833]]
  const checkChunkingStatus = useMemo(() => {
    const debounce = (func: Function, wait: number) => {
      let timeout: NodeJS.Timeout;
      return (...args: any[]) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
      };
    };

    return debounce(async (videoId: string) => {
      try {
        const response = await fetch(`/api/hero-videos/chunk?videoId=${videoId}`);
        if (response.ok) {
          const data = await response.json();
          setCachedChunkingStatus(videoId, data);
          
          const newState = {
            processing: data.chunkingStatus === 'processing',
            progress: data.chunkingProgress || 0,
            status: data.chunkingStatus || 'pending',
            error: data.chunkingError || '',
            errorDetails: data.errorDetails || ''
          };
          
          setChunkingStates(prev => ({
            ...prev,
            [videoId]: newState
          }));

          // If job completed, refresh the hero videos data to update the main video object
          if (data.chunkingStatus === 'completed' || data.chunkingStatus === 'failed') {
            setTimeout(() => {
              fetchHeroVideosAndStatus();
            }, 1000);
          }
        }
      } catch (error) {
        console.error('Error checking chunking status:', error);
      }
    }, 300);
  }, []);

  // Handle video chunking
  const handleChunkVideo = useCallback(async (videoId: string, videoType: string) => {
    if (!confirm(`Start chunking this ${videoType} video? This will create 1-second segments (1 part per second of video). This will take a few minutes.`)) {
      return;
    }

    try {
      setChunkingStates(prev => ({
        ...prev,
        [videoId]: { processing: true, progress: 0, status: 'processing' }
      }));

      const response = await fetch('/api/hero-videos/chunk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const fullError = errorData.errorDetails ? 
          `${errorData.error}: ${errorData.errorDetails}` : 
          (errorData.error || 'Failed to start chunking');
        throw new Error(fullError);
      }

      const data = await response.json();

      if (!data.success) {
        const fullError = data.errorDetails ? 
          `${data.error}: ${data.errorDetails}` : 
          (data.error || 'Failed to start chunking');
        throw new Error(fullError);
      }

      setSuccess(`${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video chunking started successfully!`);
      
      // Start polling for status updates with completion detection
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch(`/api/hero-videos/chunk?videoId=${videoId}`);
          if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            
            // Update chunking state
            setChunkingStates(prev => ({
              ...prev,
              [videoId]: {
                processing: statusData.chunkingStatus === 'processing',
                progress: statusData.chunkingProgress || 0,
                status: statusData.chunkingStatus || 'pending',
                error: statusData.chunkingError || '',
                errorDetails: statusData.errorDetails || ''
              }
            }));

            // If job completed or failed, stop polling and refresh data
            if (statusData.chunkingStatus === 'completed') {
              clearInterval(pollInterval);
              setSuccess(`${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video chunked successfully!`);
              // Refresh hero videos to show updated status
              setTimeout(() => {
                fetchHeroVideosAndStatus();
              }, 1000);
            } else if (statusData.chunkingStatus === 'failed') {
              clearInterval(pollInterval);
              setError(`Video chunking failed: ${statusData.chunkingError || 'Unknown error'}`);
              setTimeout(() => {
                fetchHeroVideosAndStatus();
              }, 1000);
            }
          }
        } catch (error) {
          console.error('Error in polling:', error);
        }
      }, 3000); // Poll every 3 seconds

      // Stop polling after 15 minutes as safety backup
      setTimeout(() => {
        clearInterval(pollInterval);
      }, 900000);

    } catch (err: any) {
      console.error('Error starting video chunking:', err);
      setError(err.message || 'An error occurred while starting video chunking');
      setChunkingStates(prev => ({
        ...prev,
        [videoId]: { 
          processing: false, 
          progress: 0, 
          status: 'failed',
          error: err.message || 'Unknown error occurred',
          errorDetails: ''
        }
      }));
    }
  }, [checkChunkingStatus]);

  // Fetch segment count for chunked videos [[memory:7612604151137385833]]
  const fetchSegmentCount = useCallback(async (videoId: string) => {
    if (loadingSegments[videoId]) return; // Prevent duplicate requests
    
    try {
      setLoadingSegments(prev => ({ ...prev, [videoId]: true }));
      
      const response = await fetch(`/api/hero-videos/chunk?videoId=${videoId}`, {
        method: 'PATCH'
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSegmentCounts(prev => ({ ...prev, [videoId]: data.segmentCount }));
        }
      }
    } catch (error) {
      console.error('Error fetching segment count:', error);
    } finally {
      setLoadingSegments(prev => ({ ...prev, [videoId]: false }));
    }
  }, [loadingSegments]);

  // Delete chunked video parts [[memory:7612604151137385833]]
  const handleDeleteChunkedParts = useCallback(async (videoId: string, videoType: string) => {
    if (!confirm(`Delete all chunked parts for this ${videoType} video? This will revert back to the original video and you'll need to chunk again if needed.`)) {
      return;
    }

    try {
      setDeletingChunks(prev => ({ ...prev, [videoId]: true }));
      setProcessingAction(true);

      const response = await fetch(`/api/hero-videos/chunk?videoId=${videoId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete chunked parts');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to delete chunked parts');
      }

      setSuccess(`${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video chunked parts deleted successfully! Video reverted to original.`);
      
      // Clear segment count for this video
      setSegmentCounts(prev => {
        const newCounts = { ...prev };
        delete newCounts[videoId];
        return newCounts;
      });
      
      // Clear chunking state
      setChunkingStates(prev => {
        const newStates = { ...prev };
        delete newStates[videoId];
        return newStates;
      });
      
      // Refresh hero videos data
      await fetchHeroVideosAndStatus();

    } catch (err: any) {
      console.error('Error deleting chunked parts:', err);
      setError(err.message || 'An error occurred while deleting chunked parts');
    } finally {
      setDeletingChunks(prev => ({ ...prev, [videoId]: false }));
      setProcessingAction(false);
    }
  }, [fetchHeroVideosAndStatus]);

  // Auto-fetch segment counts for chunked videos [[memory:7612604151137385833]]
  useEffect(() => {
    const chunkedVideos = heroVideos.filter(video => 
      video.isChunked && video.chunkingStatus === 'completed' && !segmentCounts[video._id]
    );
    
    if (chunkedVideos.length > 0) {
      // Parallel fetch for all chunked videos
      Promise.all(chunkedVideos.map(video => fetchSegmentCount(video._id)));
    }
  }, [heroVideos, segmentCounts, fetchSegmentCount]);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Hero Videos Management</h1>
      
      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Upload New Hero Video</h2>
        
        {/* File Upload */}
        <div className="mb-4">
          <label htmlFor="video-file" className="block text-sm font-medium text-gray-700 mb-2">
            Select Video File
          </label>
          <input
            ref={fileInputRef}
            type="file"
            id="video-file"
            accept="video/*"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-semibold
              file:bg-blue-50 file:text-blue-700
              hover:file:bg-blue-100"
          />
          {selectedFile && (
            <p className="text-sm text-gray-600 mt-1">
              Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
            </p>
          )}
        </div>
        
        {/* Device Type Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Device Type
          </label>
          <div className="flex space-x-4">
            <label className={`flex items-center p-3 border rounded-md cursor-pointer ${
              selectedType === 'mobile' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            } ${mobileVideo ? 'opacity-50' : ''}`}>
              <input
                type="radio"
                name="deviceType"
                value="mobile"
                checked={selectedType === 'mobile'}
                onChange={(e) => setSelectedType(e.target.value as 'mobile')}
                className="sr-only"
              />
              <Smartphone size={20} className="mr-2" />
              <span>Mobile</span>
              {mobileVideo && <span className="ml-2 text-xs text-gray-500">(Active)</span>}
            </label>
            
            <label className={`flex items-center p-3 border rounded-md cursor-pointer ${
              selectedType === 'desktop' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            } ${desktopVideo ? 'opacity-50' : ''}`}>
              <input
                type="radio"
                name="deviceType"
                value="desktop"
                checked={selectedType === 'desktop'}
                onChange={(e) => setSelectedType(e.target.value as 'desktop')}
                className="sr-only"
              />
              <Monitor size={20} className="mr-2" />
              <span>Desktop</span>
              {desktopVideo && <span className="ml-2 text-xs text-gray-500">(Active)</span>}
            </label>
          </div>
        </div>
        
        {/* Upload Button */}
        <button
          onClick={handleUpload}
          disabled={!selectedFile || uploading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
        >
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload Video
            </>
          )}
        </button>
      </div>
      
      {/* Messages */}
      {error && (
        <div className="p-4 mb-6 bg-red-100 text-red-700 rounded flex items-center">
          <XCircle size={18} className="mr-2" />
          <p>{error}</p>
        </div>
      )}
      
      {success && (
        <div className="p-4 mb-6 bg-green-100 text-green-700 rounded flex items-center">
          <CheckCircle size={18} className="mr-2" />
          <p>{success}</p>
        </div>
      )}
      
      {/* Current Videos */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Current Hero Videos</h2>
        
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <Loader2 className="mr-2 h-6 w-6 animate-spin text-blue-500" />
            <p className="text-gray-500">Loading videos...</p>
          </div>
        ) : heroVideos.length === 0 ? (
          <div className="p-6 text-center bg-gray-50 border border-gray-200 rounded">
            <Play className="mx-auto text-gray-400 mb-2" size={48} />
            <p className="text-gray-600">No hero videos uploaded yet</p>
            <p className="text-sm text-gray-500 mt-1">Upload your first hero video above</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Mobile Video */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Smartphone className="mr-2 text-blue-600" size={20} />
                <h3 className="font-medium">Mobile Hero Video</h3>
              </div>
              
              {mobileVideo ? (
                <div>
                  <video
                    src={mobileVideo.videoUrl}
                    controls
                    className="w-full h-48 rounded mb-3 bg-gray-100"
                  />
                  <div className="text-sm text-gray-600 space-y-1 mb-3">
                    <p><strong>File:</strong> {mobileVideo.fileName}</p>
                    <p><strong>Size:</strong> {formatFileSize(mobileVideo.fileSize)}</p>
                    <p><strong>Uploaded:</strong> {new Date(mobileVideo.createdAt).toLocaleDateString()}</p>
                  </div>
                  
                  {/* Chunk Button and Status - Mobile */}
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-3">
                    {chunkingStates[mobileVideo._id]?.processing ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-blue-600">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span className="text-sm font-medium">Chunking in progress...</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${chunkingStates[mobileVideo._id]?.progress || 0}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">
                          Progress: {chunkingStates[mobileVideo._id]?.progress || 0}% • Creating 1-second video segments
                        </div>
                      </div>
                    ) : (mobileVideo.isChunked && mobileVideo.chunkingStatus === 'completed') || chunkingStates[mobileVideo._id]?.status === 'completed' ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-green-600">
                            <CheckCircle className="mr-2 h-4 w-4" />
                            <span className="text-sm font-medium">Video chunked successfully</span>
                          </div>
                          {segmentCounts[mobileVideo._id] && (
                            <div className="flex items-center text-blue-600">
                              <BarChart3 className="mr-1 h-4 w-4" />
                              <span className="text-sm font-medium">Parts: {segmentCounts[mobileVideo._id]}</span>
                            </div>
                          )}
                        </div>
                        <button
                          onClick={() => handleDeleteChunkedParts(mobileVideo._id, 'mobile')}
                          disabled={deletingChunks[mobileVideo._id] || processingAction}
                          className="flex items-center text-orange-600 hover:text-orange-800 disabled:opacity-50 text-sm"
                        >
                          {deletingChunks[mobileVideo._id] ? (
                            <>
                              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                              Deleting chunks...
                            </>
                          ) : (
                            <>
                              <Trash2 className="mr-1 h-3 w-3" />
                              Delete Chunked Parts
                            </>
                          )}
                        </button>
                      </div>
                    ) : chunkingStates[mobileVideo._id]?.status === 'failed' || mobileVideo.chunkingStatus === 'failed' ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-red-600">
                          <XCircle className="mr-2 h-4 w-4" />
                          <span className="text-sm font-medium">Chunking failed</span>
                        </div>
                        <div className="text-xs text-red-500 bg-red-50 p-2 rounded border border-red-200">
                          <strong>Error:</strong> {chunkingStates[mobileVideo._id]?.error || mobileVideo.chunkingError || 'Unknown error occurred'}
                        </div>
                        <button
                          onClick={() => handleChunkVideo(mobileVideo._id, 'mobile')}
                          disabled={processingAction}
                          className="flex items-center text-blue-600 hover:text-blue-800 disabled:opacity-50 text-sm"
                        >
                          <Scissors className="mr-1 h-3 w-3" />
                          Retry Chunking
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleChunkVideo(mobileVideo._id, 'mobile')}
                        disabled={processingAction}
                        className="flex items-center text-blue-600 hover:text-blue-800 disabled:opacity-50 text-sm font-medium"
                      >
                        <Scissors className="mr-2 h-4 w-4" />
                        Chunk Video (1-sec segments)
                      </button>
                    )}
                  </div>

                  <button
                    onClick={() => handleDeleteVideo(mobileVideo._id, 'mobile')}
                    disabled={processingAction}
                    className="flex items-center text-red-600 hover:text-red-800 disabled:opacity-50"
                  >
                    <Trash2 size={16} className="mr-1" />
                    Delete Video
                  </button>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <Smartphone className="mx-auto mb-2" size={32} />
                  <p>No mobile hero video uploaded</p>
                </div>
              )}
            </div>
            
            {/* Desktop Video */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Monitor className="mr-2 text-green-600" size={20} />
                <h3 className="font-medium">Desktop Hero Video</h3>
              </div>
              
              {desktopVideo ? (
                <div>
                  <video
                    src={desktopVideo.videoUrl}
                    controls
                    className="w-full h-48 rounded mb-3 bg-gray-100"
                  />
                  <div className="text-sm text-gray-600 space-y-1 mb-3">
                    <p><strong>File:</strong> {desktopVideo.fileName}</p>
                    <p><strong>Size:</strong> {formatFileSize(desktopVideo.fileSize)}</p>
                    <p><strong>Uploaded:</strong> {new Date(desktopVideo.createdAt).toLocaleDateString()}</p>
                  </div>
                  
                  {/* Chunk Button and Status - Desktop */}
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-3">
                    {chunkingStates[desktopVideo._id]?.processing ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-green-600">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span className="text-sm font-medium">Chunking in progress...</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${chunkingStates[desktopVideo._id]?.progress || 0}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">
                          Progress: {chunkingStates[desktopVideo._id]?.progress || 0}% • Creating 1-second video segments
                        </div>
                      </div>
                    ) : (desktopVideo.isChunked && desktopVideo.chunkingStatus === 'completed') || chunkingStates[desktopVideo._id]?.status === 'completed' ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-green-600">
                            <CheckCircle className="mr-2 h-4 w-4" />
                            <span className="text-sm font-medium">Video chunked successfully</span>
                          </div>
                          {segmentCounts[desktopVideo._id] && (
                            <div className="flex items-center text-green-600">
                              <BarChart3 className="mr-1 h-4 w-4" />
                              <span className="text-sm font-medium">Parts: {segmentCounts[desktopVideo._id]}</span>
                            </div>
                          )}
                        </div>
                        <button
                          onClick={() => handleDeleteChunkedParts(desktopVideo._id, 'desktop')}
                          disabled={deletingChunks[desktopVideo._id] || processingAction}
                          className="flex items-center text-orange-600 hover:text-orange-800 disabled:opacity-50 text-sm"
                        >
                          {deletingChunks[desktopVideo._id] ? (
                            <>
                              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                              Deleting chunks...
                            </>
                          ) : (
                            <>
                              <Trash2 className="mr-1 h-3 w-3" />
                              Delete Chunked Parts
                            </>
                          )}
                        </button>
                      </div>
                    ) : chunkingStates[desktopVideo._id]?.status === 'failed' || desktopVideo.chunkingStatus === 'failed' ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-red-600">
                          <XCircle className="mr-2 h-4 w-4" />
                          <span className="text-sm font-medium">Chunking failed</span>
                        </div>
                        <div className="text-xs text-red-500 bg-red-50 p-2 rounded border border-red-200">
                          <strong>Error:</strong> {chunkingStates[desktopVideo._id]?.error || desktopVideo.chunkingError || 'Unknown error occurred'}
                        </div>
                        <button
                          onClick={() => handleChunkVideo(desktopVideo._id, 'desktop')}
                          disabled={processingAction}
                          className="flex items-center text-green-600 hover:text-green-800 disabled:opacity-50 text-sm"
                        >
                          <Scissors className="mr-1 h-3 w-3" />
                          Retry Chunking
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleChunkVideo(desktopVideo._id, 'desktop')}
                        disabled={processingAction}
                        className="flex items-center text-green-600 hover:text-green-800 disabled:opacity-50 text-sm font-medium"
                      >
                        <Scissors className="mr-2 h-4 w-4" />
                        Chunk Video (1-sec segments)
                      </button>
                    )}
                  </div>

                  <button
                    onClick={() => handleDeleteVideo(desktopVideo._id, 'desktop')}
                    disabled={processingAction}
                    className="flex items-center text-red-600 hover:text-red-800 disabled:opacity-50"
                  >
                    <Trash2 size={16} className="mr-1" />
                    Delete Video
                  </button>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <Monitor className="mx-auto mb-2" size={32} />
                  <p>No desktop hero video uploaded</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 