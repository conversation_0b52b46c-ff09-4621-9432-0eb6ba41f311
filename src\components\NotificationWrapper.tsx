'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamic import with ssr: false is allowed in Client Components
const PushNotificationBanner = dynamic(
  () => import('@/components/PushNotificationBanner'),
  { ssr: false }
);

interface NotificationWrapperProps {
  vapidKey: string;
}

export default function NotificationWrapper({ vapidKey }: NotificationWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);
  const isPushNotificationEnabled = process.env.NEXT_PUBLIC_PUSH_NOTIFICATION_SHOW === 'true';
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  if (!isMounted || !isPushNotificationEnabled) {
    return null;
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <PushNotificationBanner vapidKey={vapidKey} />
    </div>
  );
} 