'use client';
import React, { useEffect, useState } from 'react';
import Particles, { initParticlesEngine } from '@tsparticles/react';
import { loadSlim } from '@tsparticles/slim'; // loads tsparticles slim shape preset

export const SparklesCore = ({
  id,
  background,
  minSize,
  maxSize,
  particleDensity,
  className,
}: {
  id: string;
  background: string;
  minSize: number;
  maxSize?: number;
  particleDensity?: number;
  className?: string;
}) => {
  const [engine, setEngine] = useState<any>(null);

  useEffect(() => {
    if (!engine) {
      return;
    }

    loadSlim(engine);
  }).then(() => {
    // ... existing code ...
  });

  return (
    <div id={id} className={'w-full h-full absolute top-0 left-0 ' + className}>
      <Particles
        id="tsparticles"
        options={{
          particles: {
            number: {
              value: particleDensity,
            },
            size: {
              value: {
                min: minSize,
                max: maxSize,
              },
            },
            color: {
              value: background,
            },
            shape: {
              type: 'circle',
            },
            opacity: {
              value: 1,
            },
            move: {
              enable: true,
              speed: 2,
              direction: 'none',
              straight: false,
              outModes: 'out',
            },
          },
          interactivity: {
            events: {
              onHover: {
                enable: true,
                mode: 'grab',
              },
              onClick: {
                enable: true,
                mode: 'push',
              },
            },
            modes: {
              grab: {
                distance: 100,
                links: {
                  enable: true,
                },
              },
              push: {
                particles_nb: 1,
              },
            },
          },
          background: {
            color: background,
          },
        }}
        init={initParticlesEngine}
        style={{
          width: '100%',
          height: '100%',
          position: 'absolute',
          top: 0,
          left: 0,
        }}
        minSize={minSize}
        maxSize={maxSize}
        enableHorizontalTransform={false}
        enableVerticalTransform={false}
      />
    </div>
  );
}; 