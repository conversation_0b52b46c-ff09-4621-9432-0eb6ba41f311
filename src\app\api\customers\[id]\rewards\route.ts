import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(req: Request, { params }: Params) {
  try {
    const { id } = params;
    const { rewardId, rewardName, issuedDate, replaceAllRewards = true } = await req.json();
    
    await dbConnect();
    
    // Validate input
    if (!rewardId || !rewardName || !issuedDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Find customer and add reward
    const customer = await Customer.findById(id);
    
    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }
    
    // Initialize currentRewards array if it doesn't exist
    if (!customer.currentRewards) {
      customer.currentRewards = [];
    }
    
    // Always replace all rewards (we only want one reward per customer)
    // This ensures there's only one number in current rewards
    customer.currentRewards = [{
      id: rewardId,
      name: rewardName,
      issuedDate: issuedDate
    }];
    
    // Save customer
    await customer.save();
    
    return NextResponse.json({
      success: true,
      message: 'Reward added successfully',
      customer: customer
    });
  } catch (error: any) {
    console.error('Error adding customer reward:', error);
    return NextResponse.json(
      { 
        error: 'Failed to add reward',
        details: error.message || 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 