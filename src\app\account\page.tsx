'use client';

import { useAuth } from '@/contexts/AuthContext';
import { User, Mail, Phone, MapPin } from 'lucide-react';

export default function AccountProfilePage() {
  const { customer, isLoading } = useAuth();
  
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
        </div>
      </div>
    );
  }
  
  if (!customer) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <p className="text-gray-500">Please log in to view your profile information.</p>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h2 className="font-semibold text-xl text-gray-800">My Profile</h2>
        <p className="text-gray-500 text-sm mt-1">View and manage your account information</p>
      </div>
      
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <User size={32} className="text-blue-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-xl font-semibold">{customer.firstName} {customer.lastName}</h3>
            <p className="text-gray-500">Customer Account</p>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-700 mb-4">Contact Information</h3>
          
          <div className="space-y-3">
            <div className="flex items-center">
              <Mail className="text-gray-400 mr-3" size={18} />
              <div>
                <p className="text-sm text-gray-500">Email Address</p>
                <p className="font-medium">{customer.email}</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <Phone className="text-gray-400 mr-3" size={18} />
              <div>
                <p className="text-sm text-gray-500">Phone Number</p>
                <p className="font-medium">{customer.phone || 'Not provided'}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-700 mb-4">Shipping Address</h3>
          
          <div className="flex items-start">
            <MapPin className="text-gray-400 mr-3 mt-1" size={18} />
            <div>
              <p className="font-medium">{customer.address.street}</p>
              <p>{customer.address.city}, {customer.address.province} {customer.address.postalCode}</p>
              <p>{customer.address.country}</p>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            Edit Profile
          </button>
        </div>
      </div>
    </div>
  );
} 