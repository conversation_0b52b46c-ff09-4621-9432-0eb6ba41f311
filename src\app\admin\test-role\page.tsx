'use client';

import { useEffect, useState } from 'react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { ShieldCheck, X } from 'lucide-react';

export default function TestRolePage() {
  const { user, hasPermission } = useAdminAuth();
  const [permissionReport, setPermissionReport] = useState<Record<string, Record<string, boolean>>>({});
  
  // Resources and actions to test
  const resources = [
    'products', 
    'categories', 
    'inventory', 
    'orders', 
    'returns', 
    'customers', 
    'reviews', 
    'analytics', 
    'users'
  ];
  
  const actions = ['view', 'create', 'update', 'delete'];
  
  // Generate permission report on component mount
  useEffect(() => {
    const report: Record<string, Record<string, boolean>> = {};
    
    resources.forEach(resource => {
      report[resource] = {};
      actions.forEach(action => {
        report[resource][action] = hasPermission(resource, action);
      });
    });
    
    setPermissionReport(report);
  }, [hasPermission]);
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Role Permissions</h1>
      
      {user && (
        <div className="bg-white p-6 shadow-sm rounded-md mb-8">
          <h2 className="text-lg font-semibold mb-4">User Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-gray-500">Name:</span>
              <span className="ml-2 font-medium">{user.firstName} {user.lastName}</span>
            </div>
            <div>
              <span className="text-gray-500">Email:</span>
              <span className="ml-2 font-medium">{user.email}</span>
            </div>
            <div>
              <span className="text-gray-500">Role:</span>
              <span className="ml-2 font-medium">{user.role.name}</span>
            </div>
            <div>
              <span className="text-gray-500">Status:</span>
              <span className={`ml-2 font-medium ${user.isActive ? 'text-green-600' : 'text-red-600'}`}>
                {user.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white shadow-sm rounded-md overflow-hidden">
        <div className="p-4 bg-gray-50 border-b">
          <h2 className="text-lg font-semibold">Permission Test Matrix</h2>
          <p className="text-sm text-gray-500 mt-1">
            This table shows what actions you have permission to perform on different resources.
          </p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Resource
                </th>
                {actions.map(action => (
                  <th key={action} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {action}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Object.keys(permissionReport).map(resource => (
                <tr key={resource} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="font-medium capitalize">{resource}</span>
                  </td>
                  {actions.map(action => (
                    <td key={`${resource}-${action}`} className="px-6 py-4 whitespace-nowrap">
                      {permissionReport[resource][action] ? (
                        <ShieldCheck className="text-green-500 h-5 w-5" />
                      ) : (
                        <X className="text-red-500 h-5 w-5" />
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4 text-blue-800">
        <p className="text-sm">
          <strong>Note:</strong> Use this page to verify your role's permissions. If you don't 
          have access to certain sections of the admin panel, it's because your role doesn't have 
          the required permissions.
        </p>
      </div>
    </div>
  );
} 