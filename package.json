{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "node scripts/create-admin.js", "notifications": "node scripts/notification-scheduler.js", "analyze": "cross-env ANALYZE=true npm run build", "build:analyze": "npm run analyze"}, "dependencies": {"@aws-sdk/client-cloudfront": "^3.774.0", "@aws-sdk/client-mediaconvert": "^3.839.0", "@aws-sdk/client-s3": "^3.774.0", "@aws-sdk/client-translate": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.774.0", "@paypal/react-paypal-js": "^8.8.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@react-pdf/renderer": "^4.3.0", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/bcryptjs": "^2.4.6", "@types/rc-slider": "^9.3.0", "@types/three": "^0.176.0", "@use-gesture/react": "^10.3.1", "@videojs/http-streaming": "^3.17.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "framer-motion": "^12.11.0", "hls.js": "^1.6.5", "js-file-download": "^0.4.12", "json2csv": "^6.0.0-alpha.2", "lucide-react": "^0.484.0", "mongodb": "^6.15.0", "mongoose": "^8.13.1", "next": "15.2.3", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "rc-slider": "^11.1.8", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-to-print": "^3.0.5", "recharts": "^2.15.1", "sonner": "^2.0.3", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "three": "^0.176.0", "tw-animate-css": "^1.2.5", "video.js": "^8.22.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4", "typescript": "^5"}}