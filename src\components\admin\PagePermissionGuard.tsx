'use client';

import React, { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

/**
 * A component that checks if the user has permission to access the current page
 * and redirects to the admin dashboard if not
 */
export default function PagePermissionGuard({ children }: { children: React.ReactNode }) {
  const { hasPermission } = useAdminAuth();
  const pathname = usePathname();
  const router = useRouter();

  // The permission mapping for different admin routes
  // Maps URL patterns to required resource and action
  const permissionMap: Record<string, { resource: string, action: string }> = {
    // Products and related pages
    '/admin/products': { resource: 'products', action: 'view' },
    '/admin/add-product': { resource: 'products', action: 'create' },
    '/admin/edit-product': { resource: 'products', action: 'update' },
    '/admin/categories': { resource: 'categories', action: 'view' },
    '/admin/inventory': { resource: 'inventory', action: 'view' },
    '/admin/bulk-import-export': { resource: 'products', action: 'update' },
    '/admin/reviews': { resource: 'reviews', action: 'view' },
    
    // Orders and related pages
    '/admin/orders': { resource: 'orders', action: 'view' },
    '/admin/orders/returns': { resource: 'returns', action: 'view' },
    '/admin/orders/abandoned-carts': { resource: 'orders', action: 'view' },
    '/admin/orders/invoices': { resource: 'orders', action: 'view' },
    
    // Analytics and dashboard
    '/admin/analytics': { resource: 'analytics', action: 'view' },
    '/admin/analytics/dashboard': { resource: 'analytics', action: 'view' },
    '/admin/analytics/sales': { resource: 'analytics', action: 'view' },
    '/admin/analytics/products': { resource: 'analytics', action: 'view' },
    '/admin/analytics/customers': { resource: 'analytics', action: 'view' },
    
    // Customer management
    '/admin/customers': { resource: 'customers', action: 'view' },
    '/admin/customers/view': { resource: 'customers', action: 'view' },
    '/admin/customers/loyalty': { resource: 'customers', action: 'view' },
    '/admin/customers/support': { resource: 'customers', action: 'view' },
    
    // User roles and permissions
    '/admin/user-roles': { resource: 'users', action: 'view' },
    '/admin/user-roles/users': { resource: 'users', action: 'view' },
  };

  useEffect(() => {
    // Skip login page check
    if (pathname === '/admin/login') {
      return;
    }
    
    // Check each route in the permission map
    for (const route in permissionMap) {
      // If the current path starts with the route key, check permission
      if (pathname.startsWith(route)) {
        const { resource, action } = permissionMap[route];
        if (!hasPermission(resource, action)) {
          // If no permission, redirect to admin dashboard
          router.push('/admin');
          return;
        }
        // Permission granted, exit the loop
        break;
      }
    }
  }, [pathname, hasPermission, router]);

  return <>{children}</>;
} 