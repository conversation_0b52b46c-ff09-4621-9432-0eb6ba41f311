'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronRight, ChevronLeft, Share2, Clock, CalendarDays } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { blogPosts } from '../page'; // Import blog posts from the main blog page

// Define blog post type with multilingual support
type LocalizedContent = {
  title: string;
  excerpt: string;
  content: string;
};

type BlogPost = {
  id: string;
  slug: string;
  image: string;
  category: string;
  author: string;
  authorImage: string;
  date: {
    English: string;
    French: string;
    Italian: string;
  };
  readTime: {
    English: string;
    French: string;
    Italian: string;
  };
  localized: {
    English: LocalizedContent;
    French: LocalizedContent;
    Italian: LocalizedContent;
  };
};

// Function to get the translation key for a category
const getCategoryKey = (category: string): string => {
  switch (category) {
    case 'Gemstone History':
      return 'gemstone_history';
    case 'Buying Guide':
      return 'buying_guide';
    case 'Sustainability':
      return 'sustainability';
    case 'Maintenance':
      return 'maintenance';
    case 'Gemstone Spotlight':
      return 'gemstone_spotlight';
    case 'Market Insights':
      return 'market_insights';
    default:
      return '';
  }
};

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const { language, translations } = useLanguage();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
    
    // Find the current post based on the slug
    const currentPost = blogPosts.find(post => post.slug === params.slug);
    setPost(currentPost || null);
    
    // Find related posts (same category, excluding current post)
    if (currentPost) {
      const related = blogPosts
        .filter(p => p.category === currentPost.category && p.id !== currentPost.id)
        .slice(0, 3);
      setRelatedPosts(related);
    }
  }, [params.slug]);
  
  if (!isMounted) {
    return null;
  }
  
  if (!post) {
    return (
      <div id="blog-post-not-found-container" className="min-h-screen bg-[#f8f8f8] pt-24 flex items-center justify-center">
        <div id="blog-post-not-found-content" className="text-center">
          <h1 className="text-2xl font-bold mb-4">{translations.no_posts_found}</h1>
          <p className="mb-6">{translations.no_posts_description}</p>
          <Link 
            href="/blog" 
            className="px-6 py-3 bg-gradient-to-b from-[#51575F] to-[#1F2937] border border-[#D1C29B] rounded-full hover:bg-gradient-to-tl hover:from-[#6B7280] hover:to-[#374151] active:bg-gradient-to-tl active:from-[#4B5563] active:to-[#111827] active:scale-95 transition-all shadow-md font-medium text-white inline-block"
          >
            {translations.back_to_blog}
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div id="blog-post-page-container" className="min-h-screen bg-[#f8f8f8] pt-10">
      {/* Hero Section */}
      <div id="blog-post-hero-section" className="w-full h-[150px] md:h-[150px] lg:h-[150px] relative">
        <Image 
          src={post.image}
          alt={post.localized[language].title}
          fill
          className="object-cover brightness-[0.8]"
          priority
        />
        <div id="blog-post-hero-overlay" className="absolute inset-0 bg-[#f8f8f8]">
          <div id="blog-post-hero-content" className="container mx-auto h-full flex flex-col px-4">
            <span className="px-3 py-1 bg-gradient-to-b from-[#51575F] to-[#1F2937] text-xs font-medium rounded-full shadow-sm inline-block mb-4 w-fit text-white">
              {translations[getCategoryKey(post.category)]}
            </span>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-black max-w-4xl font-serif">
              {post.localized[language].title}
            </h1>
          </div>
        </div>
      </div>
      
      {/* Breadcrumb */}
      <div id="blog-post-breadcrumb-container" className="container mx-auto px-4 py-6">
        <div id="blog-post-breadcrumb-content" className="flex items-center text-sm text-gray-600 flex-wrap">
          <Link href="/" className="hover:text-blue-600">{translations.breadcrumb_home}</Link>
          <ChevronRight size={16} className="mx-2" />
          <Link href="/blog" className="hover:text-blue-600">{translations.blog}</Link>
          <ChevronRight size={16} className="mx-2" />
          <span className="font-medium text-gray-900 truncate max-w-[200px] md:max-w-none">
            {post.localized[language].title}
          </span>
        </div>
      </div>
      
      {/* Article Content */}
      <div id="blog-post-article-container" className="container mx-auto px-4 py-8">
        <div id="blog-post-article-content" className="max-w-3xl mx-auto">
          {/* Author and Meta */}
          <div id="blog-post-author-meta-section" className="flex items-center mb-8 border-b border-gray-200 pb-6">
            <div id="blog-post-author-image-container" className="w-12 h-12 relative rounded-full overflow-hidden mr-4">
              <Image 
                src={post.authorImage}
                alt={post.author}
                fill
                sizes="48px"
                className="object-cover"
              />
            </div>
            <div id="blog-post-author-info-container" className="flex-1">
              <p className="font-medium text-gray-900">{post.author}</p>
              <div id="blog-post-meta-info-container" className="flex items-center text-sm text-gray-500 mt-1 flex-wrap">
                <div id="blog-post-publish-date-container" className="flex items-center mr-4">
                  <CalendarDays size={14} className="mr-1" />
                  <span>{translations.published_on} {post.date[language]}</span>
                </div>
                <div id="blog-post-read-time-container" className="flex items-center">
                  <Clock size={14} className="mr-1" />
                  <span>{post.readTime[language]}</span>
                </div>
              </div>
            </div>
            <div id="blog-post-language-share-container" className="flex items-center">
              <div id="blog-post-language-indicator" className="mr-4 px-2 py-1 bg-blue-100 rounded text-xs font-medium text-blue-800">
                {language}
              </div>
              <button 
                className="flex items-center text-sm text-gray-600 hover:text-blue-600"
                aria-label="Share this post"
              >
                <Share2 size={18} className="mr-1" />
                <span className="hidden sm:inline">{translations.share_post}</span>
              </button>
            </div>
          </div>
          
          {/* Article Body */}
          <div 
            id="blog-post-article-body"
            className="prose prose-lg max-w-none mb-12 font-dosis text-justify tracking-wide"
            style={{ lineHeight: '30px' }}
            dangerouslySetInnerHTML={{ 
              __html: post.localized[language].content
                .replace(/<p>/g, '<p class="font-dosis text-justify tracking-wide" style="line-height: 30px;">')
                .replace(/<ol>/g, '<ol class="font-dosis text-justify tracking-wide" style="line-height: 30px;">')
                .replace(/<li>/g, '<li class="font-dosis text-justify tracking-wide" style="line-height: 30px;">')
            }}
          />
          
          {/* Post Navigation */}
          <div id="blog-post-navigation-container" className="border-t border-b border-gray-200 py-6 my-10 grid grid-cols-2 gap-4">
            <div id="blog-post-previous-navigation">
              <Link href={`/blog/${relatedPosts[0]?.slug || ''}`} className={`flex items-center ${!relatedPosts[0] ? 'pointer-events-none opacity-50' : 'hover:text-blue-600'}`}>
                <ChevronLeft size={20} className="mr-2" />
                <div id="blog-post-previous-post-info">
                  <p className="text-xs text-gray-500 mb-1">{translations.previous_post}</p>
                  <p className="font-medium line-clamp-1">{relatedPosts[0]?.localized[language].title || ''}</p>
                </div>
              </Link>
            </div>
            <div id="blog-post-next-navigation" className="text-right">
              <Link href={`/blog/${relatedPosts[1]?.slug || ''}`} className={`flex items-center justify-end ${!relatedPosts[1] ? 'pointer-events-none opacity-50' : 'hover:text-blue-600'}`}>
                <div id="blog-post-next-post-info">
                  <p className="text-xs text-gray-500 mb-1">{translations.next_post}</p>
                  <p className="font-medium line-clamp-1">{relatedPosts[1]?.localized[language].title || ''}</p>
                </div>
                <ChevronRight size={20} className="ml-2" />
              </Link>
            </div>
          </div>
          
          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <div id="blog-post-related-posts-section" className="my-12">
              <h3 className="text-2xl font-bold mb-6 font-serif">{translations.related_posts}</h3>
              <div id="blog-post-related-posts-grid" className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedPosts.map(relatedPost => (
                  <Link 
                    key={relatedPost.id} 
                    href={`/blog/${relatedPost.slug}`}
                    className="group bg-[#f8f8f8] rounded-lg overflow-hidden hover:shadow-lg transition-shadow p-3"
                  >
                    <div id={`blog-post-related-image-${relatedPost.id}`} className="relative aspect-[3/2] mb-3 overflow-hidden rounded-md">
                      <Image 
                        src={relatedPost.image}
                        alt={relatedPost.localized[language].title}
                        fill
                        sizes="(max-width: 768px) 100vw, 33vw"
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <h4 className="font-medium group-hover:text-blue-600 line-clamp-2">
                      {relatedPost.localized[language].title}
                    </h4>
                  </Link>
                ))}
              </div>
            </div>
          )}
          
          {/* Comment Section */}
          <div id="blog-post-comments-section" className="mt-16">
            <h3 className="text-2xl font-bold mb-6 font-serif">{translations.comments}</h3>
            
            {/* Leave a Comment Form */}
            <div id="blog-post-comment-form-container" className="bg-[#f8f8f8] p-6 rounded-md">
              <h4 className="font-medium text-lg mb-4">{translations.leave_comment}</h4>
              <textarea 
                className="w-full p-3 border border-gray-300 rounded-md mb-4 min-h-[120px] focus:outline-none focus:ring-2 focus:ring-[#F0C35F]"
                placeholder={translations.your_comment}
              ></textarea>
              <button className="px-6 py-3 bg-gradient-to-b from-[#51575F] to-[#1F2937] border border-[#D1C29B] rounded-full hover:bg-gradient-to-tl hover:from-[#6B7280] hover:to-[#374151] active:bg-gradient-to-tl active:from-[#4B5563] active:to-[#111827] active:scale-95 transition-all shadow-md font-medium text-white">
                {translations.post_comment}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}