'use client';

const ProductDetailSkeleton = () => {
  return (
    <div className="min-h-screen bg-white pb-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#f8f8f8' }}>
      <div className="max-w-7xl mx-auto">
        {/* Back button skeleton */}
        <div className="h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"></div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Video Section Skeleton */}
          <div>
            {/* Main video skeleton */}
            <div className="relative rounded-lg overflow-hidden mb-4 aspect-[4/3] bg-gray-200 animate-pulse">
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-16 h-16 bg-gray-300 rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* Video Thumbnails Gallery Skeleton */}
            <div className="flex space-x-2 mt-4 overflow-x-auto pb-2">
              <div className="w-20 h-20 rounded border-2 border-transparent bg-gray-200 flex-shrink-0 animate-pulse"></div>
            </div>
          </div>

          {/* Product Details Section Skeleton */}
          <div>
            {/* Product name skeleton */}
            <div className="h-9 bg-gray-200 rounded mb-4 w-3/4 animate-pulse"></div>
            
            {/* Weight skeleton */}
            <div className="h-6 bg-gray-200 rounded mb-4 w-1/2 animate-pulse"></div>
            
            {/* Price skeleton */}
            <div className="h-8 bg-gray-200 rounded mb-6 w-1/3 animate-pulse"></div>

            {/* Category section skeleton */}
            <div className="mb-6">
              <div className="h-6 bg-gray-200 rounded mb-2 w-1/4 animate-pulse"></div>
              <div className="h-5 bg-gray-200 rounded w-1/3 animate-pulse"></div>
            </div>

            {/* Quantity section skeleton */}
            <div className="mb-8">
              <div className="h-6 bg-gray-200 rounded mb-2 w-1/4 animate-pulse"></div>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 rounded-l animate-pulse"></div>
                <div className="w-16 h-10 bg-gray-200 animate-pulse"></div>
                <div className="w-10 h-10 bg-gray-200 rounded-r animate-pulse"></div>
              </div>
            </div>

            {/* Add to cart button skeleton */}
            <div className="w-full mb-3 h-12 bg-gray-200 rounded-md animate-pulse"></div>
            
            {/* Buy now button skeleton */}
            <div className="w-full h-12 bg-gray-200 rounded-md animate-pulse mb-6"></div>

            {/* Description section skeleton */}
            <div className="mt-6">
              <div className="h-6 bg-gray-200 rounded mb-2 w-1/3 animate-pulse"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-4/5 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailSkeleton; 