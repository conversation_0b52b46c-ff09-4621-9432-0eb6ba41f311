import { NextResponse } from 'next/server';
import { S3Client, ListObjectsV2Command, PutObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Image bucket name from environment variables
const imagesBucketName = process.env.S3_IMAGES_BUCKET || 'imagesbucket2025';

/**
 * Upload a test image to S3 bucket
 * 
 * POST /api/test-s3-image
 */
export async function POST() {
  try {
    const client = getS3Client();
    
    // Generate a test text file to simulate an image upload
    const testImageContent = Buffer.from(`Test image content created at ${new Date().toISOString()}`);
    const testImageKey = `test-image-${Date.now()}.txt`;
    
    // Upload the test file to S3
    const uploadCommand = new PutObjectCommand({
      Bucket: imagesBucketName,
      Key: testImageKey,
      Body: testImageContent,
      ContentType: 'text/plain'
    });
    
    await client.send(uploadCommand);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Successfully uploaded test file to S3 image bucket: ${imagesBucketName}`,
      bucketName: imagesBucketName,
      fileKey: testImageKey
    });
  } catch (error: any) {
    console.error('Error uploading to S3 image bucket:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to upload to S3 image bucket',
      code: error.Code || error.code,
      bucketName: imagesBucketName
    }, { status: 500 });
  }
}

/**
 * Retrieve test S3 image records (list images in the bucket)
 * 
 * GET /api/test-s3-image
 */
export async function GET() {
  try {
    const client = getS3Client();
    
    // List objects in the bucket
    const command = new ListObjectsV2Command({ 
      Bucket: imagesBucketName,
      MaxKeys: 10 // Limit to 10 objects
    });
    
    const response = await client.send(command);
    
    // Extract object keys (filenames)
    const objectKeys = response.Contents?.map(obj => obj.Key || '') || [];
    
    // Return success response
    return NextResponse.json({
      success: true,
      bucketName: imagesBucketName,
      count: response.KeyCount || 0,
      files: objectKeys
    });
  } catch (error: any) {
    console.error('Error listing S3 image bucket contents:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to list S3 image bucket contents',
      code: error.Code || error.code,
      bucketName: imagesBucketName
    }, { status: 500 });
  }
} 