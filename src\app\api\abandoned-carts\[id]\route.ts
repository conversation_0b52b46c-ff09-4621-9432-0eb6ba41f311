import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import AbandonedCart from '@/models/AbandonedCart';

/**
 * Delete an abandoned cart by ID
 * 
 * DELETE /api/abandoned-carts/[id]
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Get cart ID from route parameter
    const cartId = params.id;
    
    if (!cartId) {
      return NextResponse.json({
        success: false,
        error: 'Cart ID is required'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Delete the cart
    const result = await AbandonedCart.findByIdAndDelete(cartId);
    
    if (!result) {
      return NextResponse.json({
        success: false,
        error: 'Abandoned cart not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Abandoned cart deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting abandoned cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete abandoned cart'
    }, { status: 500 });
  }
} 