<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero with Floating Coins Animation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
        }
        #canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        .content-overlay {
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            color: #333;
            padding: 2rem;
            text-align: center;
        }
        .header {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0 0 1rem 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header .logo {
            font-weight: bold;
            font-size: 1.5rem;
            color: #6d28d9;
        }
        .header nav a {
            margin-left: 1.5rem;
            color: #555;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .header nav a:hover {
            color: #6d28d9;
        }
        .header .cta-button {
            background-color: #8b5cf6;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 9999px;
            text-decoration: none;
            transition: background-color 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header .cta-button:hover {
            background-color: #7c3aed;
        }
        .main-content {
            max-width: 600px;
            margin-top: 8rem;
        }
        .main-content .subtitle {
            font-size: 0.9rem;
            color: #a855f7;
            letter-spacing: 0.1em;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .main-content h1 {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            color: #1f2937;
        }
        .main-content .learn-more-button {
            background-color: #a78bfa;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 9999px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            display: inline-block;
        }
        .main-content .learn-more-button:hover {
            background-color: #8b5cf6;
        }
        @media (min-width: 640px) {
            .main-content h1 {
                font-size: 3rem;
            }
        }
        @media (min-width: 768px) {
            .main-content h1 {
                font-size: 3.5rem;
            }
            .main-content {
                margin-top: 10rem;
            }
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="bg-canvas"></canvas>
    </div>

    <header class="header">
        <div class="logo">&lt; Exito</div>
        <nav class="hidden md:block">
            <a href="#">Home</a>
            <a href="#">Services</a>
            <a href="#">About</a>
            <a href="#">Industries</a>
            <a href="#">Contact</a>
        </nav>
        <a href="#" class="cta-button">Sign Up</a>
    </header>

    <div class="content-overlay">
        <div class="main-content">
            <p class="subtitle">DISCOVER REVOLUTIONARY TOOLS</p>
            <h1>FX trading boosted with AI and pro experience</h1>
            <a href="#" class="learn-more-button">Learn More</a>
        </div>
    </div>

    <script>
        // Ensure Three.js is loaded before proceeding
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof THREE === 'undefined') {
                console.error("THREE.js failed to load.");
                // Try to load Three.js dynamically
                const script = document.createElement('script');
                script.src = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js";
                script.onload = initThreeJS;
                document.head.appendChild(script);
            } else {
                initThreeJS();
            }
        });

        function initThreeJS() {
            console.log("Setting up Three.js scene");
            // --- Three.js Setup ---

            // Create a new scene
            const scene = new THREE.Scene();

            // Create a perspective camera
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 10; // Position camera further back to see coins better

            // Create a WebGL renderer and attach it to the canvas
            const canvas = document.getElementById('bg-canvas');
            const renderer = new THREE.WebGLRenderer({
                canvas: canvas,
                alpha: true, // Enable transparency for the canvas background
                antialias: true // Enable antialiasing for smoother edges
            });
            // Set the renderer size to match the window dimensions
            renderer.setSize(window.innerWidth, window.innerHeight);
            // Set the pixel ratio for high-DPI screens
            renderer.setPixelRatio(window.devicePixelRatio);
            // Explicitly set clear color with alpha=0 for transparency
            renderer.setClearColor(0x000000, 0);

            // --- Lighting Setup ---

            // Add ambient light (lights all objects equally)
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // Increased intensity
            scene.add(ambientLight);
            // Add a directional light (like sunlight)
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0); // Increased intensity
            // Position the directional light
            directionalLight.position.set(5, 10, 7.5);
            scene.add(directionalLight);

            // Add a point light for better highlights
            const pointLight = new THREE.PointLight(0xa78bfa, 2, 50);
            pointLight.position.set(0, 0, 15);
            scene.add(pointLight);

            // --- Coin Creation ---

            // Define the geometry for the coins (a cylinder)
            const coinGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.1, 32);

            // Define the material for the coins with improved visibility
            const coinMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xffc0cb, // Base color (pinkish)
                metalness: 0.3,   // Slightly more metallic
                roughness: 0.1,   // Smooth surface
                transparent: true, // Enable transparency
                opacity: 0.8, // Increased opacity for better visibility
                emissive: 0x8a2be2, // Blue-violet emissive color for the glow
                emissiveIntensity: 3.0, // Increased emissive intensity for stronger glow
                side: THREE.DoubleSide // Render both sides
            });

            // Array to hold all the coin meshes
            const coins = [];
            const numCoins = 40; // More coins
            const spread = 20; // Wider spread

            // Create and position multiple coins
            for (let i = 0; i < numCoins; i++) {
                // Create a mesh (geometry + material) for each coin
                const coin = new THREE.Mesh(coinGeometry, coinMaterial);

                // Set random initial position within the defined spread
                // Keep z position between -5 and 5 to ensure visibility
                coin.position.set(
                    (Math.random() - 0.5) * spread, // X position
                    (Math.random() - 0.5) * spread, // Y position
                    (Math.random() * 10) - 5 // Z position between -5 and 5
                );

                // Set random initial rotation
                coin.rotation.set(
                    Math.random() * Math.PI * 2, // Rotate around X axis
                    Math.random() * Math.PI * 2, // Rotate around Y axis
                    Math.random() * Math.PI * 2 // Rotate around Z axis
                );

                // Store unique animation properties for each coin in userData
                coin.userData = {
                    rotationSpeedX: (Math.random() - 0.5) * 0.03, // Slightly faster rotation
                    rotationSpeedY: (Math.random() - 0.5) * 0.03, 
                    floatSpeed: 0.01 + Math.random() * 0.01, // Faster float speed
                    floatOffset: Math.random() * Math.PI * 2 // Varying start point in float cycle
                };

                // Add the coin to the scene and the coins array
                scene.add(coin);
                coins.push(coin);
            }

            // --- Animation Loop ---

            // Clock to track time for smooth animation
            const clock = new THREE.Clock();

            // The main animation loop function
            function animate() {
                // Request the next frame from the browser
                requestAnimationFrame(animate);

                const elapsedTime = clock.getElapsedTime();

                // Animate each coin
                coins.forEach(coin => {
                    // Apply rotation based on stored speeds
                    coin.rotation.x += coin.userData.rotationSpeedX;
                    coin.rotation.y += coin.userData.rotationSpeedY;

                    // Apply floating movement using a sine wave for smooth oscillation
                    coin.position.y += Math.sin(elapsedTime * coin.userData.floatSpeed + coin.userData.floatOffset) * 0.02;
                    
                    // Add subtle horizontal movement
                    coin.position.x += Math.sin(elapsedTime * 0.2 + coin.userData.floatOffset) * 0.003;
                });

                // Render the scene with the camera
                renderer.render(scene, camera);
            }

            // --- Event Handlers ---

            // Handle window resizing to maintain correct aspect ratio and size
            function onWindowResize() {
                // Update camera aspect ratio
                camera.aspect = window.innerWidth / window.innerHeight;
                // Update the camera's projection matrix
                camera.updateProjectionMatrix();

                // Update renderer size
                renderer.setSize(window.innerWidth, window.innerHeight);
                // Update pixel ratio on resize
                renderer.setPixelRatio(window.devicePixelRatio);
                
                // Force a render after resize
                renderer.render(scene, camera);
            }

            // Add event listener for window resize
            window.addEventListener('resize', onWindowResize, false);

            // Start the animation loop immediately
            animate();
            // Call resize handler initially to set correct size
            onWindowResize();
            
            console.log("Three.js animation started successfully");
        }
    </script>
</body>
</html> 