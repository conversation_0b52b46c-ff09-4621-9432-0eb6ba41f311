import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { SupportTicket } from '@/models/SupportTicket';

/**
 * GET /api/support-tickets
 * Retrieve all support tickets with optional filtering
 */
export async function GET(request: Request) {
  try {
    // Connect to database
    await dbConnect();
    
    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const category = searchParams.get('category');
    const customerEmail = searchParams.get('email');
    
    // Build query object
    const query: any = {};
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (category) query.category = category;
    if (customerEmail) query['customer.email'] = customerEmail.toLowerCase();
    
    // Fetch tickets with filters, newest first
    const tickets = await SupportTicket.find(query)
      .sort({ createdAt: -1 })
      .lean();
    
    return NextResponse.json(tickets);
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch support tickets' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/support-tickets
 * Create a new support ticket
 */
export async function POST(request: Request) {
  try {
    // Connect to database
    await dbConnect();
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['customer', 'description', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }
    
    // Validate customer fields
    if (!body.customer.email || !body.customer.name) {
      return NextResponse.json(
        { error: 'Customer email and name are required' },
        { status: 400 }
      );
    }
    
    // Generate ticket number
    const count = await SupportTicket.countDocuments();
    const date = new Date();
    const year = date.getFullYear().toString().substr(2, 2); // Get last 2 digits of year
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const ticketNumber = `TICKET-${year}${month}-${String(count + 1).padStart(4, '0')}`;
    
    // Create new ticket with initial message from customer
    const newTicket = new SupportTicket({
      ...body,
      ticketNumber,
      messages: [
        {
          sender: 'customer',
          message: body.description,
          timestamp: new Date()
        }
      ]
    });
    
    // Save the ticket
    await newTicket.save();
    
    return NextResponse.json(newTicket, { status: 201 });
  } catch (error) {
    console.error('Error creating support ticket:', error);
    return NextResponse.json(
      { error: 'Failed to create support ticket' },
      { status: 500 }
    );
  }
} 