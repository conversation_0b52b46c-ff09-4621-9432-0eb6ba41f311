'use client';

import { useEffect, useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface Subscriber {
  _id: string;
  name: string;
  email: string;
  createdAt: string;
}

export default function SubscribeEmailListPage() {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubscribers = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/subscribe');
        if (!response.ok) {
          throw new Error(`Failed to fetch subscribers: ${response.statusText}`);
        }
        const data = await response.json();
        setSubscribers(data.subscribers || []);
      } catch (err: any) {
        console.error('Error fetching subscribers:', err);
        setError(err.message || 'An unexpected error occurred.');
      }
      setLoading(false);
    };

    fetchSubscribers();
  }, []);

  if (loading) {
    return <div className="container mx-auto p-4 text-center">Loading subscribers...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-4 text-center text-red-500">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl">Subscribed Email List ({subscribers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {subscribers.length === 0 ? (
            <p className="text-gray-600">No subscribers yet.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px] sm:w-[250px]">Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead className="text-right w-[150px] sm:w-[200px]">Subscribed At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {subscribers.map((subscriber) => (
                  <TableRow key={subscriber._id}>
                    <TableCell className="font-medium">{subscriber.name}</TableCell>
                    <TableCell>{subscriber.email}</TableCell>
                    <TableCell className="text-right">
                      {new Date(subscriber.createdAt).toLocaleDateString()} {new Date(subscriber.createdAt).toLocaleTimeString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 