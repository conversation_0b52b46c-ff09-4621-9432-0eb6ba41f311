import mongoose, { Schema } from 'mongoose';

// Define payment gateway schema
const PaymentGatewaySchema = new Schema(
  {
    name: { 
      type: String, 
      required: [true, 'Gateway name is required'],
      trim: true
    },
    provider: { 
      type: String, 
      required: [true, 'Provider name is required'],
      trim: true
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    testMode: { 
      type: Boolean, 
      default: true
    },
    supportedCurrencies: {
      type: [String],
      default: ['USD']
    },
    // Configuration fields
    apiKey: {
      type: String,
      default: ''
    },
    secretKey: {
      type: String,
      default: ''
    },
    merchantId: {
      type: String,
      default: ''
    },
    webhookUrl: {
      type: String,
      default: ''
    },
    // Additional settings
    minimumAmount: {
      type: Number,
      default: 0
    },
    processingFee: {
      type: Number,
      default: 0
    },
    processingFeeType: {
      type: String,
      enum: ['flat', 'percentage'],
      default: 'percentage'
    },
    notes: {
      type: String,
      default: ''
    }
  },
  { timestamps: true }
);

// Check if model exists before creating to prevent model overwrite during hot reloading
export const PaymentGateway = mongoose.models.PaymentGateway || mongoose.model('PaymentGateway', PaymentGatewaySchema); 