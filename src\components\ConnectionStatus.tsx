'use client';

import { useState, useEffect } from 'react';

/**
 * Type definition for the connection status response from the API
 */
type ConnectionStatusResponse = {
  status: 'connected' | 'error';
  error: {
    message: string;
    code?: string | number;
    name?: string;
    explanation: string;
  } | null;
  dbInfo?: {
    name: string;
    collections: any[];
    host: string;
    port: string | number;
    readyState: string;
  } | null;
};

/**
 * Function to provide simple explanations for technical error details
 */
function getSimpleErrorExplanation(error: any): {[key: string]: string} {
  const explanations: {[key: string]: string} = {};
  
  // Simple explanation for error message
  if (error.message) {
    if (error.message.includes('ENOTFOUND')) {
      explanations.message = "The database server couldn't be found. This usually means the server name in your connection string is incorrect.";
    } else if (error.message.includes('Authentication failed')) {
      explanations.message = "Your username or password is incorrect.";
    } else if (error.message.includes('timed out')) {
      explanations.message = "It took too long to connect to the database. This could be due to slow internet or server issues.";
    } else if (error.message.includes('ECONNREFUSED')) {
      explanations.message = "The database server refused the connection. The server might be down or not accepting connections.";
    } else {
      explanations.message = "There was a problem connecting to the database.";
    }
  }
  
  // Simple explanation for error code
  if (error.code) {
    if (error.code === 'ENOTFOUND') {
      explanations.code = "Server not found - the database address is incorrect or doesn't exist.";
    } else if (error.code === 'ETIMEDOUT') {
      explanations.code = "Connection timeout - it took too long to connect to the database.";
    } else if (error.code === 'ECONNREFUSED') {
      explanations.code = "Connection refused - the database server is not accepting connections.";
    } else if (error.code === 'ECONNRESET') {
      explanations.code = "Connection reset - the connection was unexpectedly closed by the server.";
    } else if (error.code === 18) {
      explanations.code = "Authentication error - your username or password is incorrect.";
    } else if (error.code === 8000) {
      explanations.code = "Wrong database name - the database name in your connection string is incorrect.";
    } else {
      explanations.code = `Error code ${error.code} - this is a technical error code from MongoDB.`;
    }
  }
  
  // Simple explanation for error name
  if (error.name) {
    if (error.name === 'MongoServerError') {
      explanations.name = "This is a problem with the MongoDB server itself.";
    } else if (error.name === 'MongoNetworkError') {
      explanations.name = "This is a network problem connecting to the database. Check your internet connection.";
    } else if (error.name === 'MongoParseError') {
      explanations.name = "There's a problem with the format of your MongoDB connection string.";
    } else if (error.name === 'MongooseError') {
      explanations.name = "This is a general problem with the database connection.";
    } else if (error.name === 'Error') {
      explanations.name = "This is a general error connecting to the database.";
    } else {
      explanations.name = `This is a ${error.name} error - a technical error type.`;
    }
  }
  
  return explanations;
}

/**
 * ConnectionStatus component displays the current MongoDB connection status
 * It makes an API call to check the connection and displays appropriate UI based on the result
 */
export default function ConnectionStatus() {
  // State to store connection status information
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatusResponse | null>(null);
  // State to track if we're currently checking the connection
  const [loading, setLoading] = useState<boolean>(true);
  // State to store database information
  const [databaseInfo, setDatabaseInfo] = useState<ConnectionStatusResponse | null>(null);
  // State to track if we're currently fetching database info
  const [loadingDbInfo, setLoadingDbInfo] = useState<boolean>(false);

  // Effect to check database connection when component mounts
  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Call our API endpoint that checks MongoDB connection
        const response = await fetch('/api/check-connection');
        
        // Parse the JSON response
        const data = await response.json();
        
        // Update state with connection status
        setConnectionStatus(data);
        
        // If connected, fetch database information
        if (data.status === 'connected') {
          fetchDatabaseInfo();
        }
      } catch (error) {
        // Handle client-side fetch errors
        setConnectionStatus({
          status: 'error',
          error: {
            message: 'Failed to check database connection',
            explanation: 'There was a problem connecting to the server to check the database status.'
          }
        });
      } finally {
        // Set loading to false once we're done
        setLoading(false);
      }
    };

    // Call the function to check connection
    checkConnection();
  }, []); // Empty dependency array means this effect runs once when component mounts

  // Function to fetch database information
  const fetchDatabaseInfo = async () => {
    setLoadingDbInfo(true);
    try {
      const response = await fetch('/api/database-info');
      const data = await response.json();
      setDatabaseInfo(data);
    } catch (error) {
      setDatabaseInfo({
        status: 'error',
        error: {
          message: 'Failed to fetch database information',
          explanation: 'There was a problem retrieving the database details.'
        }
      });
    } finally {
      setLoadingDbInfo(false);
    }
  };

  // If we're still loading, show a loading indicator
  if (loading) {
    return (
      <div className="bg-gray-100 p-4 rounded-md shadow-sm">
        <div className="flex items-center">
          <div className="animate-spin h-5 w-5 mr-3 border-2 border-gray-500 border-t-transparent rounded-full"></div>
          <p>Checking database connection...</p>
        </div>
      </div>
    );
  }

  // If connection is successful, show success message and database info
  if (connectionStatus?.status === 'connected') {
    return (
      <div>
        {/* Connection success message */}
        <div className="bg-green-100 p-4 rounded-md shadow-sm border border-green-200 mb-4">
          <div className="flex items-center">
            <svg 
              className="h-5 w-5 text-green-500 mr-3" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
            <p className="text-green-700 font-medium">Successfully connected to MongoDB Atlas</p>
          </div>
        </div>
        
        {/* Database information section */}
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-200">
          <h3 className="font-medium text-lg mb-3">Database Information:</h3>
          
          {loadingDbInfo ? (
            <div className="flex items-center text-gray-500">
              <div className="animate-spin h-4 w-4 mr-2 border-2 border-gray-500 border-t-transparent rounded-full"></div>
              <p>Loading database details...</p>
            </div>
          ) : databaseInfo?.status === 'connected' && databaseInfo.dbInfo ? (
            <div className="space-y-2">
              <p><span className="font-medium">Database Name:</span> {databaseInfo.dbInfo.name}</p>
              <p><span className="font-medium">Host:</span> {databaseInfo.dbInfo.host}</p>
              <p><span className="font-medium">Port:</span> {databaseInfo.dbInfo.port}</p>
              <p><span className="font-medium">Connection Status:</span> {databaseInfo.dbInfo.readyState}</p>
              
              {/* Collections information */}
              <div className="mt-3">
                <p className="font-medium mb-1">Collections ({databaseInfo.dbInfo.collections.length}):</p>
                {databaseInfo.dbInfo.collections.length > 0 ? (
                  <ul className="list-disc pl-5 text-sm text-gray-700">
                    {databaseInfo.dbInfo.collections.map((collection: any, index: number) => (
                      <li key={index}>{collection.name}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500 italic">No collections found in this database.</p>
                )}
              </div>
            </div>
          ) : (
            <div className="text-red-600">
              <p>Failed to load database information.</p>
              {databaseInfo?.error && (
                <p className="text-sm mt-1 italic">{databaseInfo.error.explanation}</p>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Get simple explanations for all technical details
  const simpleExplanations = connectionStatus?.error 
    ? getSimpleErrorExplanation(connectionStatus.error) 
    : {};

  // If connection failed, show error message with details
  return (
    <div className="bg-red-100 p-4 rounded-md shadow-sm border border-red-200">
      <div className="flex items-start">
        <svg 
          className="h-5 w-5 text-red-500 mr-3 mt-0.5" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
          />
        </svg>
        <div>
          <p className="text-red-700 font-medium">Failed to connect to MongoDB Atlas</p>
          
          {/* Display detailed error information */}
          {connectionStatus?.error && (
            <div className="mt-2 text-sm">
              <p className="font-medium text-red-800">Error Details:</p>
              <p className="mt-1">{connectionStatus.error.explanation}</p>
              
              {/* Technical details with simple explanations */}
              <div className="mt-4 border border-red-200 rounded-md p-4 bg-white">
                <h3 className="font-medium text-lg mb-3">What This Means:</h3>
                
                {/* Error message with simple explanation */}
                <div className="mb-4">
                  <h4 className="font-medium text-red-700">The Error:</h4>
                  <p className="text-gray-800">{connectionStatus.error.message}</p>
                  {simpleExplanations.message && (
                    <p className="mt-1 text-gray-600 italic">{simpleExplanations.message}</p>
                  )}
                </div>
                
                {/* Error code with simple explanation */}
                {connectionStatus.error.code && (
                  <div className="mb-4">
                    <h4 className="font-medium text-red-700">Error Code: {connectionStatus.error.code}</h4>
                    {simpleExplanations.code && (
                      <p className="mt-1 text-gray-600 italic">{simpleExplanations.code}</p>
                    )}
                  </div>
                )}
                
                {/* Error name with simple explanation */}
                {connectionStatus.error.name && (
                  <div className="mb-4">
                    <h4 className="font-medium text-red-700">Error Type: {connectionStatus.error.name}</h4>
                    {simpleExplanations.name && (
                      <p className="mt-1 text-gray-600 italic">{simpleExplanations.name}</p>
                    )}
                  </div>
                )}
                
                {/* How to fix section */}
                <div className="mt-4 pt-3 border-t border-red-200">
                  <h4 className="font-medium text-red-700">How to Fix This:</h4>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                    <li>Check that your MongoDB connection string in the .env.local file is correct</li>
                    <li>Make sure you've replaced the placeholders with your actual MongoDB Atlas credentials</li>
                    <li>Verify that your MongoDB Atlas cluster is running and accessible</li>
                    <li>Check if there are network restrictions preventing the connection</li>
                    <li>Ensure your MongoDB Atlas IP whitelist allows connections from your current IP</li>
                  </ul>
                </div>
              </div>
              
              {/* Technical details in a collapsible section for developers */}
              <details className="mt-2 border border-red-300 rounded-md p-2 bg-red-50">
                <summary className="cursor-pointer text-red-600">Technical Details (for developers)</summary>
                <div className="mt-2 pl-2 border-l-2 border-red-300">
                  <p><span className="font-medium">Message:</span> {connectionStatus.error.message}</p>
                  {connectionStatus.error.code && (
                    <p><span className="font-medium">Error Code:</span> {connectionStatus.error.code}</p>
                  )}
                  {connectionStatus.error.name && (
                    <p><span className="font-medium">Error Type:</span> {connectionStatus.error.name}</p>
                  )}
                </div>
              </details>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 