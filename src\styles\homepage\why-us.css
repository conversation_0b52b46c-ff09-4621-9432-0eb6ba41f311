.why-us-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
    padding-left: 0;
    padding-right: 0;
    position: relative;
}

@media (min-width: 640px) {
    .why-us-section {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}

@media (min-width: 768px) {
    .why-us-section {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

.why-us-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .why-us-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .why-us-container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.why-us-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 768px) {
    .why-us-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .why-us-grid {
        gap: 2.5rem;
    }
}

.why-us-content {
    order: 2;
}

@media (min-width: 768px) {
    .why-us-content {
        order: 1;
    }
}

.why-us-heading {
    display: none;
    font-size: 2.25rem;
    font-weight: 500;
    color: black;
    text-transform: capitalize;
    font-family: var(--font-dosis), sans-serif;
    margin-bottom: 2rem;
    text-align: left;
}

@media (min-width: 768px) {
    .why-us-heading {
        display: block;
    }
}

@media (min-width: 1024px) {
    .why-us-heading {
        font-size: 3rem;
        margin-bottom: 2.5rem;
    }
}

.why-us-description {
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 28.8px;
    letter-spacing: 1px;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: justify;
}

@media (min-width: 640px) {
    .why-us-description {
        margin-bottom: 2rem;
    }
}

@media (min-width: 768px) {
    .why-us-description {
    }
}

.why-us-button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    background: linear-gradient(to top left, #51575F, #1F2937);
    color: white;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: capitalize;
    font-family: var(--font-segoe-ui), sans-serif;
    transition: all 0.3s;
}

.why-us-button:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to top left, #6B7280, #374151);
}

.why-us-button:active {
    transform: scale(0.95);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    background: linear-gradient(to top left, #4B5563, #111827);
}

.why-us-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(163, 0, 63, 0.5);
}

@media (min-width: 768px) {
    .why-us-button {
        font-size: 1rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.why-us-image-container {
    order: 1;
}

@media (min-width: 768px) {
    .why-us-image-container {
        order: 2;
    }
}

.why-us-mobile-heading-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

@media (min-width: 640px) {
    .why-us-mobile-heading-grid {
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }
}

@media (min-width: 768px) {
    .why-us-mobile-heading-grid {
        margin-bottom: 0;
    }
}

.why-us-mobile-heading-container {
    display: flex;
    align-items: center;
    justify-content: start;
}

@media (min-width: 768px) {
    .why-us-mobile-heading-container {
        display: none;
    }
}

.why-us-mobile-heading {
    font-family: var(--font-dosis), sans-serif;
    font-size: 25px;
    font-weight: 500;
    line-height: 36px;
    letter-spacing: 1.25px;
    color: rgb(0, 0, 0);
    text-align: center;
    margin-bottom: 0.75rem;
    text-transform: capitalize;
}

@media (min-width: 640px) {
    .why-us-mobile-heading {
        font-size: 1.875rem;
    }
}

.why-us-image-wrapper {
    position: relative;
    border-radius: 0.75rem;
    overflow: hidden;
    aspect-ratio: 4/3;
    width: 100%;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s;
}

.why-us-image-wrapper:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (min-width: 640px) {
    .why-us-image-wrapper {
        aspect-ratio: 16/9;
    }
}

@media (min-width: 768px) {
    .why-us-image-wrapper {
        aspect-ratio: 3/2;
    }
}

@media (min-width: 1024px) {
    .why-us-image-wrapper {
        aspect-ratio: 16/9;
    }
}

.why-us-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.why-us-desktop-image-grid {
    display: none;
    margin-top: 4rem;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .why-us-desktop-image-grid {
        display: grid;
    }
}

@media (min-width: 1024px) {
    .why-us-desktop-image-grid {
        margin-top: 5rem;
        gap: 2rem;
    }
}

.why-us-category-link {
    display: flex;
    flex-direction: column;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: transform 0.3s;
    cursor: pointer;
}

.why-us-category-link:hover {
    transform: translateY(-2px);
}

.why-us-category-link:hover .why-us-category-image {
    transform: scale(1.05);
}

.why-us-category-image-container {
    position: relative;
    aspect-ratio: 1/1;
    overflow: hidden;
}

.why-us-category-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.why-us-category-label {
    background-color: #f8f8f8;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
}

.why-us-category-text {
    font-family: var(--font-dosis), sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: black;
    text-transform: capitalize;
    letter-spacing: 1px;
}

.why-us-category-text:hover {
    text-decoration: underline;
}

.why-us-mobile-image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    margin-top: 2.5rem;
}

@media (min-width: 638px) {
    .why-us-mobile-image-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        margin-top: 3rem;
    }
}

@media (min-width: 768px) {
    .why-us-mobile-image-grid {
        display: none;
    }
}

.why-us-mobile-category-link {
    display: flex;
    flex-direction: column;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: transform 0.3s;
    cursor: pointer;
}

.why-us-mobile-category-link:hover {
    transform: translateY(-2px);
}

.why-us-mobile-category-link:hover .why-us-mobile-category-image {
    transform: scale(1.05);
}

.why-us-mobile-category-image-container {
    position: relative;
    aspect-ratio: 1/1;
    overflow: hidden;
}

.why-us-mobile-category-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.why-us-mobile-category-label {
    background-color: #f8f8f8;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
}

.why-us-mobile-category-text {
    font-family: var(--font-dosis), sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: black;
    text-transform: capitalize;
    letter-spacing: 1px;
}

@media (min-width: 640px) {
    .why-us-mobile-category-text {
        font-size: 16px;
        line-height: 24px;
    }
}

.why-us-mobile-category-text:hover {
    text-decoration: underline;
}

/* Mobile Layout - Only visible on mobile/tablet */
.why-us-mobile-layout {
    display: block;
}

@media (min-width: 768px) {
    .why-us-mobile-layout {
        display: none;
    }
}

/* Desktop Layout - Only visible on desktop */
.why-us-desktop-layout {
    display: none;
}

@media (min-width: 768px) {
    .why-us-desktop-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
    }
    
    .why-us-content {
        order: 1; /* Text content on left */
    }
    
    .why-us-desktop-image-container {
        order: 2; /* Image on right */
    }
}

@media (min-width: 1024px) {
    .why-us-desktop-layout {
        gap: 3rem;
    }
}

/* Mobile Description */
.why-us-mobile-description {
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 28.8px;
    letter-spacing: 1px;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: justify;
    margin-top: 1rem;
}

@media (min-width: 640px) {
    .why-us-mobile-description {
        margin-bottom: 2rem;
        margin-top: 1.5rem;
    }
}

/* Mobile Button */
.why-us-mobile-button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    background: linear-gradient(to top left, #51575F, #1F2937);
    color: white;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: capitalize;
    font-family: var(--font-segoe-ui), sans-serif;
    transition: all 0.3s;
}

.why-us-mobile-button:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to top left, #6B7280, #374151);
}

.why-us-mobile-button:active {
    transform: scale(0.95);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    background: linear-gradient(to top left, #4B5563, #111827);
}

.why-us-mobile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(163, 0, 63, 0.5);
}

/* Desktop Image Container */
.why-us-desktop-image-container {
    width: 100%;
    height: 100%;
}

.why-us-desktop-image-wrapper {
    position: relative;
    border-radius: 0.75rem;
    overflow: hidden;
    aspect-ratio: 4/3;
    width: 100%;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s;
}

.why-us-desktop-image-wrapper:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (min-width: 1024px) {
    .why-us-desktop-image-wrapper {
        aspect-ratio: 16/9;
    }
} 

/* Mobile: Show only first 2 category cards */
@media (max-width: 638px) {
  .why-us-category-link:nth-child(3),
  .why-us-mobile-category-link:nth-child(3) {
    display: none;
  }
}

/* Tablet and Desktop: Show all 3 category cards */
@media (min-width: 638px) {
  .why-us-category-link:nth-child(3),
  .why-us-mobile-category-link:nth-child(3) {
    display: block;
  }
} 