import { NextResponse } from 'next/server';
import { S3Client, ListObjectsV2Command, PutObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Video bucket name from environment variables
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * Upload a test video to S3 bucket
 * 
 * POST /api/test-s3-video
 */
export async function POST() {
  try {
    const client = getS3Client();
    
    // Generate a test text file to simulate a video upload
    const testVideoContent = Buffer.from(`Test video content created at ${new Date().toISOString()}\nThis is a simulated video file.`);
    const testVideoKey = `test-video-${Date.now()}.txt`;
    
    // Upload the test file to S3
    const uploadCommand = new PutObjectCommand({
      Bucket: videosBucketName,
      Key: testVideoKey,
      Body: testVideoContent,
      ContentType: 'text/plain'
    });
    
    await client.send(uploadCommand);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Successfully uploaded test file to S3 video bucket: ${videosBucketName}`,
      bucketName: videosBucketName,
      fileKey: testVideoKey
    });
  } catch (error: any) {
    console.error('Error uploading to S3 video bucket:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to upload to S3 video bucket',
      code: error.Code || error.code,
      bucketName: videosBucketName
    }, { status: 500 });
  }
}

/**
 * Retrieve test S3 video records (list videos in the bucket)
 * 
 * GET /api/test-s3-video
 */
export async function GET() {
  try {
    const client = getS3Client();
    
    // List objects in the bucket
    const command = new ListObjectsV2Command({ 
      Bucket: videosBucketName,
      MaxKeys: 10 // Limit to 10 objects
    });
    
    const response = await client.send(command);
    
    // Extract object keys (filenames)
    const objectKeys = response.Contents?.map(obj => obj.Key || '') || [];
    
    // Return success response
    return NextResponse.json({
      success: true,
      bucketName: videosBucketName,
      count: response.KeyCount || 0,
      files: objectKeys
    });
  } catch (error: any) {
    console.error('Error listing S3 video bucket contents:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to list S3 video bucket contents',
      code: error.Code || error.code,
      bucketName: videosBucketName
    }, { status: 500 });
  }
} 