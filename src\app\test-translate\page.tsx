'use client';

import React, { useState, useEffect } from 'react';
import { Languages, Send, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface TranslationResponse {
  success: boolean;
  translations?: {
    english: string;
    french: string;
    italian: string;
  };
  error?: string;
  message?: string;
}

interface ServiceStatus {
  success: boolean;
  status?: string;
  message?: string;
  error?: string;
  details?: string;
  rawError?: {
    name: string;
    message: string;
    code?: string;
    statusCode?: number;
  };
  testTranslation?: {
    original: string;
    translated: string;
    language: string;
  };
}

export default function TestTranslatePage() {
  const [englishText, setEnglishText] = useState('');
  const [frenchText, setFrenchText] = useState('');
  const [italianText, setItalianText] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [isCheckingService, setIsCheckingService] = useState(true);
  const [lastTranslation, setLastTranslation] = useState<TranslationResponse | null>(null);

  // Check service status on component mount
  useEffect(() => {
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    setIsCheckingService(true);
    try {
      const response = await fetch('/api/test-translate', {
        method: 'GET',
      });
      const data: ServiceStatus = await response.json();
      setServiceStatus(data);
    } catch (error) {
      setServiceStatus({
        success: false,
        status: 'error',
        error: 'Failed to check service status'
      });
    } finally {
      setIsCheckingService(false);
    }
  };

  const handleTranslate = async () => {
    if (!englishText.trim()) {
      alert('Please enter some English text to translate');
      return;
    }

    setIsTranslating(true);
    setFrenchText('');
    setItalianText('');

    try {
      const response = await fetch('/api/test-translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: englishText.trim()
        }),
      });

      const data: TranslationResponse = await response.json();
      setLastTranslation(data);

      if (data.success && data.translations) {
        setFrenchText(data.translations.french);
        setItalianText(data.translations.italian);
      } else {
        alert(`Translation failed: ${data.error}`);
      }
    } catch (error) {
      console.error('Translation error:', error);
      alert('Failed to translate text. Please try again.');
    } finally {
      setIsTranslating(false);
    }
  };

  const handleClear = () => {
    setEnglishText('');
    setFrenchText('');
    setItalianText('');
    setLastTranslation(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Languages className="h-8 w-8 text-blue-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">AWS Translation Test</h1>
          </div>
          <p className="text-lg text-gray-600">
            Test AWS Translate service with English to French and Italian translations
          </p>
        </div>

        {/* Service Status */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Service Status</h2>
            {isCheckingService ? (
              <div className="flex items-center text-gray-600">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Checking AWS Translate service...
              </div>
            ) : serviceStatus ? (
              <div className="space-y-2">
                <div className="flex items-center">
                  {serviceStatus.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span className={`font-medium ${serviceStatus.success ? 'text-green-700' : 'text-red-700'}`}>
                    {serviceStatus.success ? 'Service Ready' : 'Service Error'}
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {serviceStatus.message || serviceStatus.error}
                </p>
                {serviceStatus.details && (
                  <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-sm">
                    <p className="font-medium text-red-800 mb-1">Error Details:</p>
                    <p className="text-red-700 font-mono text-xs break-all">{serviceStatus.details}</p>
                  </div>
                )}
                {serviceStatus.rawError && (
                  <div className="mt-2 p-3 bg-red-100 border border-red-300 rounded text-sm">
                    <p className="font-medium text-red-800 mb-1">Raw Error (Development):</p>
                    <div className="text-red-700 font-mono text-xs space-y-1">
                      <p><strong>Name:</strong> {serviceStatus.rawError.name}</p>
                      <p><strong>Message:</strong> {serviceStatus.rawError.message}</p>
                      {serviceStatus.rawError.code && <p><strong>Code:</strong> {serviceStatus.rawError.code}</p>}
                      {serviceStatus.rawError.statusCode && <p><strong>Status Code:</strong> {serviceStatus.rawError.statusCode}</p>}
                    </div>
                  </div>
                )}
                {!serviceStatus.success && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
                    <p className="font-medium text-yellow-800 mb-1">Common Solutions:</p>
                    <ul className="text-yellow-700 text-xs space-y-1 list-disc list-inside">
                      <li>Check if AWS_ACCESS_KEY_ID is set in environment variables</li>
                      <li>Check if AWS_SECRET_ACCESS_KEY is set in environment variables</li>
                      <li>Verify AWS credentials have Translate permissions</li>
                      <li>Ensure AWS_REGION is set (current: {process.env.NEXT_PUBLIC_AWS_REGION || 'not set'})</li>
                      <li>Check if AWS Translate service is available in your region</li>
                    </ul>
                  </div>
                )}
                {serviceStatus.testTranslation && (
                  <div className="text-sm text-gray-500 bg-gray-50 p-2 rounded">
                    Test: "{serviceStatus.testTranslation.original}" → "{serviceStatus.testTranslation.translated}" ({serviceStatus.testTranslation.language})
                  </div>
                )}
              </div>
            ) : null}
            <button
              onClick={checkServiceStatus}
              className="mt-3 text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Refresh Status
            </button>
          </div>
        </div>

        {/* Translation Form */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="space-y-6">
            {/* English Input */}
            <div>
              <label htmlFor="english" className="block text-sm font-medium text-gray-700 mb-2">
                English Text (Input)
              </label>
              <textarea
                id="english"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter English text here..."
                value={englishText}
                onChange={(e) => setEnglishText(e.target.value)}
                disabled={isTranslating}
              />
              <p className="mt-1 text-sm text-gray-500">
                {englishText.length}/5000 characters
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={handleTranslate}
                disabled={isTranslating || !englishText.trim() || !serviceStatus?.success}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isTranslating ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                {isTranslating ? 'Translating...' : 'Translate'}
              </button>
              <button
                onClick={handleClear}
                disabled={isTranslating}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Clear All
              </button>
            </div>

            {/* French Output */}
            <div>
              <label htmlFor="french" className="block text-sm font-medium text-gray-700 mb-2">
                French Translation (Auto-filled)
              </label>
              <textarea
                id="french"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="French translation will appear here..."
                value={frenchText}
                readOnly
              />
            </div>

            {/* Italian Output */}
            <div>
              <label htmlFor="italian" className="block text-sm font-medium text-gray-700 mb-2">
                Italian Translation (Auto-filled)
              </label>
              <textarea
                id="italian"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Italian translation will appear here..."
                value={italianText}
                readOnly
              />
            </div>
          </div>

          {/* Last Translation Result */}
          {lastTranslation && (
            <div className="mt-6 p-4 bg-gray-50 rounded-md">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Last Translation Result</h3>
              <div className={`text-sm ${lastTranslation.success ? 'text-green-700' : 'text-red-700'}`}>
                {lastTranslation.success ? (
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    {lastTranslation.message}
                  </div>
                ) : (
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {lastTranslation.error}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Debug Information */}
        <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Debug Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">Environment Variables Status:</p>
              <ul className="mt-1 space-y-1 text-gray-600">
                <li>• AWS_ACCESS_KEY_ID: {typeof window !== 'undefined' ? 'Check server logs' : 'Server-side only'}</li>
                <li>• AWS_SECRET_ACCESS_KEY: {typeof window !== 'undefined' ? 'Check server logs' : 'Server-side only'}</li>
                <li>• AWS_REGION: {process.env.NEXT_PUBLIC_AWS_REGION || 'Not set (using default: eu-north-1)'}</li>
              </ul>
            </div>
            <div>
              <p className="font-medium text-gray-700">Service Information:</p>
              <ul className="mt-1 space-y-1 text-gray-600">
                <li>• Service: AWS Translate</li>
                <li>• Source Language: English (en)</li>
                <li>• Target Languages: French (fr), Italian (it)</li>
                <li>• Character Limit: 5,000 per request</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-blue-900 mb-2">How to Use</h3>
          <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
            <li>Make sure the AWS Translate service status shows "Service Ready"</li>
            <li>Enter your English text in the first textarea</li>
            <li>Click "Translate" to automatically fill French and Italian translations</li>
            <li>The translations will appear in the read-only fields below</li>
            <li>Use "Clear All" to reset all fields</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
