import mongoose from 'mongoose';

// Define schema for return item
const ReturnItemSchema = new mongoose.Schema({
  productId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  }
});

// Define main return request schema
const ReturnRequestSchema = new mongoose.Schema({
  returnNumber: {
    type: String,
    required: true,
    unique: true
  },
  orderNumber: {
    type: String,
    required: true
  },
  customer: {
    name: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    }
  },
  items: {
    type: [ReturnItemSchema],
    required: true,
    validate: {
      validator: function(items: any[]) {
        return items.length > 0;
      },
      message: 'Return request must contain at least one item'
    }
  },
  reason: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'completed'],
    default: 'pending'
  },
  refundAmount: {
    type: Number,
    required: true
  },
  adminNotes: {
    type: String
  },
  refundedAt: {
    type: Date
  }
}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

// Create a function to generate return numbers
ReturnRequestSchema.statics.generateReturnNumber = async function() {
  const count = await this.countDocuments();
  const returnNumber = `RET-${String(count + 1).padStart(5, '0')}`;
  return returnNumber;
};

// Check if model exists before creating
const ReturnRequest = mongoose.models.ReturnRequest || mongoose.model('ReturnRequest', ReturnRequestSchema);

export default ReturnRequest; 