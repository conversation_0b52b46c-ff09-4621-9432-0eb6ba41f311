import mongoose, { Schema } from 'mongoose';

// Define reward schema
const RewardSchema = new Schema({
  id: { type: Number, required: true },
  name: { type: String, required: true },
  issuedDate: { type: String, required: true }
});

// Define spent reward schema
const SpentRewardSchema = new Schema({
  id: { type: Number, required: true },
  name: { type: String, required: true },
  spentDate: { type: String, required: true },
  amount: { type: Number, required: true }
});

// Define the Customer schema
const CustomerSchema = new Schema(
  {
    email: { type: String, required: true, unique: true, lowercase: true },
    phone: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    address: {
      street: { type: String, required: true },
      country: { type: String, required: true },
      province: { type: String, required: true },
      city: { type: String, required: true },
      postalCode: { type: String, required: true },
    },
    orders: [{ type: Schema.Types.ObjectId, ref: 'Order' }],
    wishlist: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
    currentRewards: { type: [RewardSchema], default: [] },
    spentRewards: { type: [SpentRewardSchema], default: [] },
    totalSpent: { type: Number, default: 0 }
  },
  { timestamps: true }
);

// Check if model exists before creating (to avoid model overwrite during hot reloading)
export const Customer = mongoose.models.Customer || mongoose.model('Customer', CustomerSchema);
export default Customer; 