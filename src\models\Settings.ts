import mongoose, { Schema } from 'mongoose';

// Define store settings schema
const StoreSettingsSchema = new Schema({
  storeName: { type: String, default: '' },
  storeEmail: { type: String, default: '' },
  storePhone: { type: String, default: '' },
  storeCurrency: { type: String, default: 'USD' },
  storeTimeZone: { type: String, default: 'UTC' },
  storeDefaultLanguage: { type: String, default: 'en' },
});

// Define payment settings schema
const PaymentSettingsSchema = new Schema({
  stripeEnabled: { type: Boolean, default: false },
  stripePublicKey: { type: String, default: '' },
  stripeSecretKey: { type: String, default: '' },
  paypalEnabled: { type: Boolean, default: false },
  paypalClientId: { type: String, default: '' },
  paypalSecretKey: { type: String, default: '' },
  cashOnDeliveryEnabled: { type: Boolean, default: true },
});

// Define shipping settings schema
const ShippingSettingsSchema = new Schema({
  defaultShippingMethod: { type: String, default: 'flat_rate' },
  flatRateEnabled: { type: Boolean, default: true },
  flatRateAmount: { type: Number, default: 10 },
  freeShippingEnabled: { type: Boolean, default: false },
  freeShippingMinimumOrder: { type: Number, default: 100 },
  customShippingRatesEnabled: { type: Boolean, default: false },
});

// Define the main settings schema that includes all the above schemas
const SettingsSchema = new Schema(
  {
    store: StoreSettingsSchema,
    payment: PaymentSettingsSchema,
    shipping: ShippingSettingsSchema,
  },
  { timestamps: true }
);

// Use a singleton pattern to ensure we always have one settings document
SettingsSchema.statics.getSingleton = async function() {
  const count = await this.countDocuments();
  
  if (count === 0) {
    // Create default settings if none exist
    return await this.create({});
  }
  
  // Return the first settings document
  return await this.findOne();
};

// Check if model exists before creating to prevent model overwrite during hot reloading
export const Settings = mongoose.models.Settings || mongoose.model('Settings', SettingsSchema); 