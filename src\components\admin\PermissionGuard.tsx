'use client';

import React from 'react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useRouter } from 'next/navigation';

interface PermissionGuardProps {
  resource: string;
  action: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * A component that guards access to UI elements or routes based on user permissions
 * 
 * @param resource - The resource name (products, orders, etc.)
 * @param action - The action name (view, create, update, delete)
 * @param children - The content to render if user has permission
 * @param fallback - Optional content to render if user doesn't have permission
 * @param redirectTo - Optional URL to redirect to if user doesn't have permission
 */
export default function PermissionGuard({
  resource,
  action,
  children,
  fallback = null,
  redirectTo,
}: PermissionGuardProps) {
  const { hasPermission } = useAdminAuth();
  const router = useRouter();
  
  // Check if user has permission for this resource and action
  const userHasPermission = hasPermission(resource, action);
  
  // If no permission and redirect path provided, redirect to that path
  React.useEffect(() => {
    if (!userHasPermission && redirectTo) {
      router.push(redirectTo);
    }
  }, [userHasPermission, redirectTo, router]);

  // Return children if user has permission, otherwise return fallback
  return userHasPermission ? <>{children}</> : <>{fallback}</>;
} 