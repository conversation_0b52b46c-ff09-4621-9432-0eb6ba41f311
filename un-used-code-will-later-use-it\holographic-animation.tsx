import React from 'react';

// Declare global THREE type to resolve TypeScript errors
declare global {
  interface Window {
    THREE: {
      Scene: new () => any;
      PerspectiveCamera: new (fov: number, aspect: number, near: number, far: number) => any;
      WebGLRenderer: new (options?: {
        canvas?: HTMLCanvasElement;
        antialias?: boolean;
        alpha?: boolean;
      }) => any;
      PlaneGeometry: new (width: number, height: number, widthSegments?: number, heightSegments?: number) => any;
      ShaderMaterial: new (options?: {
        uniforms?: any;
        vertexShader?: string;
        fragmentShader?: string;
      }) => any;
      Mesh: new (geometry: any, material: any) => any;
      Vector2: new (x?: number, y?: number) => any;
      Clock: new () => any;
    };
  }
}

export function initHolographicAnimation() {
  const { THREE } = window;

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({ 
    canvas: document.getElementById('holographic-canvas') as HTMLCanvasElement, 
    antialias: true,
    alpha: true 
  });
  renderer.setSize(window.innerWidth, window.innerHeight);

  // Noise functions
  function random(s: number): number { 
    return Math.sin(s * 12.9898) * 43758.5453123;
  }

  function fBm(x: number, y: number, z: number, octaves: number = 4, persistence: number = 0.5, lacunarity: number = 2.0): number { 
    let value = 0.0;
    let amplitude = 1.0;
    let frequency = 1.0;
    let maxValue = 0.0;

    for (let i = 0; i < octaves; i++) {
      const n = random(x * frequency + y + z);
      value += n * amplitude;
      maxValue += amplitude;
      amplitude *= persistence;
      frequency *= lacunarity;
    }

    return value / maxValue;
  }

  // Holographic shader
  const holographicMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) }
    },
    vertexShader: `
      varying vec2 vUv;
      uniform float time;
      
      void main() {
        vUv = uv;
        vec3 newPosition = position;
        float noiseValue = sin(position.x * 10.0 + time * 0.5) * 0.05;
        newPosition.z += noiseValue;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      varying vec2 vUv;
      
      void main() {
        vec3 color = vec3(0.2, 0.7, 1.0);
        float noise = sin(vUv.x * 20.0 + time * 0.2) * 0.1;
        float alpha = 0.6 + noise;
        gl_FragColor = vec4(color, alpha);
      }
    `,
    transparent: true
  });

  const geometry = new THREE.PlaneGeometry(10, 10, 32, 32);
  const hologram = new THREE.Mesh(geometry, holographicMaterial);
  scene.add(hologram);

  camera.position.z = 5;

  const clock = new THREE.Clock();

  function animateHolographic() {
    requestAnimationFrame(animateHolographic);

    const elapsedTime = clock.getElapsedTime();
    (holographicMaterial.uniforms.time as { value: number }).value = elapsedTime;

    renderer.render(scene, camera);
  }

  function onHolographicResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
    (holographicMaterial.uniforms.resolution as { value: any }).value.set(window.innerWidth, window.innerHeight);
  }

  window.addEventListener('resize', onHolographicResize);

  animateHolographic();

  // Cleanup function
  return () => {
    window.removeEventListener('resize', onHolographicResize);
    geometry.dispose();
    holographicMaterial.dispose();
    renderer.dispose();
  };
} 