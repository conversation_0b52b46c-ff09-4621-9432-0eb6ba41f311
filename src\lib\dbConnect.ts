import mongoose from 'mongoose';

// Define environment variables type
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      MONGODB_URI: string;
    }
  }
}

// Database connection state
interface ConnectionState {
  isConnected: number;
}

const connection: ConnectionState = { isConnected: 0 };

/**
 * Connect to MongoDB
 */
async function dbConnect() {
  // If already connected, return
  if (connection.isConnected) {
    return;
  }

  // Check for environment variable
  if (!process.env.MONGODB_URI) {
    throw new Error(
      'Please define the MONGODB_URI environment variable inside .env.local'
    );
  }

  try {
    // Connect to database
    const db = await mongoose.connect(process.env.MONGODB_URI);

    // Set connection state using readyState
    // 0: disconnected, 1: connected, 2: connecting, 3: disconnecting
    connection.isConnected = db.connection.readyState;
    
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    throw error;
  }
}

export default dbConnect; 