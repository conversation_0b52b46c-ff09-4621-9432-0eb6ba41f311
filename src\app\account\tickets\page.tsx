'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { 
  MessageSquare, 
  Clock, 
  ChevronRight, 
  Send, 
  RefreshCw,
  Tag,
  CalendarIcon
} from 'lucide-react';

interface TicketMessage {
  sender: 'customer' | 'admin';
  message: string;
  timestamp: string;
  attachmentUrl?: string;
}

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'waiting-for-customer' | 'resolved' | 'closed';
  messages: TicketMessage[];
  createdAt: string;
  updatedAt: string;
}

export default function CustomerTicketsPage() {
  const { customer, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [replyMessage, setReplyMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isReplying, setIsReplying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch customer tickets when authenticated
  useEffect(() => {
    if (isAuthenticated && customer) {
      fetchTickets();
    } else if (!authLoading && !isAuthenticated) {
      // Redirect to login if not authenticated
      router.push('/login?redirect=/account/tickets');
    }
  }, [isAuthenticated, customer, authLoading, router]);
  
  const fetchTickets = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch tickets for the logged-in customer
      const response = await fetch(`/api/support-tickets?email=${customer?.email}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch tickets');
      }
      
      const data = await response.json();
      setTickets(data);
      
      // Auto-select the first ticket if none is selected
      if (data.length > 0 && !selectedTicket) {
        setSelectedTicket(data[0]);
      }
    } catch (err: any) {
      setError('Error loading your tickets. Please try again.');
      console.error('Error fetching tickets:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSelectTicket = (ticket: Ticket) => {
    setSelectedTicket(ticket);
  };
  
  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTicket || !replyMessage.trim()) return;
    
    setIsReplying(true);
    
    try {
      const response = await fetch(`/api/support-tickets/${selectedTicket._id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sender: 'customer',
          message: replyMessage
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to send reply');
      }
      
      const data = await response.json();
      
      // Update tickets list with the updated ticket
      setTickets(tickets.map(ticket => 
        ticket._id === selectedTicket._id ? data.ticket : ticket
      ));
      
      // Update selected ticket
      setSelectedTicket(data.ticket);
      
      // Clear reply message
      setReplyMessage('');
    } catch (err) {
      console.error('Error sending reply:', err);
      alert('Failed to send reply. Please try again.');
    } finally {
      setIsReplying(false);
    }
  };
  
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'open':
        return 'Open';
      case 'in-progress':
        return 'In Progress';
      case 'waiting-for-customer':
        return 'Waiting for Your Reply';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return status;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'waiting-for-customer':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Redirect to login if not authenticated and not loading
  if (!authLoading && !isAuthenticated) {
    return null; // Router will handle the redirect
  }
  
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Support Tickets</h1>
        <div className="flex space-x-4">
          <button
            onClick={fetchTickets}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => router.push('/customer-support')}
            className="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            <MessageSquare size={16} className="mr-2" />
            New Ticket
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          {error}
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
        </div>
      ) : tickets.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <MessageSquare size={40} className="mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Support Tickets Found</h2>
          <p className="text-gray-600 mb-4">You haven't submitted any support tickets yet.</p>
          <button
            onClick={() => router.push('/customer-support')}
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            <MessageSquare size={16} className="mr-2" />
            Create New Ticket
          </button>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row gap-6">
          {/* Tickets List */}
          <div className="w-full md:w-1/3">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h2 className="font-semibold text-gray-700">Your Tickets</h2>
              </div>
              <div className="overflow-y-auto max-h-[600px]">
                {tickets.map((ticket) => (
                  <div
                    key={ticket._id}
                    className={`border-b border-gray-200 p-4 cursor-pointer hover:bg-gray-50 ${
                      selectedTicket?._id === ticket._id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleSelectTicket(ticket)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">{ticket.subject}</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {ticket.ticketNumber} ・ {formatDate(ticket.createdAt)}
                        </p>
                      </div>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs ${getStatusColor(ticket.status)}`}>
                        {getStatusLabel(ticket.status)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Ticket Details */}
          {selectedTicket && (
            <div className="w-full md:w-2/3">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h2 className="font-semibold text-xl text-gray-800">{selectedTicket.subject}</h2>
                  <div className="flex flex-wrap items-center gap-3 mt-2">
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${getStatusColor(selectedTicket.status)}`}>
                      {getStatusLabel(selectedTicket.status)}
                    </span>
                    <span className="text-sm text-gray-500 flex items-center">
                      <Tag size={14} className="mr-1" />
                      {selectedTicket.category}
                    </span>
                    <span className="text-sm text-gray-500 flex items-center">
                      <CalendarIcon size={14} className="mr-1" />
                      {formatDate(selectedTicket.createdAt)}
                    </span>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="mb-6">
                    <h3 className="font-medium text-gray-700 mb-2">Ticket Description</h3>
                    <div className="bg-gray-50 p-4 rounded-md">
                      {selectedTicket.description}
                    </div>
                  </div>
                  
                  {/* Conversation Thread */}
                  <div className="mb-6">
                    <h3 className="font-medium text-gray-700 mb-3">Conversation</h3>
                    <div className="space-y-4 max-h-80 overflow-y-auto p-1">
                      {selectedTicket.messages && selectedTicket.messages.map((message, index) => (
                        <div 
                          key={index} 
                          className={`flex ${message.sender === 'customer' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div 
                            className={`max-w-[85%] rounded-lg p-3 ${
                              message.sender === 'customer' 
                                ? 'bg-blue-500 text-white' 
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            <div className="text-sm mb-1">
                              {message.sender === 'customer' ? 'You' : 'Support Team'}
                            </div>
                            <div className="whitespace-pre-wrap">{message.message}</div>
                            <div className="text-xs mt-1 opacity-75">
                              {formatDate(message.timestamp)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Reply Form */}
                  {['open', 'in-progress', 'waiting-for-customer'].includes(selectedTicket.status) && (
                    <form onSubmit={handleReplySubmit}>
                      <div className="mb-4">
                        <label htmlFor="reply" className="block text-sm font-medium text-gray-700 mb-1">
                          Your Reply
                        </label>
                        <textarea
                          id="reply"
                          rows={4}
                          className="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Type your message here..."
                          value={replyMessage}
                          onChange={(e) => setReplyMessage(e.target.value)}
                          required
                        ></textarea>
                      </div>
                      <button
                        type="submit"
                        disabled={isReplying}
                        className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                      >
                        {isReplying ? (
                          <>
                            <Clock size={18} className="animate-spin mr-2" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send size={18} className="mr-2" />
                            Send Reply
                          </>
                        )}
                      </button>
                    </form>
                  )}
                  
                  {/* Case Closed Message */}
                  {['resolved', 'closed'].includes(selectedTicket.status) && (
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-4 text-center">
                      <p className="text-gray-700">This support case has been {selectedTicket.status}.</p>
                      <p className="text-gray-500 text-sm mt-1">
                        If you need further assistance, please create a new support ticket.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 