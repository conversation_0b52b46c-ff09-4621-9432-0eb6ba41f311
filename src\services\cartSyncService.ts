import { CartItem } from '@/store/useCartStore';

// Helper function to generate a unique session ID if not exists
const getSessionId = (): string => {
  // Check if we already have a sessionId in localStorage
  let sessionId = localStorage.getItem('cart_session_id');
  
  // If not, generate a new one
  if (!sessionId) {
    sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 15);
    localStorage.setItem('cart_session_id', sessionId);
  }
  
  return sessionId;
};

// Helper to get customer info from localStorage or cookies if available
const getCustomerInfo = () => {
  let customerInfo: any = {};
  
  // Try to get from localStorage
  const storedInfo = localStorage.getItem('customer_info');
  if (storedInfo) {
    try {
      customerInfo = JSON.parse(storedInfo);
    } catch (e) {
      console.error('Error parsing customer info', e);
    }
  }
  
  return customerInfo;
};

// Save cart to MongoDB
export const saveCartToDatabase = async (
  items: CartItem[],
  customerEmail?: string,
  customerName?: string
): Promise<boolean> => {
  if (!items.length) {
    // Don't save empty carts
    console.log('[Cart Sync] Not saving empty cart');
    return false;
  }
  
  try {
    console.log('[Cart Sync] Starting cart sync process');
    const sessionId = getSessionId();
    console.log('[Cart Sync] Session ID:', sessionId);
    
    // Get any stored customer info
    const customerInfo = getCustomerInfo();
    
    // Prepare cart data
    const cartData = {
      sessionId,
      customerInfo: {
        email: customerEmail || customerInfo.email || null,
        name: customerName || customerInfo.name || null,
        phone: customerInfo.phone || null
      },
      items: items.map(item => ({
        productId: item.product._id,
        name: item.product.name,
        price: item.product.price,
        quantity: item.quantity,
        imageUrl: item.product.imageUrl
      })),
      subtotal: items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0),
      userAgent: navigator.userAgent,
      referrer: document.referrer || null
    };
    
    console.log('[Cart Sync] Cart data prepared:', cartData);
    
    // Send to API
    console.log('[Cart Sync] Sending data to API...');
    const response = await fetch('/api/abandoned-carts/sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(cartData)
    });
    
    const result = await response.json();
    console.log('[Cart Sync] API response:', result);
    
    return result.success;
  } catch (error) {
    console.error('[Cart Sync] Error saving cart to database:', error);
    return false;
  }
};

// Save customer information for later use
export const saveCustomerInfo = (email: string, name?: string, phone?: string) => {
  const customerInfo = {
    email,
    name: name || '',
    phone: phone || ''
  };
  
  localStorage.setItem('customer_info', JSON.stringify(customerInfo));
  
  // After saving, also sync the cart with this new information
  const storedCart = localStorage.getItem('cart-storage');
  if (storedCart) {
    try {
      const cartData = JSON.parse(storedCart);
      if (cartData.state && cartData.state.items && cartData.state.items.length) {
        saveCartToDatabase(cartData.state.items, email, name);
      }
    } catch (e) {
      console.error('Error parsing stored cart', e);
    }
  }
};

// Mark cart as recovered (after successful checkout)
export const markCartAsRecovered = async (orderId: string): Promise<boolean> => {
  try {
    const sessionId = getSessionId();
    
    // Don't proceed if we don't have a session ID
    if (!sessionId) {
      return false;
    }
    
    const response = await fetch('/api/abandoned-carts/recover', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        orderId
      })
    });
    
    const result = await response.json();
    
    // If successful, also clear the cart session
    if (result.success) {
      localStorage.removeItem('cart_session_id');
    }
    
    return result.success;
  } catch (error) {
    console.error('Error marking cart as recovered:', error);
    return false;
  }
}; 