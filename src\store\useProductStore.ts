import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Product {
  // We'll keep the exact same product interface as used in the shop
  _id: string;
  name: string;
  price: number;
  images: string[];
  videos?: {
    url: string;
    thumbnail?: string;
  }[];
  [key: string]: any; // To ensure we keep all existing product fields
}

interface ProductStore {
  products: Product[];
  lastSyncCheck: number | null;
  isBackgroundSync: boolean;
  setProducts: (products: Product[], isSilentUpdate?: boolean) => void;
  setProductVideos: (productId: string, videos: { url: string; thumbnail?: string }[], isSilentUpdate?: boolean) => void;
  setBackgroundSync: (isSync: boolean) => void;
  clearProducts: () => void;
}

// Background sync interval - 5 minutes
const SYNC_INTERVAL = 5 * 60 * 1000;

export const useProductStore = create<ProductStore>()(
  persist(
    (set, get) => ({
      products: [],
      lastSyncCheck: null,
      isBackgroundSync: false,

      setProducts: (products, isSilentUpdate = false) => {
        // Only update if there are actual changes
        const currentProducts = get().products;
        const hasChanges = JSON.stringify(products) !== JSON.stringify(currentProducts);
        
        if (hasChanges || !currentProducts.length) {
          set({ 
            products,
            lastSyncCheck: Date.now(),
          });
          
          if (!isSilentUpdate) {
            console.log('Products updated with new data');
          }
        }
      },

      setProductVideos: (productId, videos, isSilentUpdate = false) => {
        const products = get().products.map(product => 
          product._id === productId 
            ? { ...product, videos }
            : product
        );

        // Only update if there are actual changes
        const currentProducts = get().products;
        const hasChanges = JSON.stringify(products) !== JSON.stringify(currentProducts);
        
        if (hasChanges) {
          set({ 
            products,
            lastSyncCheck: Date.now()
          });
          
          if (!isSilentUpdate) {
            console.log('Product videos updated');
          }
        }
      },

      setBackgroundSync: (isSync) => set({ isBackgroundSync: isSync }),

      clearProducts: () => set({ 
        products: [], 
        lastSyncCheck: null,
        isBackgroundSync: false
      }),
    }),
    {
      name: 'product-store',
      version: 1, // Add version for future migrations if needed
    }
  )
); 