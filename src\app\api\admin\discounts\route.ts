import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Discount from '@/models/Discount';
import { withAuth } from '@/lib/auth';

// GET - retrieve all discounts
export async function GET(req: NextRequest) {
  try {
    // Connect to database
    await dbConnect();
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const searchTerm = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';

    // Build query
    let query: any = {};
    
    if (searchTerm) {
      query.$or = [
        { code: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    const discounts = await Discount.find(query).sort({ createdAt: -1 });
    
    return NextResponse.json(discounts);
  } catch (error) {
    console.error('Error fetching discounts:', error);
    return NextResponse.json({ error: 'Failed to fetch discounts' }, { status: 500 });
  }
}

// POST - create a new discount
export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await dbConnect();
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.code || !data.type || data.value === undefined || !data.startDate || !data.endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if code already exists
    const existingDiscount = await Discount.findOne({ code: data.code.toUpperCase() });
    if (existingDiscount) {
      return NextResponse.json(
        { error: 'Discount code already exists' },
        { status: 400 }
      );
    }
    
    // Create new discount
    const discount = await Discount.create({
      ...data,
      code: data.code.toUpperCase(),
      usedCount: 0
    });
    
    return NextResponse.json(discount, { status: 201 });
  } catch (error) {
    console.error('Error creating discount:', error);
    return NextResponse.json({ error: 'Failed to create discount' }, { status: 500 });
  }
} 