import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Category } from '@/models/Category';
import mongoose from 'mongoose';

// PUT - Update a subcategory
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; subId: string } }
) {
  try {
    const { id, subId } = params;
    const { name } = await request.json();
    
    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(subId)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }
    
    if (!name || typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json(
        { error: 'Subcategory name is required' },
        { status: 400 }
      );
    }
    
    await connectToDatabase();
    
    // Check if category exists
    const category = await Category.findById(id);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }
    
    // Check if subcategory exists
    const subcategory = category.subcategories.find(
      (sub: any) => sub._id.toString() === subId
    );
    
    if (!subcategory) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      );
    }
    
    // Check if the name is already in use by another subcategory in this category
    const nameConflict = category.subcategories.some(
      (sub: any) => sub._id.toString() !== subId && sub.name.toLowerCase() === name.toLowerCase()
    );
    
    if (nameConflict) {
      return NextResponse.json(
        { error: 'A subcategory with this name already exists in this category' },
        { status: 400 }
      );
    }
    
    // Update the subcategory
    const updatedCategory = await Category.findOneAndUpdate(
      { _id: id, 'subcategories._id': subId },
      { $set: { 'subcategories.$.name': name } },
      { new: true, runValidators: true }
    );
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error updating subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to update subcategory' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a subcategory
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; subId: string } }
) {
  try {
    const { id, subId } = params;
    
    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(subId)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }
    
    await connectToDatabase();
    
    // Check if category exists
    const category = await Category.findById(id);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }
    
    // Check if subcategory exists
    const subcategory = category.subcategories.find(
      (sub: any) => sub._id.toString() === subId
    );
    
    if (!subcategory) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      );
    }
    
    // Remove the subcategory
    const updatedCategory = await Category.findByIdAndUpdate(
      id,
      { $pull: { subcategories: { _id: subId } } },
      { new: true }
    );
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error deleting subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to delete subcategory' },
      { status: 500 }
    );
  }
} 