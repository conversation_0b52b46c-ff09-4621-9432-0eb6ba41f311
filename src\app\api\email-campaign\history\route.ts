import { NextRequest, NextResponse } from 'next/server';
import Campaign from '@/models/Campaign';
import connectDB from '@/lib/mongoose';

export async function GET(request: NextRequest) {
  try {
    // Connect to the database
    await connectDB();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    // Build the query
    const query: any = {};
    if (type) query.type = type;
    if (status) query.status = status;

    // Fetch campaigns from the database
    const campaigns = await Campaign.find(query)
      .sort({ createdAt: -1 }) // Sort by created date, newest first
      .limit(100); // Limit to 100 campaigns

    return NextResponse.json(campaigns);
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
} 