import { NextResponse } from 'next/server';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';

// S3 configuration from environment variables
const s3Config = {
  region: process.env.AWS_REGION || 'eu-north-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
};

// Get S3 client
const getS3Client = () => {
  return new S3Client(s3Config);
};

// Video bucket name from environment variables
const videosBucketName = process.env.S3_VIDEOS_BUCKET || 'videosbucket2025';

/**
 * Get a video file from S3
 * 
 * GET /api/test-s3-video/[key]
 */
export async function GET(
  request: Request,
  { params }: { params: { key: string } }
) {
  try {
    const client = getS3Client();
    
    // Get the file from S3
    const command = new GetObjectCommand({
      Bucket: videosBucketName,
      Key: params.key,
    });
    
    const response = await client.send(command);
    
    // Get the content type from the response or default to video/mp4
    const contentType = response.ContentType || 'video/mp4';
    
    // Get the content length
    const contentLength = response.ContentLength || 0;
    
    // Get the range header from the request
    const range = request.headers.get('range');
    
    if (range) {
      // Handle range request for video streaming
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : contentLength - 1;
      const chunksize = (end - start) + 1;
      
      // Get the video stream for the requested range
      const stream = response.Body as any;
      const chunks: Uint8Array[] = [];
      
      // Skip to the start position
      let bytesRead = 0;
      for await (const chunk of stream) {
        if (bytesRead >= start) {
          chunks.push(chunk);
        }
        bytesRead += chunk.length;
        
        // Stop if we've read enough bytes
        if (bytesRead >= end + 1) break;
      }
      
      const buffer = Buffer.concat(chunks);
      
      // Return partial content with range headers
      return new NextResponse(buffer, {
        status: 206,
        headers: {
          'Content-Type': contentType,
          'Content-Length': chunksize.toString(),
          'Content-Range': `bytes ${start}-${end}/${contentLength}`,
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'public, max-age=31536000',
        },
      });
    } else {
      // Handle full video request
      const chunks: Uint8Array[] = [];
      for await (const chunk of response.Body as any) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Length': contentLength.toString(),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'public, max-age=31536000',
        },
      });
    }
  } catch (error: any) {
    console.error('Error fetching video from S3:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to fetch video from S3'
    }, { status: 500 });
  }
} 