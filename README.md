# Gemstone E-commerce Website

An e-commerce website for selling gemstones, built with Next.js and MongoDB Atlas.

## Features

- Database connection status monitoring
- S3 bucket connection checks for images and videos
- User-friendly error explanations for database and S3 connection issues
- Responsive design with Tailwind CSS

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- MongoDB Atlas account
- AWS account with S3 buckets (for media storage)

### Setup

1. Clone this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a `.env.local` file in the root directory with your connection strings and credentials:
   ```
   # MongoDB connection string
   MONGODB_URI=*************************************************************************************************************
   
   # Next.js environment variables
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   NEXT_PUBLIC_DEV_MODE=false
   
   # AWS S3 Configuration
   AWS_REGION=us-east-1
   AWS_ACCESS_KEY_ID=your_access_key_id
   AWS_SECRET_ACCESS_KEY=your_secret_access_key
   S3_IMAGES_BUCKET=your-images-bucket-name
   S3_VIDEOS_BUCKET=your-videos-bucket-name
   ```
   Replace with your actual MongoDB Atlas connection string and AWS credentials.

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

6. Use the Developer Mode toggle or press `Ctrl+Shift+D` to enable developer mode.

## Project Structure

- `src/lib/mongodb.ts` - MongoDB connection utility
- `src/lib/s3.ts` - AWS S3 connection utility
- `src/components/ConnectionStatus.tsx` - Database connection status component
- `src/components/S3ConnectionStatus.tsx` - S3 bucket connection status component
- `src/app/api/check-connection/route.ts` - API route to check database connection
- `src/app/api/check-s3-connections/route.ts` - API route to check S3 bucket connections
- `src/app/page.tsx` - Home page
- `src/app/cs/page.tsx` - Connection status page

## Connection Status Page (/cs)

The application includes a dedicated connection status page that shows:
- MongoDB connection status
- S3 bucket connection status for images and videos

The connection status components show:
- Loading state while checking the connections
- Success message when connected to MongoDB Atlas and S3 buckets
- Detailed error information when connections fail, including:
  - User-friendly error explanation
  - Technical details for debugging

### MongoDB Connection Status
- Shows database name, host, port, and connection state
- Lists collections in the database
- Provides detailed error messages with user-friendly explanations

### S3 Bucket Connection Status
- Checks connections to both images and videos buckets
- Shows number of objects in each bucket
- Displays sample filenames from each bucket
- Provides detailed error messages with user-friendly explanations

## Developer Mode

The application includes a developer mode that can be toggled in two ways:
1. Using the Developer Mode toggle in the UI
2. Using the keyboard shortcut `Ctrl+Shift+D`

When developer mode is enabled, the application provides:
- Access to the connection status page via a link
- Database tools for creating collections
- Connection guide with setup instructions

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

Gmail smtp guidelines..................................................

Here's how to find each value for your .env.local file:
GMAIL_USER
This is your Gmail email address (like <EMAIL>)
Simply use your own Gmail address here
GMAIL_APP_PASSWORD
This requires a few steps:
Go to your Google Account at https://myaccount.google.com/
Select "Security" in the left sidebar
Under "Signing in to Google," find "2-Step Verification" and click on it
If you haven't set up 2-Step Verification yet, you'll need to do that first
Scroll down to find "App passwords" and click on it
Select "Mail" as the app and "Other" as the device
Enter a name like "Afghan Gems Website"
Click "Create"
Google will generate a 16-character password (no spaces) - copy this value
EMAIL_FROM
This is the display name + email that recipients will see
Format: "Display Name <<EMAIL>>"
Example: "Afghan Gems <<EMAIL>>"
Use your business name and the same email as GMAIL_USER
After updating these values in .env.local, restart your application for the changes to take effect.

https://myaccount.google.com/apppasswords