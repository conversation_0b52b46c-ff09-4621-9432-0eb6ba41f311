import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import ReturnRequest from '@/models/ReturnRequest';

/**
 * Create a new return request
 * 
 * POST /api/returns
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const data = await request.json();
    
    // Validate required fields
    if (!data.orderNumber || !data.reason || !data.items || data.items.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: orderNumber, reason, and at least one item'
      }, { status: 400 });
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Generate a return number
    const count = await ReturnRequest.countDocuments();
    const returnNumber = `RET-${String(count + 1).padStart(5, '0')}`;
    
    // Create the return request
    const returnRequest = new ReturnRequest({
      returnNumber,
      orderNumber: data.orderNumber,
      customer: {
        name: data.customer?.name || 'Customer',
        email: data.customer?.email || '<EMAIL>'
      },
      items: data.items.map((item: any) => ({
        productId: item.productId,
        name: item.name,
        price: item.price,
        quantity: item.quantity
      })),
      reason: data.reason,
      status: 'pending',
      refundAmount: data.refundAmount || data.items.reduce((sum: number, item: any) => 
        sum + (item.price * item.quantity), 0)
    });
    
    // Save to database
    await returnRequest.save();
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Return request created successfully',
      returnRequest
    });
  } catch (error: any) {
    console.error('Error creating return request:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create return request'
    }, { status: 500 });
  }
}

/**
 * Get all return requests
 * 
 * GET /api/returns
 */
export async function GET(request: Request) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const orderNumber = searchParams.get('orderNumber');
    const query: any = {};
    
    // Apply filters if provided
    if (status && status !== 'all') {
      query.status = status;
    }
    
    if (orderNumber) {
      query.orderNumber = orderNumber;
    }
    
    // Get all return requests
    const returnRequests = await ReturnRequest.find(query).sort({ createdAt: -1 });
    
    // Return success response
    return NextResponse.json({
      success: true,
      count: returnRequests.length,
      returns: returnRequests
    });
  } catch (error: any) {
    console.error('Error retrieving return requests:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to retrieve return requests'
    }, { status: 500 });
  }
} 