import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Customer } from '@/models/Customer';

export async function GET(req: Request) {
  try {
    await dbConnect();
    
    // Get the search parameter from the URL
    const url = new URL(req.url);
    const email = url.searchParams.get('email');
    const phone = url.searchParams.get('phone');
    
    if (!email && !phone) {
      return NextResponse.json(
        { error: 'Either email or phone is required' },
        { status: 400 }
      );
    }
    
    // Find customer by email or phone
    const query = email 
      ? { email: email.toLowerCase() } 
      : { phone };
    
    const customer = await Customer.findOne(query).lean();
    
    if (!customer) {
      return NextResponse.json(
        { found: false },
        { status: 200 }
      );
    }
    
    return NextResponse.json({
      found: true,
      customer: {
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        address: customer.address
      }
    });
  } catch (error) {
    console.error('Error looking up customer:', error);
    return NextResponse.json(
      { error: 'Failed to lookup customer' },
      { status: 500 }
    );
  }
} 