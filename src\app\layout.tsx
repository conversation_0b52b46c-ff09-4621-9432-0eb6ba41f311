import './globals.css';
import { <PERSON>ada<PERSON>, Viewport } from 'next';
import { <PERSON><PERSON>rencyProvider } from '@/contexts/CurrencyContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { AdminAuthProvider } from '@/contexts/AdminAuthContext';
import { Toaster } from 'sonner';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import { Kameron, Roboto } from "next/font/google";
import localFont from 'next/font/local';
import Header from "@/components/header/Header";
import CartDrawer from "@/components/CartDrawer";
import React from 'react';
import NotificationWrapper from '@/components/NotificationWrapper';
import Script from 'next/script';
import Footer from "@/components/footer/Footer";
import PageTransition from '@/components/PageTransition';

// Optimized Google fonts with font-display: swap for better LCP
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap', // Prevents font from blocking render
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap', // Prevents font from blocking render
});

const kameron = Kameron({
  weight: ["400", "500"],
  variable: "--font-kameron",
  subsets: ["latin"],
  display: 'swap', // Prevents font from blocking render
});

const roboto = Roboto({
  weight: ["400", "500"],
  variable: "--font-roboto",
  subsets: ["latin"],
  display: 'swap', // Prevents font from blocking render
});

// Load Segoe UI font from downloaded fonts - already optimized with swap
const segoeUIFont = localFont({
  src: '../../downloaded fonts/segoe-ui/segoeuithis.ttf',
  variable: '--font-segoe-ui',
  display: 'swap',
  weight: '400'
});

// Load Dosis font from downloaded fonts - already optimized with swap
const dosisFont = localFont({
  src: '../../downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf',
  variable: '--font-dosis',
  display: 'swap',
  weight: '400 700'
});

export const metadata: Metadata = {
  title: 'Afghan International Gems - Fine Gems & Jewelry',
  description: 'Discover exquisite gemstones and jewelry from Afghan International Gems, a premier source for rare and beautiful gems from around the world.',
  icons: {
    icon: [
      {
        url: '/images/fav icon/fav-icon.png',
        href: '/images/fav icon/fav-icon.png',
      },
    ],
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Prepare Firebase config for the service worker
  const firebaseEnvScript = `
    window.ENV_FIREBASE_API_KEY = "${process.env.NEXT_PUBLIC_FIREBASE_API_KEY}";
    window.ENV_FIREBASE_AUTH_DOMAIN = "${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}";
    window.ENV_FIREBASE_PROJECT_ID = "${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}";
    window.ENV_FIREBASE_STORAGE_BUCKET = "${process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET}";
    window.ENV_FIREBASE_MESSAGING_SENDER_ID = "${process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}";
    window.ENV_FIREBASE_APP_ID = "${process.env.NEXT_PUBLIC_FIREBASE_APP_ID}";
    window.ENV_FIREBASE_VAPID_KEY = "${process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY}";
  `;

  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable} ${kameron.variable} ${roboto.variable} ${segoeUIFont.variable} ${dosisFont.variable}`}>
      <head>
        <script dangerouslySetInnerHTML={{ __html: firebaseEnvScript }} />
        <Script src="/firebase-sw-loader.js" strategy="beforeInteractive" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        
        {/* PERFORMANCE OPTIMIZATION: Critical font preloading */}
        <link 
          rel="preload" 
          href="/downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf" 
          as="font" 
          type="font/truetype" 
          crossOrigin="anonymous" 
        />
        <link 
          rel="preload" 
          href="/downloaded fonts/segoe-ui/segoeuithis.ttf" 
          as="font" 
          type="font/truetype" 
          crossOrigin="anonymous" 
        />
        
        {/* PERFORMANCE OPTIMIZATION: DNS prefetch for external domains */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        
        {/* Preconnect to resources domains */}
        {process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN && (
          <link rel="preconnect" href={process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN} crossOrigin="anonymous" />
        )}
        {process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN && (
          <link rel="preconnect" href={process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN} crossOrigin="anonymous" />
        )}
        
        {/* PERFORMANCE OPTIMIZATION: Hero videos with fetchPriority high */}
        <link rel="preload" as="video" href="/videos/mobile-hero.mp4" type="video/mp4" media="(max-width: 767px)" fetchPriority="high" />
        <link rel="preload" as="video" href="/videos/desktop-hero.mp4" type="video/mp4" media="(min-width: 768px)" fetchPriority="high" />
        
        {/* PERFORMANCE OPTIMIZATION: Critical LCP images with fetchPriority high */}
        <link rel="preload" as="image" href="/images/Tanzanite.png" fetchPriority="high" />
        <link rel="preload" as="image" href="/images/sphene.png" fetchPriority="high" />
        <link rel="preload" as="image" href="/images/Ruby.png" fetchPriority="high" />
        
        {/* PERFORMANCE OPTIMIZATION: Additional critical homepage images */}
        <link rel="preload" as="image" href="/images/our-gems/collectors-choice2.png" />
        <link rel="preload" as="image" href="/images/our-gems/designers-product.png" />

        {/* Add preload hints for video content */}
        <link
          rel="preload"
          as="fetch"
          crossOrigin="anonymous"
          href="/api/products"
        />
        <link 
          rel="preconnect" 
          href={process.env.NEXT_PUBLIC_CLOUDFRONT_URL || ''} 
          crossOrigin="anonymous"
        />
      </head>
      <body className="antialiased" suppressHydrationWarning={true}>
        <AdminAuthProvider>
          <AuthProvider>
            <CurrencyProvider>
              <LanguageProvider>
                <Header />
                <CartDrawer />
                <NotificationWrapper 
                  vapidKey={process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || ''}
                />
                <PageTransition>
                  {children}
                </PageTransition>
                <Toaster richColors position="top-right" />
                <Footer />
              </LanguageProvider>
            </CurrencyProvider>
          </AuthProvider>
        </AdminAuthProvider>
      </body>
    </html>
  );
}
